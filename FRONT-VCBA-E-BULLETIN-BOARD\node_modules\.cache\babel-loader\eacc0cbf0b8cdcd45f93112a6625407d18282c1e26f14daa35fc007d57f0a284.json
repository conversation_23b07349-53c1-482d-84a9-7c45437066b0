{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M15 3h6v6\",\n  key: \"1q9fwt\"\n}], [\"path\", {\n  d: \"m21 3-7 7\",\n  key: \"1l2asr\"\n}], [\"path\", {\n  d: \"m3 21 7-7\",\n  key: \"tjx5ai\"\n}], [\"path\", {\n  d: \"M9 21H3v-6\",\n  key: \"wtvkvv\"\n}]];\nconst Maximize2 = createLucideIcon(\"maximize-2\", __iconNode);\nexport { __iconNode, Maximize2 as default };\n//# sourceMappingURL=maximize-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}