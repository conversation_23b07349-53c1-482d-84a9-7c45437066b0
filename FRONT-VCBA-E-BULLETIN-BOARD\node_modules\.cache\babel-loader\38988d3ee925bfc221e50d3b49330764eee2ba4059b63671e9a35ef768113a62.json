{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"14\",\n  height: \"20\",\n  x: \"5\",\n  y: \"2\",\n  rx: \"2\",\n  ry: \"2\",\n  key: \"1yt0o3\"\n}], [\"path\", {\n  d: \"M12.667 8 10 12h4l-2.667 4\",\n  key: \"h9lk2d\"\n}]];\nconst SmartphoneCharging = createLucideIcon(\"smartphone-charging\", __iconNode);\nexport { __iconNode, SmartphoneCharging as default };\n//# sourceMappingURL=smartphone-charging.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}