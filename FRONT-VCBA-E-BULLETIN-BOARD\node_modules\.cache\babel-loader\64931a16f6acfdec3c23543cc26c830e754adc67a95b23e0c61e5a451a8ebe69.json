{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 2v6h6\",\n  key: \"18ldww\"\n}], [\"path\", {\n  d: \"M21 12A9 9 0 0 0 6 5.3L3 8\",\n  key: \"1pbrqz\"\n}], [\"path\", {\n  d: \"M21 22v-6h-6\",\n  key: \"usdfbe\"\n}], [\"path\", {\n  d: \"M3 12a9 9 0 0 0 15 6.7l3-2.7\",\n  key: \"1hosoe\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"12\",\n  r: \"1\",\n  key: \"41hilf\"\n}]];\nconst RefreshCcwDot = createLucideIcon(\"refresh-ccw-dot\", __iconNode);\nexport { __iconNode, RefreshCcwDot as default };\n//# sourceMappingURL=refresh-ccw-dot.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}