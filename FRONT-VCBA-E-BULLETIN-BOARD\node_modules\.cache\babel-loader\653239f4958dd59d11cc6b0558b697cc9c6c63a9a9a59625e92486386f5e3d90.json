{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m14.305 19.53.923-.382\",\n  key: \"3m78fa\"\n}], [\"path\", {\n  d: \"m15.228 16.852-.923-.383\",\n  key: \"npixar\"\n}], [\"path\", {\n  d: \"m16.852 15.228-.383-.923\",\n  key: \"5xggr7\"\n}], [\"path\", {\n  d: \"m16.852 20.772-.383.924\",\n  key: \"dpfhf9\"\n}], [\"path\", {\n  d: \"m19.148 15.228.383-.923\",\n  key: \"1reyyz\"\n}], [\"path\", {\n  d: \"m19.53 21.696-.382-.924\",\n  key: \"1goivc\"\n}], [\"path\", {\n  d: \"M2 7.82a15 15 0 0 1 20 0\",\n  key: \"1ovjuk\"\n}], [\"path\", {\n  d: \"m20.772 16.852.924-.383\",\n  key: \"htqkph\"\n}], [\"path\", {\n  d: \"m20.772 19.148.924.383\",\n  key: \"9w9pjp\"\n}], [\"path\", {\n  d: \"M5 11.858a10 10 0 0 1 11.5-1.785\",\n  key: \"3sn16i\"\n}], [\"path\", {\n  d: \"M8.5 15.429a5 5 0 0 1 2.413-1.31\",\n  key: \"1pxovh\"\n}], [\"circle\", {\n  cx: \"18\",\n  cy: \"18\",\n  r: \"3\",\n  key: \"1xkwt0\"\n}]];\nconst WifiCog = createLucideIcon(\"wifi-cog\", __iconNode);\nexport { __iconNode, WifiCog as default };\n//# sourceMappingURL=wifi-cog.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}