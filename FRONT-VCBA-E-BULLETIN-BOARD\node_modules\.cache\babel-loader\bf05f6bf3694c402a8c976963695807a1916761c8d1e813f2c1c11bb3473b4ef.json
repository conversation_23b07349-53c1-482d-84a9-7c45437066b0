{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 14a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2\",\n  key: \"1yyzbs\"\n}], [\"path\", {\n  d: \"M14 4a2 2 0 0 1 2-2\",\n  key: \"1w2hp7\"\n}], [\"path\", {\n  d: \"M16 10a2 2 0 0 1-2-2\",\n  key: \"shjach\"\n}], [\"path\", {\n  d: \"M20 14a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2\",\n  key: \"zfj4xr\"\n}], [\"path\", {\n  d: \"M20 2a2 2 0 0 1 2 2\",\n  key: \"188mtx\"\n}], [\"path\", {\n  d: \"M22 8a2 2 0 0 1-2 2\",\n  key: \"ddf4tu\"\n}], [\"path\", {\n  d: \"m3 7 3 3 3-3\",\n  key: \"x25e72\"\n}], [\"path\", {\n  d: \"M6 10V5a 3 3 0 0 1 3-3h1\",\n  key: \"1ageje\"\n}], [\"rect\", {\n  x: \"2\",\n  y: \"14\",\n  width: \"8\",\n  height: \"8\",\n  rx: \"2\",\n  key: \"4rksxw\"\n}]];\nconst ReplaceAll = createLucideIcon(\"replace-all\", __iconNode);\nexport { __iconNode, ReplaceAll as default };\n//# sourceMappingURL=replace-all.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}