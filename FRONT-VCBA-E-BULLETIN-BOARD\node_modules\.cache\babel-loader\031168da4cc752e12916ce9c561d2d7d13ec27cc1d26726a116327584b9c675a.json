{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 14v2\",\n  key: \"w2a1xv\"\n}], [\"path\", {\n  d: \"M14 20v2\",\n  key: \"1lq872\"\n}], [\"path\", {\n  d: \"M14 2v2\",\n  key: \"6buw04\"\n}], [\"path\", {\n  d: \"M14 8v2\",\n  key: \"i67w9a\"\n}], [\"path\", {\n  d: \"M2 15h8\",\n  key: \"82wtch\"\n}], [\"path\", {\n  d: \"M2 3h6a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H2\",\n  key: \"up0l64\"\n}], [\"path\", {\n  d: \"M2 9h8\",\n  key: \"yelfik\"\n}], [\"path\", {\n  d: \"M22 15h-4\",\n  key: \"1es58f\"\n}], [\"path\", {\n  d: \"M22 3h-2a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h2\",\n  key: \"pdjoqf\"\n}], [\"path\", {\n  d: \"M22 9h-4\",\n  key: \"1luja7\"\n}], [\"path\", {\n  d: \"M5 3v18\",\n  key: \"14hmio\"\n}]];\nconst TableColumnsSplit = createLucideIcon(\"table-columns-split\", __iconNode);\nexport { __iconNode, TableColumnsSplit as default };\n//# sourceMappingURL=table-columns-split.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}