{"ast": null, "code": "var _jsxFileName = \"D:\\\\capstone-e-bulletin-reactjs-proj\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\student\\\\layout\\\\StudentSidebar.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { NavLink, useLocation } from 'react-router-dom';\nimport { BarChart3, Settings, BookOpen, Rss } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst navItems = [{\n  path: '/student/dashboard',\n  label: 'Dashboard',\n  icon: BarChart3,\n  description: 'Overview & Quick Actions'\n}, {\n  path: '/student/newsfeed',\n  label: 'Newsfeed',\n  icon: Rss,\n  description: 'Latest Announcements & Events'\n}, {\n  path: '/student/settings',\n  label: 'Settings',\n  icon: Settings,\n  description: 'Profile & Preferences'\n}];\nconst StudentSidebar = ({\n  isOpen,\n  onToggle\n}) => {\n  _s();\n  const location = useLocation();\n  const isActive = path => {\n    return location.pathname === path;\n  };\n  return /*#__PURE__*/_jsxDEV(\"aside\", {\n    style: {\n      position: 'fixed',\n      left: 0,\n      top: 0,\n      height: '100vh',\n      width: isOpen ? '280px' : '80px',\n      background: 'linear-gradient(180deg, #1e40af 0%, #1e3a8a 100%)',\n      transition: 'width 0.3s ease',\n      zIndex: 1000,\n      boxShadow: '4px 0 20px rgba(0, 0, 0, 0.1)',\n      overflow: 'hidden'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: isOpen ? '1.5rem' : '1rem',\n        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '1rem',\n        minHeight: '80px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"img\", {\n        src: \"/logo/vcba1.png\",\n        alt: \"VCBA Logo\",\n        style: {\n          width: '48px',\n          height: '48px',\n          objectFit: 'contain',\n          flexShrink: 0\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            color: 'white',\n            margin: 0,\n            fontSize: '1.1rem',\n            fontWeight: '700',\n            lineHeight: '1.2'\n          },\n          children: \"Student Portal\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: 'rgba(255, 255, 255, 0.7)',\n            margin: 0,\n            fontSize: '0.75rem',\n            lineHeight: '1.2'\n          },\n          children: \"E-Bulletin Board\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      style: {\n        padding: '2rem 0'\n      },\n      children: navItems.map(item => /*#__PURE__*/_jsxDEV(NavLink, {\n        to: item.path,\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '1rem',\n          padding: isOpen ? '1.5rem 1.5rem' : '1.5rem',\n          color: isActive(item.path) ? '#fbbf24' : 'rgba(255, 255, 255, 0.8)',\n          textDecoration: 'none',\n          background: isActive(item.path) ? 'linear-gradient(90deg, rgba(251, 191, 36, 0.2) 0%, transparent 100%)' : 'transparent',\n          borderRight: isActive(item.path) ? '3px solid #fbbf24' : '3px solid transparent',\n          transition: 'all 0.2s ease',\n          position: 'relative',\n          overflow: 'hidden'\n        },\n        onMouseEnter: e => {\n          if (!isActive(item.path)) {\n            e.currentTarget.style.background = 'rgba(255, 255, 255, 0.05)';\n            e.currentTarget.style.color = 'white';\n          }\n        },\n        onMouseLeave: e => {\n          if (!isActive(item.path)) {\n            e.currentTarget.style.background = 'transparent';\n            e.currentTarget.style.color = 'rgba(255, 255, 255, 0.8)';\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontSize: '1.5rem',\n            flexShrink: 0,\n            width: '24px',\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(item.icon, {\n            size: 20,\n            color: isActive(item.path) ? '#facc15' : 'rgba(255, 255, 255, 0.8)'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 13\n        }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            minWidth: 0\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: isActive(item.path) ? '600' : '500',\n              fontSize: '1rem',\n              marginBottom: '0.25rem'\n            },\n            children: item.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '0.75rem',\n              color: isActive(item.path) ? 'rgba(251, 191, 36, 0.8)' : 'rgba(255, 255, 255, 0.6)',\n              lineHeight: '1.2'\n            },\n            children: item.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 15\n        }, this)]\n      }, item.path, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), isOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        bottom: '2rem',\n        left: '1.5rem',\n        right: '1.5rem',\n        padding: '1.5rem',\n        background: 'rgba(255, 255, 255, 0.05)',\n        borderRadius: '12px',\n        border: '1px solid rgba(255, 255, 255, 0.1)',\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: 'rgba(255, 255, 255, 0.9)',\n          fontSize: '1rem',\n          fontWeight: '600',\n          marginBottom: '0.5rem'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '0.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(BookOpen, {\n            size: 18,\n            color: \"rgba(255, 255, 255, 0.9)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this), \"Welcome Student!\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: 'rgba(255, 255, 255, 0.7)',\n          fontSize: '0.75rem',\n          lineHeight: '1.4'\n        },\n        children: \"Stay updated with the latest announcements and manage your profile\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentSidebar, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c = StudentSidebar;\nexport default StudentSidebar;\nvar _c;\n$RefreshReg$(_c, \"StudentSidebar\");", "map": {"version": 3, "names": ["React", "NavLink", "useLocation", "BarChart3", "Settings", "BookOpen", "Rss", "jsxDEV", "_jsxDEV", "navItems", "path", "label", "icon", "description", "StudentSidebar", "isOpen", "onToggle", "_s", "location", "isActive", "pathname", "style", "position", "left", "top", "height", "width", "background", "transition", "zIndex", "boxShadow", "overflow", "children", "padding", "borderBottom", "display", "alignItems", "gap", "minHeight", "src", "alt", "objectFit", "flexShrink", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "margin", "fontSize", "fontWeight", "lineHeight", "map", "item", "to", "textDecoration", "borderRight", "onMouseEnter", "e", "currentTarget", "onMouseLeave", "textAlign", "size", "flex", "min<PERSON><PERSON><PERSON>", "marginBottom", "bottom", "right", "borderRadius", "border", "_c", "$RefreshReg$"], "sources": ["D:/capstone-e-bulletin-reactjs-proj/FRONT-VCBA-E-BULLETIN-BOARD/src/components/student/layout/StudentSidebar.tsx"], "sourcesContent": ["import React from 'react';\nimport { NavLink, useLocation } from 'react-router-dom';\nimport { BarChart3, Settings, BookOpen, Rss } from 'lucide-react';\n\ninterface StudentSidebarProps {\n  isOpen: boolean;\n  onToggle: () => void;\n}\n\ninterface NavItem {\n  path: string;\n  label: string;\n  icon: React.ComponentType<{ size?: number; color?: string }>;\n  description: string;\n}\n\nconst navItems: NavItem[] = [\n  {\n    path: '/student/dashboard',\n    label: 'Dashboard',\n    icon: BarChart3,\n    description: 'Overview & Quick Actions'\n  },\n  {\n    path: '/student/newsfeed',\n    label: 'Newsfeed',\n    icon: Rss,\n    description: 'Latest Announcements & Events'\n  },\n  {\n    path: '/student/settings',\n    label: 'Settings',\n    icon: Settings,\n    description: 'Profile & Preferences'\n  }\n];\n\nconst StudentSidebar: React.FC<StudentSidebarProps> = ({ isOpen, onToggle }) => {\n  const location = useLocation();\n\n  const isActive = (path: string) => {\n    return location.pathname === path;\n  };\n\n  return (\n    <aside style={{\n      position: 'fixed',\n      left: 0,\n      top: 0,\n      height: '100vh',\n      width: isOpen ? '280px' : '80px',\n      background: 'linear-gradient(180deg, #1e40af 0%, #1e3a8a 100%)',\n      transition: 'width 0.3s ease',\n      zIndex: 1000,\n      boxShadow: '4px 0 20px rgba(0, 0, 0, 0.1)',\n      overflow: 'hidden'\n    }}>\n      {/* Logo Section */}\n      <div style={{\n        padding: isOpen ? '1.5rem' : '1rem',\n        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '1rem',\n        minHeight: '80px'\n      }}>\n        <img\n          src=\"/logo/vcba1.png\"\n          alt=\"VCBA Logo\"\n          style={{\n            width: '48px',\n            height: '48px',\n            objectFit: 'contain',\n            flexShrink: 0\n          }}\n        />\n        {isOpen && (\n          <div>\n            <h2 style={{\n              color: 'white',\n              margin: 0,\n              fontSize: '1.1rem',\n              fontWeight: '700',\n              lineHeight: '1.2'\n            }}>\n              Student Portal\n            </h2>\n            <p style={{\n              color: 'rgba(255, 255, 255, 0.7)',\n              margin: 0,\n              fontSize: '0.75rem',\n              lineHeight: '1.2'\n            }}>\n              E-Bulletin Board\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Navigation */}\n      <nav style={{ padding: '2rem 0' }}>\n        {navItems.map((item) => (\n          <NavLink\n            key={item.path}\n            to={item.path}\n            style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              padding: isOpen ? '1.5rem 1.5rem' : '1.5rem',\n              color: isActive(item.path) ? '#fbbf24' : 'rgba(255, 255, 255, 0.8)',\n              textDecoration: 'none',\n              background: isActive(item.path) \n                ? 'linear-gradient(90deg, rgba(251, 191, 36, 0.2) 0%, transparent 100%)'\n                : 'transparent',\n              borderRight: isActive(item.path) ? '3px solid #fbbf24' : '3px solid transparent',\n              transition: 'all 0.2s ease',\n              position: 'relative',\n              overflow: 'hidden'\n            }}\n            onMouseEnter={(e) => {\n              if (!isActive(item.path)) {\n                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.05)';\n                e.currentTarget.style.color = 'white';\n              }\n            }}\n            onMouseLeave={(e) => {\n              if (!isActive(item.path)) {\n                e.currentTarget.style.background = 'transparent';\n                e.currentTarget.style.color = 'rgba(255, 255, 255, 0.8)';\n              }\n            }}\n          >\n            <span style={{\n              fontSize: '1.5rem',\n              flexShrink: 0,\n              width: '24px',\n              textAlign: 'center'\n            }}>\n              <item.icon size={20} color={isActive(item.path) ? '#facc15' : 'rgba(255, 255, 255, 0.8)'} />\n            </span>\n            {isOpen && (\n              <div style={{ flex: 1, minWidth: 0 }}>\n                <div style={{\n                  fontWeight: isActive(item.path) ? '600' : '500',\n                  fontSize: '1rem',\n                  marginBottom: '0.25rem'\n                }}>\n                  {item.label}\n                </div>\n                <div style={{\n                  fontSize: '0.75rem',\n                  color: isActive(item.path) \n                    ? 'rgba(251, 191, 36, 0.8)' \n                    : 'rgba(255, 255, 255, 0.6)',\n                  lineHeight: '1.2'\n                }}>\n                  {item.description}\n                </div>\n              </div>\n            )}\n          </NavLink>\n        ))}\n      </nav>\n\n      {/* Welcome Message */}\n      {isOpen && (\n        <div style={{\n          position: 'absolute',\n          bottom: '2rem',\n          left: '1.5rem',\n          right: '1.5rem',\n          padding: '1.5rem',\n          background: 'rgba(255, 255, 255, 0.05)',\n          borderRadius: '12px',\n          border: '1px solid rgba(255, 255, 255, 0.1)',\n          textAlign: 'center'\n        }}>\n          <div style={{\n            color: 'rgba(255, 255, 255, 0.9)',\n            fontSize: '1rem',\n            fontWeight: '600',\n            marginBottom: '0.5rem'\n          }}>\n            <span style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>\n              <BookOpen size={18} color=\"rgba(255, 255, 255, 0.9)\" />\n              Welcome Student!\n            </span>\n          </div>\n          <div style={{\n            color: 'rgba(255, 255, 255, 0.7)',\n            fontSize: '0.75rem',\n            lineHeight: '1.4'\n          }}>\n            Stay updated with the latest announcements and manage your profile\n          </div>\n        </div>\n      )}\n    </aside>\n  );\n};\n\nexport default StudentSidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,EAAEC,WAAW,QAAQ,kBAAkB;AACvD,SAASC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAclE,MAAMC,QAAmB,GAAG,CAC1B;EACEC,IAAI,EAAE,oBAAoB;EAC1BC,KAAK,EAAE,WAAW;EAClBC,IAAI,EAAET,SAAS;EACfU,WAAW,EAAE;AACf,CAAC,EACD;EACEH,IAAI,EAAE,mBAAmB;EACzBC,KAAK,EAAE,UAAU;EACjBC,IAAI,EAAEN,GAAG;EACTO,WAAW,EAAE;AACf,CAAC,EACD;EACEH,IAAI,EAAE,mBAAmB;EACzBC,KAAK,EAAE,UAAU;EACjBC,IAAI,EAAER,QAAQ;EACdS,WAAW,EAAE;AACf,CAAC,CACF;AAED,MAAMC,cAA6C,GAAGA,CAAC;EAAEC,MAAM;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC9E,MAAMC,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAE9B,MAAMiB,QAAQ,GAAIT,IAAY,IAAK;IACjC,OAAOQ,QAAQ,CAACE,QAAQ,KAAKV,IAAI;EACnC,CAAC;EAED,oBACEF,OAAA;IAAOa,KAAK,EAAE;MACZC,QAAQ,EAAE,OAAO;MACjBC,IAAI,EAAE,CAAC;MACPC,GAAG,EAAE,CAAC;MACNC,MAAM,EAAE,OAAO;MACfC,KAAK,EAAEX,MAAM,GAAG,OAAO,GAAG,MAAM;MAChCY,UAAU,EAAE,mDAAmD;MAC/DC,UAAU,EAAE,iBAAiB;MAC7BC,MAAM,EAAE,IAAI;MACZC,SAAS,EAAE,+BAA+B;MAC1CC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBAEAxB,OAAA;MAAKa,KAAK,EAAE;QACVY,OAAO,EAAElB,MAAM,GAAG,QAAQ,GAAG,MAAM;QACnCmB,YAAY,EAAE,oCAAoC;QAClDC,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,MAAM;QACXC,SAAS,EAAE;MACb,CAAE;MAAAN,QAAA,gBACAxB,OAAA;QACE+B,GAAG,EAAC,iBAAiB;QACrBC,GAAG,EAAC,WAAW;QACfnB,KAAK,EAAE;UACLK,KAAK,EAAE,MAAM;UACbD,MAAM,EAAE,MAAM;UACdgB,SAAS,EAAE,SAAS;UACpBC,UAAU,EAAE;QACd;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACD/B,MAAM,iBACLP,OAAA;QAAAwB,QAAA,gBACExB,OAAA;UAAIa,KAAK,EAAE;YACT0B,KAAK,EAAE,OAAO;YACdC,MAAM,EAAE,CAAC;YACTC,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,KAAK;YACjBC,UAAU,EAAE;UACd,CAAE;UAAAnB,QAAA,EAAC;QAEH;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtC,OAAA;UAAGa,KAAK,EAAE;YACR0B,KAAK,EAAE,0BAA0B;YACjCC,MAAM,EAAE,CAAC;YACTC,QAAQ,EAAE,SAAS;YACnBE,UAAU,EAAE;UACd,CAAE;UAAAnB,QAAA,EAAC;QAEH;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNtC,OAAA;MAAKa,KAAK,EAAE;QAAEY,OAAO,EAAE;MAAS,CAAE;MAAAD,QAAA,EAC/BvB,QAAQ,CAAC2C,GAAG,CAAEC,IAAI,iBACjB7C,OAAA,CAACP,OAAO;QAENqD,EAAE,EAAED,IAAI,CAAC3C,IAAK;QACdW,KAAK,EAAE;UACLc,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE,MAAM;UACXJ,OAAO,EAAElB,MAAM,GAAG,eAAe,GAAG,QAAQ;UAC5CgC,KAAK,EAAE5B,QAAQ,CAACkC,IAAI,CAAC3C,IAAI,CAAC,GAAG,SAAS,GAAG,0BAA0B;UACnE6C,cAAc,EAAE,MAAM;UACtB5B,UAAU,EAAER,QAAQ,CAACkC,IAAI,CAAC3C,IAAI,CAAC,GAC3B,sEAAsE,GACtE,aAAa;UACjB8C,WAAW,EAAErC,QAAQ,CAACkC,IAAI,CAAC3C,IAAI,CAAC,GAAG,mBAAmB,GAAG,uBAAuB;UAChFkB,UAAU,EAAE,eAAe;UAC3BN,QAAQ,EAAE,UAAU;UACpBS,QAAQ,EAAE;QACZ,CAAE;QACF0B,YAAY,EAAGC,CAAC,IAAK;UACnB,IAAI,CAACvC,QAAQ,CAACkC,IAAI,CAAC3C,IAAI,CAAC,EAAE;YACxBgD,CAAC,CAACC,aAAa,CAACtC,KAAK,CAACM,UAAU,GAAG,2BAA2B;YAC9D+B,CAAC,CAACC,aAAa,CAACtC,KAAK,CAAC0B,KAAK,GAAG,OAAO;UACvC;QACF,CAAE;QACFa,YAAY,EAAGF,CAAC,IAAK;UACnB,IAAI,CAACvC,QAAQ,CAACkC,IAAI,CAAC3C,IAAI,CAAC,EAAE;YACxBgD,CAAC,CAACC,aAAa,CAACtC,KAAK,CAACM,UAAU,GAAG,aAAa;YAChD+B,CAAC,CAACC,aAAa,CAACtC,KAAK,CAAC0B,KAAK,GAAG,0BAA0B;UAC1D;QACF,CAAE;QAAAf,QAAA,gBAEFxB,OAAA;UAAMa,KAAK,EAAE;YACX4B,QAAQ,EAAE,QAAQ;YAClBP,UAAU,EAAE,CAAC;YACbhB,KAAK,EAAE,MAAM;YACbmC,SAAS,EAAE;UACb,CAAE;UAAA7B,QAAA,eACAxB,OAAA,CAAC6C,IAAI,CAACzC,IAAI;YAACkD,IAAI,EAAE,EAAG;YAACf,KAAK,EAAE5B,QAAQ,CAACkC,IAAI,CAAC3C,IAAI,CAAC,GAAG,SAAS,GAAG;UAA2B;YAAAiC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxF,CAAC,EACN/B,MAAM,iBACLP,OAAA;UAAKa,KAAK,EAAE;YAAE0C,IAAI,EAAE,CAAC;YAAEC,QAAQ,EAAE;UAAE,CAAE;UAAAhC,QAAA,gBACnCxB,OAAA;YAAKa,KAAK,EAAE;cACV6B,UAAU,EAAE/B,QAAQ,CAACkC,IAAI,CAAC3C,IAAI,CAAC,GAAG,KAAK,GAAG,KAAK;cAC/CuC,QAAQ,EAAE,MAAM;cAChBgB,YAAY,EAAE;YAChB,CAAE;YAAAjC,QAAA,EACCqB,IAAI,CAAC1C;UAAK;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eACNtC,OAAA;YAAKa,KAAK,EAAE;cACV4B,QAAQ,EAAE,SAAS;cACnBF,KAAK,EAAE5B,QAAQ,CAACkC,IAAI,CAAC3C,IAAI,CAAC,GACtB,yBAAyB,GACzB,0BAA0B;cAC9ByC,UAAU,EAAE;YACd,CAAE;YAAAnB,QAAA,EACCqB,IAAI,CAACxC;UAAW;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACd,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA,GAzDIO,IAAI,CAAC3C,IAAI;QAAAiC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA0DP,CACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL/B,MAAM,iBACLP,OAAA;MAAKa,KAAK,EAAE;QACVC,QAAQ,EAAE,UAAU;QACpB4C,MAAM,EAAE,MAAM;QACd3C,IAAI,EAAE,QAAQ;QACd4C,KAAK,EAAE,QAAQ;QACflC,OAAO,EAAE,QAAQ;QACjBN,UAAU,EAAE,2BAA2B;QACvCyC,YAAY,EAAE,MAAM;QACpBC,MAAM,EAAE,oCAAoC;QAC5CR,SAAS,EAAE;MACb,CAAE;MAAA7B,QAAA,gBACAxB,OAAA;QAAKa,KAAK,EAAE;UACV0B,KAAK,EAAE,0BAA0B;UACjCE,QAAQ,EAAE,MAAM;UAChBC,UAAU,EAAE,KAAK;UACjBe,YAAY,EAAE;QAChB,CAAE;QAAAjC,QAAA,eACAxB,OAAA;UAAMa,KAAK,EAAE;YAAEc,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAS,CAAE;UAAAL,QAAA,gBACpExB,OAAA,CAACH,QAAQ;YAACyD,IAAI,EAAE,EAAG;YAACf,KAAK,EAAC;UAA0B;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,oBAEzD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNtC,OAAA;QAAKa,KAAK,EAAE;UACV0B,KAAK,EAAE,0BAA0B;UACjCE,QAAQ,EAAE,SAAS;UACnBE,UAAU,EAAE;QACd,CAAE;QAAAnB,QAAA,EAAC;MAEH;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEZ,CAAC;AAAC7B,EAAA,CAnKIH,cAA6C;EAAA,QAChCZ,WAAW;AAAA;AAAAoE,EAAA,GADxBxD,cAA6C;AAqKnD,eAAeA,cAAc;AAAC,IAAAwD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}