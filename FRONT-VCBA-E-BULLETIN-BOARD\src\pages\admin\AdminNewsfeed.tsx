import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { announcementService } from '../../services';
import { useCategories } from '../../hooks/useAnnouncements';
import AdminCommentSection from '../../components/admin/AdminCommentSection';
import FacebookImageGallery from '../../components/common/FacebookImageGallery';
import type { AnnouncementAttachment } from '../../services/announcementService';
import type { CalendarEvent } from '../../types/calendar.types';
import { getImageUrl, API_BASE_URL } from '../../config/constants';
import {
  Newspaper,
  Search,
  Pin,
  Calendar,
  MessageSquare,
  Heart,
  Eye,
  Edit,
  Users,
  LayoutDashboard,
  BookOpen,
  PartyPopper,
  AlertTriangle,
  Clock,
  Trophy,
  Briefcase,
  GraduationCap,
  Flag,
  Coffee,
  Plane
} from 'lucide-react';

// Image Display Component
interface ImageDisplayProps {
  imagePath: string;
  alt: string;
  style?: React.CSSProperties;
  onMouseEnter?: (e: React.MouseEvent<HTMLImageElement>) => void;
  onMouseLeave?: (e: React.MouseEvent<HTMLImageElement>) => void;
}

const ImageDisplay: React.FC<ImageDisplayProps> = ({ 
  imagePath, 
  alt, 
  style, 
  onMouseEnter, 
  onMouseLeave 
}) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  const handleImageError = () => {
    setImageError(true);
    setImageLoading(false);
  };

  const handleImageLoad = () => {
    setImageLoading(false);
  };

  if (imageError) {
    return (
      <div style={{
        ...style,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f3f4f6',
        color: '#6b7280',
        fontSize: '0.875rem'
      }}>
        Image not available
      </div>
    );
  }

  return (
    <div style={{ position: 'relative', ...style }}>
      {imageLoading && (
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#f3f4f6',
          color: '#6b7280'
        }}>
          Loading...
        </div>
      )}
      <img
        src={getImageUrl(imagePath || '') || ''}
        alt={alt}
        style={{
          ...style,
          opacity: imageLoading ? 0 : 1,
          transition: 'opacity 0.3s ease'
        }}
        onError={handleImageError}
        onLoad={handleImageLoad}
        onMouseEnter={onMouseEnter}
        onMouseLeave={onMouseLeave}
      />
    </div>
  );
};

// Image Gallery Component
interface ImageGalleryProps {
  images: AnnouncementAttachment[];
  altPrefix: string;
  onImageClick?: (index: number) => void;
}

const ImageGallery: React.FC<ImageGalleryProps> = ({ images, altPrefix, onImageClick }) => {
  if (!images || images.length === 0) return null;

  const visibleImages = images.slice(0, 4);
  const remainingCount = Math.max(0, images.length - 4);

  const getContainerStyle = (index: number, total: number): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      position: 'relative',
      overflow: 'hidden',
      borderRadius: '12px',
      cursor: onImageClick ? 'pointer' : 'default'
    };

    if (total === 1) {
      return { ...baseStyle, width: '100%', height: '300px' };
    } else if (total === 2) {
      return { ...baseStyle, width: '50%', height: '250px' };
    } else if (total === 3) {
      if (index === 0) {
        return { ...baseStyle, width: '50%', height: '250px' };
      } else {
        return { ...baseStyle, width: '50%', height: '120px' };
      }
    } else {
      if (index === 0) {
        return { ...baseStyle, width: '50%', height: '250px' };
      } else {
        return { ...baseStyle, width: '33.33%', height: '120px' };
      }
    }
  };

  const getImageStyle = (index: number, total: number): React.CSSProperties => {
    return {
      width: '100%',
      height: '100%',
      objectFit: 'cover' as const,
      transition: 'transform 0.3s ease'
    };
  };

  return (
    <div style={{
      display: 'flex',
      gap: '4px',
      width: '100%',
      marginBottom: '1rem'
    }}>
      {/* Main image or left side */}
      <div style={getContainerStyle(0, visibleImages.length)}>
        <ImageDisplay
          imagePath={visibleImages[0].file_path}
          alt={`${altPrefix} - Image 1`}
          style={getImageStyle(0, visibleImages.length)}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'scale(1.02)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'scale(1)';
          }}
        />
        {onImageClick && (
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              cursor: 'pointer'
            }}
            onClick={() => onImageClick(0)}
          />
        )}
      </div>

      {/* Right side images */}
      {visibleImages.length > 1 && (
        <div style={{
          display: 'flex',
          flexDirection: visibleImages.length === 2 ? 'row' : 'column',
          gap: '4px',
          width: visibleImages.length === 2 ? '50%' : '50%'
        }}>
          {visibleImages.slice(1).map((image, idx) => {
            const actualIndex = idx + 1;
            const isLast = actualIndex === visibleImages.length - 1 && remainingCount > 0;
            
            return (
              <div
                key={actualIndex}
                style={{
                  ...getContainerStyle(actualIndex, visibleImages.length),
                  position: 'relative'
                }}
              >
                <ImageDisplay
                  imagePath={image.file_path}
                  alt={`${altPrefix} - Image ${actualIndex + 1}`}
                  style={getImageStyle(actualIndex, visibleImages.length)}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'scale(1.02)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'scale(1)';
                  }}
                />
                
                {/* Overlay for remaining images count */}
                {isLast && remainingCount > 0 && (
                  <div style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.6)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '1.5rem',
                    fontWeight: '600'
                  }}>
                    +{remainingCount}
                  </div>
                )}
                
                {onImageClick && (
                  <div
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      cursor: 'pointer'
                    }}
                    onClick={() => onImageClick(actualIndex)}
                  />
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

// Main AdminNewsfeed Component
const AdminNewsfeed: React.FC = () => {
  const navigate = useNavigate();

  // Category styling function
  const getCategoryStyle = (categoryName: string) => {
    const styles = {
      'ACADEMIC': {
        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',
        icon: BookOpen
      },
      'GENERAL': {
        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',
        icon: Users
      },
      'EVENTS': {
        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
        icon: PartyPopper
      },
      'EMERGENCY': {
        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
        icon: AlertTriangle
      },
      'SPORTS': {
        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
        icon: Trophy
      },
      'DEADLINES': {
        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
        icon: Clock
      }
    };

    return styles[categoryName as keyof typeof styles] || styles['GENERAL'];
  };

  // Holiday type styling function
  const getHolidayTypeStyle = (holidayTypeName: string) => {
    const styles = {
      'National Holiday': {
        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',
        icon: Flag
      },
      'School Event': {
        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
        icon: GraduationCap
      },
      'Academic Break': {
        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
        icon: Coffee
      },
      'Sports Event': {
        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
        icon: Trophy
      },
      'Field Trip': {
        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',
        icon: Plane
      },
      'Meeting': {
        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',
        icon: Briefcase
      }
    };

    return styles[holidayTypeName as keyof typeof styles] || styles['School Event'];
  };

  // Filter states
  const [filterCategory, setFilterCategory] = useState<string>('');
  const [filterGradeLevel, setFilterGradeLevel] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');

  // UI states
  const [showComments, setShowComments] = useState<number | null>(null);
  const [newComment, setNewComment] = useState<{ [key: number]: string }>({});
  const [submittingComment, setSubmittingComment] = useState<number | null>(null);
  const [selectedPinnedPost, setSelectedPinnedPost] = useState<any | null>(null);

  // Data states
  const [announcements, setAnnouncements] = useState<any[]>([]);
  const [pinnedAnnouncements, setPinnedAnnouncements] = useState<any[]>([]);
  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | undefined>();
  const [calendarLoading, setCalendarLoading] = useState(false);
  const [calendarError, setCalendarError] = useState<string | undefined>();
  const [recentStudents, setRecentStudents] = useState<any[]>([]);
  const [studentLoading, setStudentLoading] = useState(false);

  const { categories } = useCategories();

  // Fetch published announcements with images (admin can see all)
  const fetchPublishedAnnouncements = async () => {
    try {
      setLoading(true);
      setError(undefined);

      const response = await fetch(`${API_BASE_URL}/api/announcements?status=published&page=1&limit=50&sort_by=created_at&sort_order=DESC`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,
          'Content-Type': 'application/json'
        }
      });
      const data = await response.json();

      if (data.success && data.data) {
        const announcementsData = data.data.announcements || [];

        // Fetch images for each announcement
        const announcementsWithImages = await Promise.all(
          announcementsData.map(async (announcement: any) => {
            try {
              const imageResponse = await fetch(`${API_BASE_URL}/api/announcements/${announcement.announcement_id}/images`, {
                headers: {
                  'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,
                  'Content-Type': 'application/json'
                }
              });
              const imageData = await imageResponse.json();

              if (imageData.success && imageData.data) {
                announcement.images = imageData.data.images || [];
              } else {
                announcement.images = [];
              }
            } catch (imgErr) {
              console.warn(`Failed to fetch images for announcement ${announcement.announcement_id}:`, imgErr);
              announcement.images = [];
            }
            return announcement;
          })
        );

        setAnnouncements(announcementsWithImages);

        // Separate pinned announcements
        const pinned = announcementsWithImages.filter((ann: any) => ann.is_pinned === 1);
        setPinnedAnnouncements(pinned);
      } else {
        setError('Failed to load announcements');
      }
    } catch (err: any) {
      console.error('Error fetching announcements:', err);
      setError(err.message || 'Failed to load announcements');
    } finally {
      setLoading(false);
    }
  };

  // Fetch calendar events
  const fetchCalendarEvents = async () => {
    try {
      setCalendarLoading(true);
      setCalendarError(undefined);

      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,
          'Content-Type': 'application/json'
        }
      });
      const data = await response.json();

      if (data.success && data.data) {
        const eventsData = data.data.events || data.data || [];

        // Fetch images for each event
        const eventsWithImages = await Promise.all(
          eventsData.map(async (event: any) => {
            try {
              const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`, {
                headers: {
                  'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,
                  'Content-Type': 'application/json'
                }
              });
              const imageData = await imageResponse.json();

              if (imageData.success && imageData.data) {
                event.images = imageData.data.attachments || [];
              } else {
                event.images = [];
              }
            } catch (imgErr) {
              console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);
              event.images = [];
            }
            return event;
          })
        );

        setCalendarEvents(eventsWithImages);
      } else {
        setCalendarError('Failed to load calendar events');
      }
    } catch (err: any) {
      console.error('Error fetching calendar events:', err);
      setCalendarError(err.message || 'Failed to load calendar events');
    } finally {
      setCalendarLoading(false);
    }
  };

  // Fetch recent student registrations
  const fetchRecentStudents = async () => {
    try {
      setStudentLoading(true);
      const response = await fetch(`${API_BASE_URL}/api/students?page=1&limit=5&sort_by=created_at&sort_order=DESC`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,
          'Content-Type': 'application/json'
        }
      });
      const data = await response.json();

      if (data.success && data.data) {
        setRecentStudents(data.data.students || []);
      }
    } catch (err: any) {
      console.error('Error fetching recent students:', err);
    } finally {
      setStudentLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchPublishedAnnouncements();
    fetchCalendarEvents();
    fetchRecentStudents();
  }, []);

  // Handle like/unlike functionality (admin perspective)
  const handleLikeToggle = async (announcement: any) => {
    try {
      if (announcement.user_reaction) {
        await announcementService.removeReaction(announcement.announcement_id);
      } else {
        const response = await fetch(`${API_BASE_URL}/api/announcements/${announcement.announcement_id}/reactions`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            reaction_type_id: 1,
            notify_admin: false // Admin doesn't need to notify themselves
          })
        });

        if (!response.ok) {
          throw new Error('Failed to add reaction');
        }
      }

      // Refresh data
      await fetchPublishedAnnouncements();
    } catch (error) {
      console.error('Error toggling like:', error);
    }
  };

  // Handle comment submission (admin perspective)
  const handleCommentSubmit = async (announcementId: number, commentText: string) => {
    if (!commentText.trim()) return;

    try {
      setSubmittingComment(announcementId);

      const response = await fetch(`${API_BASE_URL}/api/admin/announcements/${announcementId}/comments`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: commentText,
          commenter_name: 'Admin User',
          commenter_email: '<EMAIL>',
          notify_admin: false
        })
      });

      if (response.ok) {
        setNewComment(prev => ({ ...prev, [announcementId]: '' }));
        await fetchPublishedAnnouncements();
      } else {
        console.error('Failed to submit comment');
      }
    } catch (error) {
      console.error('Error submitting comment:', error);
    } finally {
      setSubmittingComment(null);
    }
  };

  // Filter announcements
  const filteredAnnouncements = announcements.filter(announcement => {
    const matchesSearch = !searchTerm ||
      announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      announcement.content.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory = !filterCategory ||
      announcement.category_id?.toString() === filterCategory;

    const matchesGradeLevel = !filterGradeLevel ||
      announcement.grade_level?.toString() === filterGradeLevel;

    return matchesSearch && matchesCategory && matchesGradeLevel;
  });

  // Filter calendar events with date-based filtering
  const filteredCalendarEvents = calendarEvents.filter(event => {
    const matchesSearch = !searchTerm ||
      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase()));

    // Only show events that are published and on or after today's date
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day

    const eventDate = new Date(event.event_date);
    eventDate.setHours(0, 0, 0, 0); // Reset time to start of day

    const isEventDateValid = eventDate >= today;
    const isPublished = (event as any).is_published === 1;

    return matchesSearch && isEventDateValid && isPublished;
  });

  // Combine and sort all content by date (most recent first)
  const displayAnnouncements = filteredAnnouncements;
  const displayEvents = filteredCalendarEvents;

  // Create combined content array for better chronological display
  const combinedContent = [
    ...displayAnnouncements.map(item => ({ ...item, type: 'announcement', sortDate: new Date(item.created_at) })),
    ...displayEvents.map(item => ({ ...item, type: 'event', sortDate: new Date(item.event_date) }))
  ].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)',
      position: 'relative'
    }}>
      {/* Background Pattern */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundImage: `
          radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.03) 0%, transparent 50%),
          radial-gradient(circle at 75% 75%, rgba(250, 204, 21, 0.03) 0%, transparent 50%)
        `,
        pointerEvents: 'none'
      }} />

      <div style={{ position: 'relative', zIndex: 1 }}>
        {/* Modern Admin Header */}
        <header style={{
          background: 'white',
          borderBottom: '1px solid #e5e7eb',
          position: 'sticky',
          top: 0,
          zIndex: 100,
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{
            padding: '0 2rem',
            height: '72px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            {/* Left Section: Logo + Page Title */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '1.5rem',
              minWidth: '300px'
            }}>
              <img
                src="/logo/vcba1.png"
                alt="VCBA Logo"
                style={{
                  width: '48px',
                  height: '48px',
                  objectFit: 'contain'
                }}
              />
              <div>
                <h1 style={{
                  margin: 0,
                  fontSize: '1.25rem',
                  fontWeight: '600',
                  color: '#111827',
                  lineHeight: '1.2'
                }}>
                  VCBA E-Bulletin Board
                </h1>
                <p style={{
                  margin: 0,
                  fontSize: '0.875rem',
                  color: '#6b7280',
                  lineHeight: '1.2'
                }}>
                  Admin Newsfeed
                </p>
              </div>
            </div>

            {/* Center Section: Search */}
            <div style={{
              flex: 1,
              maxWidth: '500px',
              margin: '0 2rem'
            }}>
              <div style={{ position: 'relative' }}>
                <Search
                  size={20}
                  style={{
                    position: 'absolute',
                    left: '1rem',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: '#9ca3af'
                  }}
                />
                <input
                  type="text"
                  placeholder="Search announcements and events..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  style={{
                    width: '100%',
                    height: '44px',
                    padding: '0 1rem 0 3rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '12px',
                    background: '#f9fafb',
                    color: '#374151',
                    fontSize: '0.875rem',
                    outline: 'none',
                    transition: 'all 0.2s ease'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = '#22c55e';
                    e.currentTarget.style.background = 'white';
                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = '#d1d5db';
                    e.currentTarget.style.background = '#f9fafb';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                />
              </div>
            </div>

            {/* Right Section: Navigation + Filters */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '1rem',
              minWidth: '400px',
              justifyContent: 'flex-end'
            }}>
              
              {/* Filters Group */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                padding: '0.5rem',
                background: '#f9fafb',
                borderRadius: '12px',
                border: '1px solid #e5e7eb'
              }}>
                <select
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                  style={{
                    padding: '0.5rem 0.75rem',
                    border: 'none',
                    borderRadius: '8px',
                    background: 'white',
                    color: '#374151',
                    fontSize: '0.875rem',
                    outline: 'none',
                    cursor: 'pointer',
                    minWidth: '110px'
                  }}
                >
                  <option value="">All Categories</option>
                  {categories.map(category => (
                    <option key={category.category_id} value={category.category_id.toString()}>
                      {category.name}
                    </option>
                  ))}
                </select>

                <select
                  value={filterGradeLevel}
                  onChange={(e) => setFilterGradeLevel(e.target.value)}
                  style={{
                    padding: '0.5rem 0.75rem',
                    border: 'none',
                    borderRadius: '8px',
                    background: 'white',
                    color: '#374151',
                    fontSize: '0.875rem',
                    outline: 'none',
                    cursor: 'pointer',
                    minWidth: '100px'
                  }}
                >
                  <option value="">All Grades</option>
                  <option value="11">Grade 11</option>
                  <option value="12">Grade 12</option>
                </select>

                {(searchTerm || filterCategory || filterGradeLevel) && (
                  <button
                    onClick={() => {
                      setSearchTerm('');
                      setFilterCategory('');
                      setFilterGradeLevel('');
                    }}
                    style={{
                      padding: '0.5rem 0.75rem',
                      border: 'none',
                      borderRadius: '8px',
                      background: '#ef4444',
                      color: 'white',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = '#dc2626';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = '#ef4444';
                    }}
                  >
                    Clear
                  </button>
                )}
              </div>

              {/* Back to Dashboard Button */}
              <button
                onClick={() => navigate('/admin/dashboard')}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  padding: '0.75rem 1rem',
                  background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
                  border: 'none',
                  borderRadius: '12px',
                  color: 'white',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  boxShadow: '0 2px 8px rgba(34, 197, 94, 0.2)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-1px)';
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.2)';
                }}
              >
                <LayoutDashboard size={16} />
                Dashboard
              </button>
            </div>
          </div>
        </header>



        {/* Main Content Layout */}
        <div style={{
          padding: '2rem',
          display: 'flex',
          gap: '2rem',
          alignItems: 'flex-start'
        }}>
          {/* Left Sidebar: Pinned Posts */}
          <div style={{
            width: '320px',
            flexShrink: 0
          }}>
            <div style={{
              background: 'white',
              borderRadius: '16px',
              border: '1px solid #e5e7eb',
              overflow: 'hidden',
              position: 'sticky',
              top: '100px'
            }}>
              {/* Pinned Posts Header */}
              <div style={{
                padding: '1.5rem 1.5rem 1rem',
                borderBottom: '1px solid #f3f4f6'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem',
                  marginBottom: '0.5rem'
                }}>
                  <Pin size={20} style={{ color: '#22c55e' }} />
                  <h3 style={{
                    margin: 0,
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    color: '#111827'
                  }}>
                    Pinned Posts
                  </h3>
                </div>
                <p style={{
                  margin: 0,
                  fontSize: '0.875rem',
                  color: '#6b7280'
                }}>
                  Important announcements and updates
                </p>
              </div>

              {/* Pinned Posts List */}
              <div style={{ padding: '1rem' }}>
                {pinnedAnnouncements.length > 0 ? (
                  <>
                    {pinnedAnnouncements.slice(0, 3).map((announcement, index) => {
                      const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();
                      const categoryStyle = getCategoryStyle(categoryName);

                      return (
                        <div
                          key={announcement.announcement_id}
                          style={{
                            padding: '1rem',
                            background: '#f8fafc',
                            borderRadius: '12px',
                            border: '1px solid #e2e8f0',
                            marginBottom: '1rem',
                            cursor: 'pointer',
                            transition: 'all 0.2s ease'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.background = '#f1f5f9';
                            e.currentTarget.style.borderColor = '#22c55e';
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.background = '#f8fafc';
                            e.currentTarget.style.borderColor = '#e2e8f0';
                          }}
                          onClick={() => setSelectedPinnedPost(announcement)}
                        >
                          <div style={{
                            display: 'flex',
                            alignItems: 'flex-start',
                            gap: '0.75rem'
                          }}>
                            <div style={{
                              width: '8px',
                              height: '8px',
                              background: categoryStyle.background.includes('#ef4444') ? '#ef4444' : '#22c55e',
                              borderRadius: '50%',
                              marginTop: '0.5rem',
                              flexShrink: 0
                            }} />
                            <div style={{ flex: 1 }}>
                              <h4 style={{
                                margin: '0 0 0.5rem 0',
                                fontSize: '0.875rem',
                                fontWeight: '600',
                                color: '#111827',
                                lineHeight: '1.4'
                              }}>
                                {announcement.title}
                              </h4>
                              <p style={{
                                margin: '0 0 0.5rem 0',
                                fontSize: '0.8rem',
                                color: '#6b7280',
                                lineHeight: '1.4'
                              }}>
                                {announcement.content.length > 80
                                  ? `${announcement.content.substring(0, 80)}...`
                                  : announcement.content}
                              </p>
                              <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '0.5rem',
                                fontSize: '0.75rem',
                                color: '#9ca3af'
                              }}>
                                <Calendar size={12} />
                                <span>{new Date(announcement.created_at).toLocaleDateString()}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}

                    {pinnedAnnouncements.length > 3 && (
                      <button style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #e5e7eb',
                        borderRadius: '8px',
                        background: 'white',
                        color: '#22c55e',
                        fontSize: '0.875rem',
                        fontWeight: '500',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.background = '#f0fdf4';
                        e.currentTarget.style.borderColor = '#22c55e';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.background = 'white';
                        e.currentTarget.style.borderColor = '#e5e7eb';
                      }}>
                        View All {pinnedAnnouncements.length} Pinned Posts
                      </button>
                    )}
                  </>
                ) : (
                  <div style={{
                    padding: '2rem 1rem',
                    textAlign: 'center',
                    color: '#6b7280'
                  }}>
                    <Pin size={24} style={{ marginBottom: '0.5rem', opacity: 0.5 }} />
                    <p style={{ margin: 0, fontSize: '0.875rem' }}>
                      No pinned posts available
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Content: Main Feed */}
          <div style={{ flex: 1, minWidth: 0 }}>
          {/* Loading State */}
          {(loading || calendarLoading) && (
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              minHeight: '400px'
            }}>
              <div style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: '1rem'
              }}>
                <div style={{
                  width: '3rem',
                  height: '3rem',
                  border: '4px solid rgba(34, 197, 94, 0.2)',
                  borderTop: '4px solid #22c55e',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }}></div>
                <p style={{
                  color: '#6b7280',
                  fontSize: '1rem',
                  fontWeight: '500'
                }}>
                  Loading content...
                </p>
              </div>
            </div>
          )}

          {/* Error State */}
          {(error || calendarError) && !loading && !calendarLoading && (
            <div style={{
              padding: '2rem',
              background: 'rgba(239, 68, 68, 0.1)',
              border: '1px solid rgba(239, 68, 68, 0.2)',
              borderRadius: '16px',
              textAlign: 'center'
            }}>
              <div style={{
                width: '4rem',
                height: '4rem',
                background: 'rgba(239, 68, 68, 0.1)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 1rem'
              }}>
                <MessageSquare size={24} color="#ef4444" />
              </div>
              <h3 style={{
                color: '#ef4444',
                margin: '0 0 0.5rem 0',
                fontSize: '1.25rem',
                fontWeight: '600'
              }}>
                Error Loading Content
              </h3>
              <p style={{
                color: '#6b7280',
                margin: '0 0 1.5rem 0',
                fontSize: '1rem'
              }}>
                {error || calendarError}
              </p>
              <button
                onClick={() => {
                  fetchPublishedAnnouncements();
                  fetchCalendarEvents();
                }}
                style={{
                  background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '12px',
                  padding: '0.75rem 1.5rem',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-1px)';
                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = 'none';
                }}
              >
                Try Again
              </button>
            </div>
          )}

          {/* Empty State */}
          {!loading && !calendarLoading && !error && !calendarError &&
           displayAnnouncements.length === 0 && displayEvents.length === 0 && (
            <div style={{
              padding: '4rem 2rem',
              textAlign: 'center'
            }}>
              <div style={{
                width: '5rem',
                height: '5rem',
                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                margin: '0 auto 2rem'
              }}>
                <Newspaper size={32} color="white" />
              </div>
              <h3 style={{
                color: '#374151',
                margin: '0 0 1rem 0',
                fontSize: '1.5rem',
                fontWeight: '600'
              }}>
                No Content Available
              </h3>
              <p style={{
                color: '#6b7280',
                margin: '0 0 2rem 0',
                fontSize: '1rem',
                lineHeight: '1.6',
                maxWidth: '500px',
                marginLeft: 'auto',
                marginRight: 'auto'
              }}>
                {searchTerm || filterCategory || filterGradeLevel
                  ? 'No content matches your current filters. Try adjusting your search criteria.'
                  : 'There are no published announcements or events at the moment. Check back later for updates.'
                }
              </p>
              {(searchTerm || filterCategory || filterGradeLevel) && (
                <button
                  onClick={() => {
                    setSearchTerm('');
                    setFilterCategory('');
                    setFilterGradeLevel('');
                  }}
                  style={{
                    background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '12px',
                    padding: '0.75rem 1.5rem',
                    fontSize: '0.875rem',
                    fontWeight: '600',
                    cursor: 'pointer',
                    transition: 'all 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'translateY(-1px)';
                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'translateY(0)';
                    e.currentTarget.style.boxShadow = 'none';
                  }}
                >
                  Clear Filters
                </button>
              )}
            </div>
          )}

          {/* Recent Students Section (Admin Only) */}
          {!studentLoading && recentStudents.length > 0 && (
            <div style={{
              background: 'rgba(255, 255, 255, 0.95)',
              borderRadius: '16px',
              padding: '1.5rem',
              border: '1px solid rgba(0, 0, 0, 0.1)',
              backdropFilter: 'blur(10px)',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
              marginBottom: '1.5rem'
            }}>
              <h3 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                color: '#2d5016',
                margin: '0 0 1rem 0',
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem'
              }}>
                👥 Recent Student Registrations
              </h3>
              <div style={{
                display: 'grid',
                gap: '0.75rem'
              }}>
                {recentStudents.slice(0, 3).map((student: any) => (
                  <div
                    key={student.student_id}
                    style={{
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      padding: '0.75rem',
                      backgroundColor: '#f8fdf8',
                      borderRadius: '8px',
                      border: '1px solid #e8f5e8'
                    }}
                  >
                    <div>
                      <div style={{
                        fontWeight: '500',
                        color: '#2d5016',
                        fontSize: '0.875rem'
                      }}>
                        {student.profile?.first_name} {student.profile?.last_name}
                      </div>
                      <div style={{
                        fontSize: '0.75rem',
                        color: '#6b7280'
                      }}>
                        Grade {student.profile?.grade_level} • {student.student_number}
                      </div>
                    </div>
                    <div style={{
                      fontSize: '0.75rem',
                      color: '#6b7280'
                    }}>
                      {new Date(student.created_at).toLocaleDateString()}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Content Feed */}
          {!loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && (
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '1.5rem'
            }}>
              {/* Calendar Events */}
              {displayEvents.length > 0 && (
                <>
                  {displayEvents.map(event => (
                    <div
                      key={`event-${event.calendar_id}`}
                      style={{
                        background: 'rgba(255, 255, 255, 0.95)',
                        borderRadius: '16px',
                        padding: '1.5rem',
                        border: '1px solid rgba(0, 0, 0, 0.1)',
                        backdropFilter: 'blur(10px)',
                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-2px)';
                        e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';
                      }}
                    >
                      {/* Event Header */}
                      <div style={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        gap: '1rem',
                        marginBottom: '1rem'
                      }}>
                        <div style={{
                          width: '48px',
                          height: '48px',
                          background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                          borderRadius: '12px',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          flexShrink: 0
                        }}>
                          <Calendar size={24} color="white" />
                        </div>

                        <div style={{ flex: 1 }}>
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.75rem',
                            marginBottom: '0.5rem'
                          }}>
                            {(() => {
                              const holidayTypeName = event.holiday_type_name || 'School Event';
                              const holidayStyle = getHolidayTypeStyle(holidayTypeName);
                              const IconComponent = holidayStyle.icon;

                              return (
                                <span style={{
                                  background: holidayStyle.background,
                                  color: 'white',
                                  fontSize: '0.75rem',
                                  fontWeight: '600',
                                  padding: '0.25rem 0.75rem',
                                  borderRadius: '20px',
                                  textTransform: 'uppercase',
                                  letterSpacing: '0.5px',
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: '0.25rem'
                                }}>
                                  <IconComponent size={12} color="white" />
                                  {holidayTypeName}
                                </span>
                              );
                            })()}

                            <div style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.25rem',
                              color: '#6b7280',
                              fontSize: '0.875rem'
                            }}>
                              <Calendar size={14} />
                              {new Date(event.event_date).toLocaleDateString('en-US', {
                                weekday: 'long',
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric'
                              })}
                            </div>
                          </div>

                          <h3 style={{
                            margin: '0 0 0.5rem 0',
                            fontSize: '1.25rem',
                            fontWeight: '700',
                            color: '#1f2937',
                            lineHeight: '1.3'
                          }}>
                            {event.title}
                          </h3>
                        </div>

                        {/* Admin Event Actions */}
                        <div style={{ display: 'flex', gap: '0.5rem' }}>
                          <button
                            onClick={() => navigate(`/admin/calendar?event=${event.calendar_id}`)}
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.25rem',
                              padding: '0.5rem 0.75rem',
                              background: 'rgba(59, 130, 246, 0.1)',
                              border: '1px solid rgba(59, 130, 246, 0.2)',
                              borderRadius: '8px',
                              color: '#3b82f6',
                              fontSize: '0.75rem',
                              fontWeight: '500',
                              cursor: 'pointer',
                              transition: 'all 0.2s ease'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.background = 'rgba(59, 130, 246, 0.2)';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.background = 'rgba(59, 130, 246, 0.1)';
                            }}
                          >
                            <Edit size={12} />
                            Edit
                          </button>
                        </div>
                      </div>

                      {/* Event Content */}
                      {event.description && (
                        <div style={{
                          color: '#4b5563',
                          fontSize: '0.95rem',
                          lineHeight: '1.6',
                          marginBottom: '1rem'
                        }}>
                          {event.description}
                        </div>
                      )}

                      {/* Event Images */}
                      {(() => {
                        // Get event images if they exist
                        const eventImageUrls: string[] = [];

                        if ((event as any).images && (event as any).images.length > 0) {
                          (event as any).images.forEach((img: any) => {
                            if (img.file_path) {
                              // Convert file_path to full URL
                              const imageUrl = getImageUrl(img.file_path);
                              if (imageUrl) {
                                eventImageUrls.push(imageUrl);
                              }
                            }
                          });
                        }

                        return eventImageUrls.length > 0 ? (
                          <div style={{ marginBottom: '1rem' }}>
                            <FacebookImageGallery
                              images={eventImageUrls.filter(Boolean) as string[]}
                              altPrefix={event.title}
                              maxVisible={4}
                              onImageClick={(index) => {
                                console.log(`Clicked image ${index + 1} for event: ${event.title}`);
                              }}
                            />
                          </div>
                        ) : null;
                      })()}

                      {/* Event Details */}
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '1.5rem',
                        padding: '1rem',
                        background: 'rgba(59, 130, 246, 0.05)',
                        borderRadius: '12px',
                        fontSize: '0.875rem'
                      }}>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.5rem',
                          color: '#6b7280'
                        }}>
                          <Calendar size={16} />
                          <span>
                            {event.end_date && event.end_date !== event.event_date
                              ? `${new Date(event.event_date).toLocaleDateString()} - ${new Date(event.end_date).toLocaleDateString()}`
                              : new Date(event.event_date).toLocaleDateString()
                            }
                          </span>
                        </div>

                        {event.holiday_type_name && (
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.5rem',
                            color: '#6b7280'
                          }}>
                            <span style={{
                              padding: '0.25rem 0.5rem',
                              background: 'rgba(59, 130, 246, 0.1)',
                              borderRadius: '6px',
                              fontSize: '0.75rem',
                              fontWeight: '500'
                            }}>
                              {event.holiday_type_name}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </>
              )}

              {/* Announcements */}
              {displayAnnouncements.length > 0 && (
                <>
                  {displayAnnouncements.map(announcement => (
                    <div
                      key={`announcement-${announcement.announcement_id}`}
                      style={{
                        background: 'rgba(255, 255, 255, 0.95)',
                        borderRadius: '16px',
                        padding: '1.5rem',
                        border: announcement.is_pinned
                          ? '2px solid rgba(250, 204, 21, 0.3)'
                          : '1px solid rgba(0, 0, 0, 0.1)',
                        backdropFilter: 'blur(10px)',
                        boxShadow: announcement.is_pinned
                          ? '0 4px 20px rgba(250, 204, 21, 0.15)'
                          : '0 4px 20px rgba(0, 0, 0, 0.08)',
                        transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                        position: 'relative'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-2px)';
                        e.currentTarget.style.boxShadow = announcement.is_pinned
                          ? '0 8px 30px rgba(250, 204, 21, 0.25)'
                          : '0 8px 30px rgba(0, 0, 0, 0.12)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = announcement.is_pinned
                          ? '0 4px 20px rgba(250, 204, 21, 0.15)'
                          : '0 4px 20px rgba(0, 0, 0, 0.08)';
                      }}
                    >
                      {/* Pinned Badge */}
                      {announcement.is_pinned && (
                        <div style={{
                          position: 'absolute',
                          top: '-8px',
                          right: '1rem',
                          background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',
                          color: 'white',
                          padding: '0.25rem 0.75rem',
                          borderRadius: '12px',
                          fontSize: '0.75rem',
                          fontWeight: '600',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '0.25rem',
                          boxShadow: '0 2px 8px rgba(250, 204, 21, 0.3)'
                        }}>
                          <Pin size={12} />
                          Pinned
                        </div>
                      )}

                      {/* Announcement Header */}
                      <div style={{
                        display: 'flex',
                        alignItems: 'flex-start',
                        gap: '1rem',
                        marginBottom: '1rem'
                      }}>
                        {(() => {
                          if (announcement.is_alert) {
                            return (
                              <div style={{
                                width: '48px',
                                height: '48px',
                                background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                                borderRadius: '12px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                flexShrink: 0
                              }}>
                                <AlertTriangle size={24} color="white" />
                              </div>
                            );
                          } else {
                            const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();
                            const categoryStyle = getCategoryStyle(categoryName);
                            const IconComponent = categoryStyle.icon;

                            return (
                              <div style={{
                                width: '48px',
                                height: '48px',
                                background: categoryStyle.background,
                                borderRadius: '12px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                flexShrink: 0
                              }}>
                                <IconComponent size={24} color="white" />
                              </div>
                            );
                          }
                        })()}

                        <div style={{ flex: 1 }}>
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.75rem',
                            marginBottom: '0.5rem',
                            flexWrap: 'wrap'
                          }}>
                            {(() => {
                              if (announcement.is_alert) {
                                return (
                                  <span style={{
                                    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                                    color: 'white',
                                    fontSize: '0.75rem',
                                    fontWeight: '600',
                                    padding: '0.25rem 0.75rem',
                                    borderRadius: '20px',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.5px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '0.25rem'
                                  }}>
                                    <AlertTriangle size={12} color="white" />
                                    Alert
                                  </span>
                                );
                              } else {
                                const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();
                                const categoryStyle = getCategoryStyle(categoryName);
                                const IconComponent = categoryStyle.icon;

                                return (
                                  <span style={{
                                    background: categoryStyle.background,
                                    color: 'white',
                                    fontSize: '0.75rem',
                                    fontWeight: '600',
                                    padding: '0.25rem 0.75rem',
                                    borderRadius: '20px',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.5px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '0.25rem'
                                  }}>
                                    <IconComponent size={12} color="white" />
                                    {categoryName}
                                  </span>
                                );
                              }
                            })()}

                            {announcement.grade_level && (
                              <span style={{
                                background: 'rgba(59, 130, 246, 0.1)',
                                color: '#3b82f6',
                                fontSize: '0.75rem',
                                fontWeight: '500',
                                padding: '0.25rem 0.75rem',
                                borderRadius: '20px'
                              }}>
                                Grade {announcement.grade_level}
                              </span>
                            )}

                            <div style={{
                              color: '#6b7280',
                              fontSize: '0.875rem'
                            }}>
                              {new Date(announcement.created_at).toLocaleDateString('en-US', {
                                weekday: 'short',
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric'
                              })}
                            </div>
                          </div>

                          <h3 style={{
                            margin: '0 0 0.5rem 0',
                            fontSize: '1.25rem',
                            fontWeight: '700',
                            color: '#1f2937',
                            lineHeight: '1.3'
                          }}>
                            {announcement.title}
                          </h3>
                        </div>

                        {/* Admin Announcement Actions */}
                        <div style={{ display: 'flex', gap: '0.5rem' }}>
                          <button
                            onClick={() => navigate(`/admin/posts?edit=${announcement.announcement_id}`)}
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.25rem',
                              padding: '0.5rem 0.75rem',
                              background: 'rgba(34, 197, 94, 0.1)',
                              border: '1px solid rgba(34, 197, 94, 0.2)',
                              borderRadius: '8px',
                              color: '#22c55e',
                              fontSize: '0.75rem',
                              fontWeight: '500',
                              cursor: 'pointer',
                              transition: 'all 0.2s ease'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.background = 'rgba(34, 197, 94, 0.2)';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.background = 'rgba(34, 197, 94, 0.1)';
                            }}
                          >
                            <Edit size={12} />
                            Edit
                          </button>

                          <button
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.25rem',
                              padding: '0.5rem 0.75rem',
                              background: 'rgba(59, 130, 246, 0.1)',
                              border: '1px solid rgba(59, 130, 246, 0.2)',
                              borderRadius: '8px',
                              color: '#3b82f6',
                              fontSize: '0.75rem',
                              fontWeight: '500',
                              cursor: 'pointer',
                              transition: 'all 0.2s ease'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.background = 'rgba(59, 130, 246, 0.2)';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.background = 'rgba(59, 130, 246, 0.1)';
                            }}
                          >
                            <Eye size={12} />
                            Analytics
                          </button>
                        </div>
                      </div>

                      {/* Announcement Content */}
                      <div style={{
                        color: '#4b5563',
                        fontSize: '0.95rem',
                        lineHeight: '1.6',
                        marginBottom: '1rem'
                      }}>
                        {announcement.content}
                      </div>

                      {/* Images */}
                      {announcement.attachments && announcement.attachments.length > 0 && (
                        <ImageGallery
                          images={announcement.attachments}
                          altPrefix={announcement.title}
                          onImageClick={(index) => {
                            // Could open image viewer modal
                            console.log('View image:', index);
                          }}
                        />
                      )}

                      {/* Announcement Stats & Actions */}
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        padding: '1rem',
                        background: 'rgba(0, 0, 0, 0.02)',
                        borderRadius: '12px',
                        marginBottom: '1rem'
                      }}>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '1.5rem'
                        }}>
                          {/* Like Button */}
                          <button
                            onClick={() => handleLikeToggle(announcement)}
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.5rem',
                              background: 'none',
                              border: 'none',
                              color: announcement.user_reaction ? '#ef4444' : '#6b7280',
                              cursor: 'pointer',
                              padding: '0.5rem',
                              borderRadius: '8px',
                              transition: 'all 0.2s ease',
                              fontSize: '0.875rem',
                              fontWeight: '500'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.background = 'none';
                            }}
                          >
                            <Heart
                              size={18}
                              fill={announcement.user_reaction ? '#ef4444' : 'none'}
                            />
                            <span>{announcement.reaction_count || 0}</span>
                          </button>

                          {/* Comments Button */}
                          {announcement.allow_comments && (
                            <button
                              onClick={() => setShowComments(
                                showComments === announcement.announcement_id ? null : announcement.announcement_id
                              )}
                              style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '0.5rem',
                                background: 'none',
                                border: 'none',
                                color: showComments === announcement.announcement_id ? '#22c55e' : '#6b7280',
                                cursor: 'pointer',
                                padding: '0.5rem',
                                borderRadius: '8px',
                                transition: 'all 0.2s ease',
                                fontSize: '0.875rem',
                                fontWeight: '500'
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';
                                e.currentTarget.style.color = '#22c55e';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.background = 'none';
                                e.currentTarget.style.color = showComments === announcement.announcement_id ? '#22c55e' : '#6b7280';
                              }}
                            >
                              <MessageSquare size={18} />
                              <span>{announcement.comment_count || 0}</span>
                            </button>
                          )}

                          {/* Views Count */}
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.5rem',
                            color: '#6b7280',
                            fontSize: '0.875rem'
                          }}>
                            <Eye size={18} />
                            <span>{announcement.view_count || 0} views</span>
                          </div>
                        </div>

                        {/* Admin Stats */}
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '1rem',
                          fontSize: '0.75rem',
                          color: '#6b7280'
                        }}>
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.25rem'
                          }}>
                            <Users size={14} />
                            <span>Posted by {announcement.posted_by_name || 'Admin'}</span>
                          </div>

                          <div style={{
                            padding: '0.25rem 0.5rem',
                            background: announcement.status === 'published'
                              ? 'rgba(34, 197, 94, 0.1)'
                              : 'rgba(107, 114, 128, 0.1)',
                            color: announcement.status === 'published' ? '#22c55e' : '#6b7280',
                            borderRadius: '6px',
                            fontWeight: '500'
                          }}>
                            {announcement.status}
                          </div>
                        </div>
                      </div>

                      {/* Comments Section */}
                      {showComments === announcement.announcement_id && announcement.allow_comments && (
                        <div style={{
                          marginTop: '1rem',
                          paddingTop: '1rem',
                          borderTop: '1px solid rgba(0, 0, 0, 0.1)'
                        }}>
                          <AdminCommentSection
                            announcementId={announcement.announcement_id}
                            allowComments={announcement.allow_comments}
                            currentUserType="admin"
                          />
                        </div>
                      )}
                    </div>
                  ))}
                </>
              )}
            </div>
          )}
          </div>
        </div>
      </div>

      {/* Pinned Post Dialog */}
      {selectedPinnedPost && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          padding: '2rem'
        }}
        onClick={() => setSelectedPinnedPost(null)}
        >
          <div style={{
            backgroundColor: 'white',
            borderRadius: '16px',
            maxWidth: '600px',
            width: '100%',
            maxHeight: '80vh',
            overflow: 'auto',
            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'
          }}
          onClick={(e) => e.stopPropagation()}
          >
            {/* Dialog Header */}
            <div style={{
              padding: '1.5rem',
              borderBottom: '1px solid #e5e7eb',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem'
              }}>
                <Pin size={20} style={{ color: '#22c55e' }} />
                <h3 style={{
                  margin: 0,
                  fontSize: '1.25rem',
                  fontWeight: '600',
                  color: '#111827'
                }}>
                  Pinned Post
                </h3>
              </div>
              <button
                onClick={() => setSelectedPinnedPost(null)}
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: '1.5rem',
                  color: '#6b7280',
                  cursor: 'pointer',
                  padding: '0.25rem',
                  borderRadius: '4px',
                  transition: 'color 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.color = '#374151';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.color = '#6b7280';
                }}
              >
                ×
              </button>
            </div>

            {/* Dialog Content */}
            <div style={{ padding: '1.5rem' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem',
                marginBottom: '1rem'
              }}>
                {(() => {
                  const categoryName = (selectedPinnedPost.category_name || 'GENERAL').toUpperCase();
                  const categoryStyle = getCategoryStyle(categoryName);
                  const IconComponent = categoryStyle.icon;

                  return (
                    <span style={{
                      background: categoryStyle.background,
                      color: 'white',
                      fontSize: '0.75rem',
                      fontWeight: '600',
                      padding: '0.25rem 0.75rem',
                      borderRadius: '20px',
                      textTransform: 'uppercase',
                      letterSpacing: '0.5px',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '0.25rem'
                    }}>
                      <IconComponent size={12} color="white" />
                      {categoryName}
                    </span>
                  );
                })()}

                <span style={{
                  background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',
                  color: 'white',
                  padding: '0.25rem 0.75rem',
                  borderRadius: '12px',
                  fontSize: '0.75rem',
                  fontWeight: '600',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.25rem'
                }}>
                  <Pin size={12} />
                  PINNED
                </span>
              </div>

              <h2 style={{
                margin: '0 0 1rem 0',
                fontSize: '1.5rem',
                fontWeight: '700',
                color: '#111827',
                lineHeight: '1.3'
              }}>
                {selectedPinnedPost.title}
              </h2>

              <div style={{
                color: '#4b5563',
                fontSize: '1rem',
                lineHeight: '1.6',
                marginBottom: '1.5rem'
              }}>
                {selectedPinnedPost.content}
              </div>

              {/* Images */}
              {selectedPinnedPost.images && selectedPinnedPost.images.length > 0 && (
                <div style={{ marginBottom: '1.5rem' }}>
                  <ImageGallery
                    images={selectedPinnedPost.images}
                    altPrefix={selectedPinnedPost.title}
                    onImageClick={(index) => {
                      console.log('View image:', index);
                    }}
                  />
                </div>
              )}

              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '1rem',
                fontSize: '0.875rem',
                color: '#6b7280',
                paddingTop: '1rem',
                borderTop: '1px solid #e5e7eb'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}>
                  <Calendar size={16} />
                  <span>Published: {new Date(selectedPinnedPost.created_at).toLocaleDateString()}</span>
                </div>
                {selectedPinnedPost.author_name && (
                  <div>
                    By: {selectedPinnedPost.author_name}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminNewsfeed;
