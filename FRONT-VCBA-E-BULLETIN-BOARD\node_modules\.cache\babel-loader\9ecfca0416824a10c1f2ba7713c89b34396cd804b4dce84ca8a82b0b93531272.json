{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 6h18\",\n  key: \"d0wm0j\"\n}], [\"path\", {\n  d: \"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6\",\n  key: \"4alrt4\"\n}], [\"path\", {\n  d: \"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2\",\n  key: \"v07s0e\"\n}], [\"line\", {\n  x1: \"10\",\n  x2: \"10\",\n  y1: \"11\",\n  y2: \"17\",\n  key: \"1uufr5\"\n}], [\"line\", {\n  x1: \"14\",\n  x2: \"14\",\n  y1: \"11\",\n  y2: \"17\",\n  key: \"xtxkd\"\n}]];\nconst Trash2 = createLucideIcon(\"trash-2\", __iconNode);\nexport { __iconNode, Trash2 as default };\n//# sourceMappingURL=trash-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}