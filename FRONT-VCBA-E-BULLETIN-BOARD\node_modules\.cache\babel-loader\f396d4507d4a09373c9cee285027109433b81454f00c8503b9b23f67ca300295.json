{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M19 15v-2a2 2 0 1 0-4 0v2\",\n  key: \"h3d1vz\"\n}], [\"path\", {\n  d: \"M9 17H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v3.5\",\n  key: \"xsnnhn\"\n}], [\"rect\", {\n  x: \"13\",\n  y: \"15\",\n  width: \"8\",\n  height: \"5\",\n  rx: \"1\",\n  key: \"1ccwuk\"\n}]];\nconst MessageSquareLock = createLucideIcon(\"message-square-lock\", __iconNode);\nexport { __iconNode, MessageSquareLock as default };\n//# sourceMappingURL=message-square-lock.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}