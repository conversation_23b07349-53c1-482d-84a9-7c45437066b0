{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 4v16\",\n  key: \"6qkkli\"\n}]];\nconst Tally1 = createLucideIcon(\"tally-1\", __iconNode);\nexport { __iconNode, Tally1 as default };\n//# sourceMappingURL=tally-1.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}