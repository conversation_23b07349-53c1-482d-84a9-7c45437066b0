import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAnnouncements, useCategories } from '../../hooks/useAnnouncements';
import { announcementService, calendarService } from '../../services';
import CommentSection from '../../components/student/CommentSection';
import ImageLightbox from '../../components/common/ImageLightbox';
import type { Announcement } from '../../types/announcement.types';
import type { AnnouncementAttachment } from '../../services/announcementService';
import type { CalendarEvent } from '../../types/calendar.types';
import { getImageUrl, API_BASE_URL } from '../../config/constants';
import {
  Home,
  Search,
  Pin,
  Calendar,
  MessageSquare,
  Heart,
  Filter,
  MapPin,
  BookOpen,
  Users,
  PartyPopper,
  AlertTriangle,
  Clock,
  Trophy,
  Briefcase,
  GraduationCap,
  Flag,
  Coffee,
  Plane
} from 'lucide-react';

// Custom hook for CORS-safe image loading
const useImageLoader = (imagePath: string | null) => {
  const [imageUrl, setImageUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!imagePath) {
      setImageUrl(null);
      return;
    }

    const loadImage = async () => {
      setLoading(true);
      setError(null);

      try {
        const fullUrl = getImageUrl(imagePath);
        if (!fullUrl) {
          throw new Error('Invalid image path');
        }

        console.log('🔄 Fetching image via CORS-safe method:', fullUrl);

        // Fetch image as blob to bypass CORS restrictions
        const response = await fetch(fullUrl, {
          method: 'GET',
          headers: {
            'Origin': window.location.origin,
          },
          mode: 'cors',
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const blob = await response.blob();
        const objectUrl = URL.createObjectURL(blob);
        setImageUrl(objectUrl);

        console.log('✅ Image loaded successfully via fetch');

      } catch (err) {
        console.error('❌ Image fetch failed:', err);
        setError(err instanceof Error ? err.message : 'Failed to load image');
      } finally {
        setLoading(false);
      }
    };

    loadImage();

    // Cleanup object URL on unmount
    return () => {
      if (imageUrl && imageUrl.startsWith('blob:')) {
        URL.revokeObjectURL(imageUrl);
      }
    };
  }, [imagePath]);

  return { imageUrl, loading, error };
};

// Reusable CORS-safe image component
interface ImageDisplayProps {
  imagePath: string | null;
  alt: string;
  style?: React.CSSProperties;
  className?: string;
  onLoad?: (e: React.SyntheticEvent<HTMLImageElement>) => void;
  onMouseEnter?: (e: React.MouseEvent<HTMLImageElement>) => void;
  onMouseLeave?: (e: React.MouseEvent<HTMLImageElement>) => void;
}

const ImageDisplay: React.FC<ImageDisplayProps> = ({
  imagePath,
  alt,
  style,
  className,
  onLoad,
  onMouseEnter,
  onMouseLeave
}) => {
  const { imageUrl, loading, error } = useImageLoader(imagePath);

  if (loading) {
    return (
      <div style={{
        ...style,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f8fafc',
        color: '#64748b'
      }} className={className}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>⏳</div>
          <div style={{ fontSize: '0.875rem' }}>Loading...</div>
        </div>
      </div>
    );
  }

  if (error || !imageUrl) {
    return (
      <div style={{
        ...style,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f8fafc',
        color: '#64748b',
        border: '2px dashed #cbd5e1'
      }} className={className}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>🖼️</div>
          <div style={{ fontSize: '0.875rem', fontWeight: '500' }}>Image unavailable</div>
          {error && (
            <div style={{ fontSize: '0.75rem', marginTop: '0.25rem', color: '#9ca3af' }}>
              {error}
            </div>
          )}
        </div>
      </div>
    );
  }

  return (
    <img
      src={imageUrl}
      alt={alt}
      style={style}
      className={className}
      onLoad={(e) => {
        console.log('✅ Image rendered successfully via CORS-safe method');
        onLoad?.(e);
      }}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
    />
  );
};

// Facebook-style image gallery component
interface FacebookImageGalleryProps {
  images: string[];
  altPrefix: string;
  maxVisible?: number;
  onImageClick?: (index: number) => void;
}

const FacebookImageGallery: React.FC<FacebookImageGalleryProps> = ({
  images,
  altPrefix,
  maxVisible = 4,
  onImageClick
}) => {
  if (!images || images.length === 0) return null;

  const visibleImages = images.slice(0, maxVisible);
  const remainingCount = images.length - maxVisible;

  const getImageStyle = (index: number, total: number): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      width: '100%',
      height: '100%',
      objectFit: 'cover',
      cursor: 'pointer',
      transition: 'transform 0.2s ease, filter 0.2s ease',
      borderRadius: index === 0 && total === 1 ? '12px' : '8px'
    };

    return baseStyle;
  };

  const getContainerStyle = (index: number, total: number): React.CSSProperties => {
    const baseStyle: React.CSSProperties = {
      position: 'relative',
      overflow: 'hidden',
      backgroundColor: '#f3f4f6'
    };

    if (total === 1) {
      return {
        ...baseStyle,
        width: '100%',
        height: '400px',
        borderRadius: '12px'
      };
    }

    if (total === 2) {
      return {
        ...baseStyle,
        width: '50%',
        height: '300px',
        borderRadius: '8px'
      };
    }

    if (total === 3) {
      if (index === 0) {
        return {
          ...baseStyle,
          width: '60%',
          height: '300px',
          borderRadius: '8px'
        };
      } else {
        return {
          ...baseStyle,
          width: '100%',
          height: '148px',
          borderRadius: '8px'
        };
      }
    }

    // 4+ images
    if (index === 0) {
      return {
        ...baseStyle,
        width: '60%',
        height: '300px',
        borderRadius: '8px'
      };
    } else {
      return {
        ...baseStyle,
        width: '100%',
        height: '96px',
        borderRadius: '8px'
      };
    }
  };

  const renderOverlay = (index: number, count: number) => {
    if (index === maxVisible - 1 && count > 0) {
      return (
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontSize: '1.5rem',
          fontWeight: '600',
          borderRadius: '8px'
        }}>
          +{count}
        </div>
      );
    }
    return null;
  };

  return (
    <div style={{
      display: 'flex',
      gap: '4px',
      width: '100%',
      marginBottom: '1rem'
    }}>
      {/* Main image or left side */}
      <div style={getContainerStyle(0, visibleImages.length)}>
        <ImageDisplay
          imagePath={visibleImages[0]}
          alt={`${altPrefix} - Image 1`}
          style={getImageStyle(0, visibleImages.length)}
          onMouseEnter={(e) => {
            e.currentTarget.style.transform = 'scale(1.02)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.transform = 'scale(1)';
          }}
        />
        {onImageClick && (
          <div
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              cursor: 'pointer'
            }}
            onClick={() => onImageClick(0)}
          />
        )}
      </div>

      {/* Right side images (for 2+ images) */}
      {visibleImages.length > 1 && (
        <div style={{
          display: 'flex',
          flexDirection: 'column',
          gap: '4px',
          width: visibleImages.length === 2 ? '50%' : '40%'
        }}>
          {visibleImages.slice(1).map((image, idx) => {
            const actualIndex = idx + 1;
            return (
              <div
                key={actualIndex}
                style={getContainerStyle(actualIndex, visibleImages.length)}
              >
                <ImageDisplay
                  imagePath={image}
                  alt={`${altPrefix} - Image ${actualIndex + 1}`}
                  style={getImageStyle(actualIndex, visibleImages.length)}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.transform = 'scale(1.02)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.transform = 'scale(1)';
                  }}
                />
                {renderOverlay(actualIndex, remainingCount)}
                {onImageClick && (
                  <div
                    style={{
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      right: 0,
                      bottom: 0,
                      cursor: 'pointer'
                    }}
                    onClick={() => onImageClick(actualIndex)}
                  />
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

const StudentNewsfeed: React.FC = () => {
  const navigate = useNavigate();

  // Category styling function
  const getCategoryStyle = (categoryName: string) => {
    const styles = {
      'ACADEMIC': {
        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',
        icon: BookOpen
      },
      'GENERAL': {
        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',
        icon: Users
      },
      'EVENTS': {
        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
        icon: PartyPopper
      },
      'EMERGENCY': {
        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
        icon: AlertTriangle
      },
      'SPORTS': {
        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
        icon: Trophy
      },
      'DEADLINES': {
        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
        icon: Clock
      }
    };

    return styles[categoryName as keyof typeof styles] || styles['GENERAL'];
  };

  // Holiday type styling function
  const getHolidayTypeStyle = (holidayTypeName: string) => {
    const styles = {
      'NATIONAL HOLIDAY': {
        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',
        icon: Flag
      },
      'SCHOOL EVENT': {
        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
        icon: GraduationCap
      },
      'ACADEMIC BREAK': {
        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
        icon: Coffee
      },
      'SPORTS EVENT': {
        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
        icon: Trophy
      },
      'FIELD TRIP': {
        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',
        icon: Plane
      },
      'MEETING': {
        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',
        icon: Briefcase
      }
    };

    return styles[holidayTypeName as keyof typeof styles] || styles['SCHOOL EVENT'];
  };

  // Filter states
  const [filterCategory, setFilterCategory] = useState<string>('');
  const [filterGradeLevel, setFilterGradeLevel] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  
  // UI states
  const [showComments, setShowComments] = useState<number | null>(null);
  const [newComment, setNewComment] = useState<{ [key: number]: string }>({});
  const [submittingComment, setSubmittingComment] = useState<number | null>(null);
  const [selectedPinnedPost, setSelectedPinnedPost] = useState<any | null>(null);

  // Lightbox states
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxImages, setLightboxImages] = useState<string[]>([]);
  const [lightboxInitialIndex, setLightboxInitialIndex] = useState(0);

  // Data states
  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);
  const [calendarLoading, setCalendarLoading] = useState(false);
  const [calendarError, setCalendarError] = useState<string | undefined>();
  const [announcements, setAnnouncements] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | undefined>();
  const [pinnedAnnouncements, setPinnedAnnouncements] = useState<any[]>([]);

  const { categories } = useCategories();

  // Grade levels for dropdown (11-12)
  const gradeLevels = Array.from({ length: 2 }, (_, i) => i + 11); // [11, 12]

  // Open lightbox function
  const openLightbox = (imageUrls: string[], initialIndex: number) => {
    setLightboxImages(imageUrls);
    setLightboxInitialIndex(initialIndex);
    setLightboxOpen(true);
  };

  // Fetch published announcements with images
  const fetchPublishedAnnouncements = async () => {
    try {
      setLoading(true);
      setError(undefined);

      const response = await fetch(`${API_BASE_URL}/api/announcements?status=published&page=1&limit=50&sort_by=created_at&sort_order=DESC`);
      const data = await response.json();

      if (data.success && data.data) {
        const announcementsData = data.data.announcements || [];

        // The announcements already include attachments/images from the API
        // No need to fetch images separately
        setAnnouncements(announcementsData);

        // Separate pinned announcements
        const pinned = announcementsData.filter((ann: any) => ann.is_pinned === 1);
        setPinnedAnnouncements(pinned);
      } else {
        setError('Failed to load announcements');
      }
    } catch (err: any) {
      console.error('Error fetching announcements:', err);
      setError(err.message || 'Failed to load announcements');
    } finally {
      setLoading(false);
    }
  };

  // Fetch calendar events
  const fetchCalendarEvents = async () => {
    try {
      setCalendarLoading(true);
      setCalendarError(undefined);

      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`);
      const data = await response.json();

      if (data.success && data.data) {
        const eventsData = data.data.events || data.data || [];

        // Fetch images for each event
        const eventsWithImages = await Promise.all(
          eventsData.map(async (event: any) => {
            try {
              const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`);
              const imageData = await imageResponse.json();

              if (imageData.success && imageData.data) {
                event.images = imageData.data.attachments || [];
              } else {
                event.images = [];
              }
            } catch (imgErr) {
              console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);
              event.images = [];
            }
            return event;
          })
        );

        setCalendarEvents(eventsWithImages);
      } else {
        setCalendarError('Failed to load calendar events');
      }
    } catch (err: any) {
      console.error('Error fetching calendar events:', err);
      setCalendarError(err.message || 'Failed to load calendar events');
    } finally {
      setCalendarLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    fetchPublishedAnnouncements();
    fetchCalendarEvents();
  }, []);

  // Handle like/unlike functionality
  const handleLikeToggle = async (announcement: any) => {
    try {
      if (announcement.user_reaction) {
        await announcementService.removeReaction(announcement.announcement_id);
      } else {
        const response = await fetch(`${API_BASE_URL}/api/announcements/${announcement.announcement_id}/reactions`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            reaction_type_id: 1,
            notify_admin: true
          })
        });

        if (!response.ok) {
          throw new Error('Failed to add reaction');
        }
      }
      
      // Refresh data
      await fetchPublishedAnnouncements();
    } catch (error) {
      console.error('Error toggling like:', error);
    }
  };

  // Handle comment submission
  const handleCommentSubmit = async (announcementId: number, commentText: string) => {
    if (!commentText.trim()) return;

    try {
      setSubmittingComment(announcementId);

      const response = await fetch(`${API_BASE_URL}/api/announcements/${announcementId}/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: commentText,
          commenter_name: 'Student User',
          commenter_email: '<EMAIL>',
          notify_admin: true
        })
      });

      if (response.ok) {
        setNewComment(prev => ({ ...prev, [announcementId]: '' }));
        await fetchPublishedAnnouncements();
      } else {
        console.error('Failed to submit comment');
      }
    } catch (error) {
      console.error('Error submitting comment:', error);
    } finally {
      setSubmittingComment(null);
    }
  };

  // Filter announcements
  const filteredAnnouncements = announcements.filter(announcement => {
    const matchesSearch = !searchTerm ||
      announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      announcement.content.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = !filterCategory || announcement.category_id.toString() === filterCategory;
    
    // Note: Grade level filtering would require additional backend support
    // For now, we'll show all announcements regardless of grade level filter
    
    return matchesSearch && matchesCategory;
  });

  // Filter calendar events with date-based filtering
  const filteredCalendarEvents = calendarEvents.filter(event => {
    const matchesSearch = !searchTerm ||
      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase()));

    // Only show events that are published and on or after today's date
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day

    const eventDate = new Date(event.event_date);
    eventDate.setHours(0, 0, 0, 0); // Reset time to start of day

    const isEventDateValid = eventDate >= today;
    const isPublished = (event as any).is_published === 1;

    return matchesSearch && isEventDateValid && isPublished;
  });

  // Combine and sort all content by date (most recent first)
  const displayAnnouncements = filteredAnnouncements;
  const displayEvents = filteredCalendarEvents;

  // Create combined content array for better chronological display
  const combinedContent = [
    ...displayAnnouncements.map(item => ({ ...item, type: 'announcement', sortDate: new Date(item.created_at) })),
    ...displayEvents.map(item => ({ ...item, type: 'event', sortDate: new Date(item.event_date) }))
  ].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());

  return (
    <>
      {/* CSS Animations */}
      <style>
        {`
          html {
            scroll-behavior: smooth;
          }

          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }

          @media (max-width: 768px) {
            .mobile-hide { display: none !important; }
            .mobile-full { width: 100% !important; margin-left: 0 !important; }
            .mobile-stack {
              flex-direction: column !important;
              gap: 0.75rem !important;
            }
            .mobile-grid { grid-template-columns: 1fr !important; }
          }

          @media (max-width: 480px) {
            .mobile-small-padding { padding: 0.75rem !important; }
            .mobile-small-text { font-size: 0.8rem !important; }
            .mobile-compact-header {
              padding: 0.75rem 1rem !important;
            }
            .mobile-compact-title {
              font-size: 1.25rem !important;
            }
            .mobile-compact-search {
              max-width: 200px !important;
            }
          }
        `}
      </style>

      <div style={{
        display: 'flex',
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #f0f9ff 0%, #fefce8 100%)',
        scrollBehavior: 'smooth'
      }}>

      {/* Main Content Area */}
      <div
        className="mobile-full"
        style={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          minHeight: '100vh',
          width: '100%'
        }}>
        {/* Modern Student Header */}
        <header style={{
          background: 'white',
          borderBottom: '1px solid #e5e7eb',
          position: 'sticky',
          top: 0,
          zIndex: 100,
          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
        }}>
          <div style={{
            width: '100%',
            padding: '0 2rem',
            height: '72px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between'
          }}>
            {/* Left Section: Logo + Page Title */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '1.5rem',
              minWidth: '300px'
            }}>
              <img
                src="/logo/vcba1.png"
                alt="VCBA Logo"
                style={{
                  width: '48px',
                  height: '48px',
                  objectFit: 'contain'
                }}
              />
              <div>
                <h1 style={{
                  margin: 0,
                  fontSize: '1.5rem',
                  fontWeight: '700',
                  color: '#111827',
                  lineHeight: '1.2'
                }}>
                  VCBA E-Bulletin Board
                </h1>
                <p style={{
                  margin: 0,
                  fontSize: '0.875rem',
                  color: '#6b7280'
                }}>
                  Student Newsfeed
                </p>
              </div>
            </div>

            {/* Center Section: Search */}
            <div style={{
              flex: 1,
              maxWidth: '500px',
              margin: '0 2rem'
            }}>
              <div style={{ position: 'relative' }}>
                <Search
                  size={20}
                  style={{
                    position: 'absolute',
                    left: '1rem',
                    top: '50%',
                    transform: 'translateY(-50%)',
                    color: '#9ca3af'
                  }}
                />
                <input
                  type="text"
                  placeholder="Search post"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  style={{
                    width: '100%',
                    height: '44px',
                    padding: '0 1rem 0 3rem',
                    border: '1px solid #d1d5db',
                    borderRadius: '12px',
                    background: '#f9fafb',
                    color: '#374151',
                    fontSize: '0.875rem',
                    outline: 'none',
                    transition: 'all 0.2s ease'
                  }}
                  onFocus={(e) => {
                    e.target.style.borderColor = '#3b82f6';
                    e.target.style.background = 'white';
                    e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';
                  }}
                  onBlur={(e) => {
                    e.target.style.borderColor = '#d1d5db';
                    e.target.style.background = '#f9fafb';
                    e.target.style.boxShadow = 'none';
                  }}
                />
              </div>
            </div>

            {/* Right Section: Filters + Actions */}
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: '1rem',
              minWidth: '400px',
              justifyContent: 'flex-end'
            }}>
              {/* Filters Group */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                padding: '0.5rem',
                background: '#f9fafb',
                borderRadius: '12px',
                border: '1px solid #e5e7eb'
              }}>
                <select
                  value={filterCategory}
                  onChange={(e) => setFilterCategory(e.target.value)}
                  style={{
                    padding: '0.5rem 0.75rem',
                    border: 'none',
                    borderRadius: '8px',
                    background: 'white',
                    color: '#374151',
                    fontSize: '0.875rem',
                    outline: 'none',
                    cursor: 'pointer',
                    minWidth: '110px'
                  }}
                >
                  <option value="">All Categories</option>
                  {categories.map(category => (
                    <option key={category.category_id} value={category.category_id.toString()}>
                      {category.name}
                    </option>
                  ))}
                </select>

                <select
                  value={filterGradeLevel}
                  onChange={(e) => setFilterGradeLevel(e.target.value)}
                  style={{
                    padding: '0.5rem 0.75rem',
                    border: 'none',
                    borderRadius: '8px',
                    background: 'white',
                    color: '#374151',
                    fontSize: '0.875rem',
                    outline: 'none',
                    cursor: 'pointer',
                    minWidth: '100px'
                  }}
                >
                  <option value="">All Grades</option>
                  {gradeLevels.map(grade => (
                    <option key={grade} value={grade.toString()}>
                      Grade {grade}
                    </option>
                  ))}
                </select>

                {(searchTerm || filterCategory || filterGradeLevel) && (
                  <button
                    onClick={() => {
                      setSearchTerm('');
                      setFilterCategory('');
                      setFilterGradeLevel('');
                    }}
                    style={{
                      padding: '0.5rem 0.75rem',
                      border: 'none',
                      borderRadius: '8px',
                      background: '#ef4444',
                      color: 'white',
                      fontSize: '0.875rem',
                      fontWeight: '500',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background = '#dc2626';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background = '#ef4444';
                    }}
                  >
                    Clear
                  </button>
                )}
              </div>



              {/* Dashboard Button */}
              <button
                onClick={() => navigate('/student/dashboard')}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  padding: '0.5rem 1rem',
                  border: '1px solid #d1d5db',
                  borderRadius: '8px',
                  background: 'white',
                  color: '#374151',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.borderColor = '#3b82f6';
                  e.currentTarget.style.color = '#3b82f6';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.borderColor = '#d1d5db';
                  e.currentTarget.style.color = '#374151';
                }}
              >
                <Home size={16} />
                Dashboard
              </button>
            </div>
          </div>
        </header>

        {/* Main Content Layout */}
        <div style={{
          flex: 1,
          display: 'flex',
          gap: '1.5rem',
          padding: '1.5rem 2rem',
          background: 'transparent',
          alignItems: 'flex-start'
        }}>
          {/* Left Sidebar: Pinned Posts */}
          <div style={{
            width: '280px',
            flexShrink: 0
          }}>
            <div style={{
              background: 'white',
              borderRadius: '14px',
              border: '1px solid #e5e7eb',
              overflow: 'hidden',
              position: 'sticky',
              top: '80px',
              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
            }}>
              {/* Pinned Posts Header */}
              <div style={{
                padding: '1rem 1.25rem 0.75rem',
                borderBottom: '1px solid #f3f4f6',
                background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.75rem',
                  marginBottom: '0.5rem'
                }}>
                  <Pin size={20} style={{ color: 'white' }} />
                  <h3 style={{
                    margin: 0,
                    fontSize: '1.125rem',
                    fontWeight: '600',
                    color: 'white'
                  }}>
                    Important Updates
                  </h3>
                </div>
                <p style={{
                  margin: 0,
                  fontSize: '0.875rem',
                  color: 'rgba(255, 255, 255, 0.8)'
                }}>
                  Don't miss these announcements
                </p>
              </div>

              {/* Pinned Posts List */}
              <div style={{ padding: '0.75rem' }}>
                {pinnedAnnouncements.length > 0 ? (
                  <>
                    {pinnedAnnouncements.slice(0, 3).map((announcement, index) => {
                      // Handle alert announcements with special styling
                      let colors: [string, string, string];
                      if (announcement.is_alert) {
                        colors = ['#fee2e2', '#fecaca', '#ef4444']; // Red alert colors
                      } else {
                        const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();

                        // Create gradient background based on category
                        const gradientColors: Record<string, [string, string, string]> = {
                          'ACADEMIC': ['#dbeafe', '#bfdbfe', '#3b82f6'],
                          'EVENTS': ['#fef3c7', '#fde68a', '#f59e0b'],
                          'EMERGENCY': ['#fee2e2', '#fecaca', '#ef4444'],
                          'SPORTS': ['#dcfce7', '#bbf7d0', '#22c55e'],
                          'DEADLINES': ['#ede9fe', '#ddd6fe', '#8b5cf6'],
                          'GENERAL': ['#f3f4f6', '#e5e7eb', '#6b7280']
                        };

                        colors = gradientColors[categoryName] || gradientColors['GENERAL'];
                      }

                      return (
                        <div
                          key={announcement.announcement_id}
                          style={{
                            padding: '0.75rem',
                            background: `linear-gradient(135deg, ${colors[0]} 0%, ${colors[1]} 100%)`,
                            borderRadius: '10px',
                            border: `1px solid ${colors[2]}`,
                            marginBottom: '0.75rem',
                            cursor: 'pointer',
                            transition: 'all 0.2s ease'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.transform = 'translateY(-2px)';
                            e.currentTarget.style.boxShadow = `0 8px 25px ${colors[2]}30`;
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.transform = 'translateY(0)';
                            e.currentTarget.style.boxShadow = 'none';
                          }}
                          onClick={() => setSelectedPinnedPost(announcement)}
                        >
                          <div style={{
                            display: 'flex',
                            alignItems: 'flex-start',
                            gap: '0.75rem'
                          }}>
                            <div style={{
                              width: '10px',
                              height: '10px',
                              background: colors[2],
                              borderRadius: '50%',
                              marginTop: '0.5rem',
                              flexShrink: 0,
                              boxShadow: `0 0 0 3px ${colors[2]}30`
                            }} />
                            <div style={{ flex: 1 }}>
                              <h4 style={{
                                margin: '0 0 0.5rem 0',
                                fontSize: '0.875rem',
                                fontWeight: '600',
                                color: colors[2],
                                lineHeight: '1.4'
                              }}>
                                {announcement.title}
                              </h4>
                              <p style={{
                                margin: '0 0 0.5rem 0',
                                fontSize: '0.8rem',
                                color: colors[2],
                                lineHeight: '1.4'
                              }}>
                                {announcement.content.length > 60
                                  ? `${announcement.content.substring(0, 60)}...`
                                  : announcement.content}
                              </p>
                              <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: '0.5rem',
                                fontSize: '0.75rem',
                                color: colors[2]
                              }}>
                                <Calendar size={12} />
                                <span>{new Date(announcement.created_at).toLocaleDateString()}</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}


                    {pinnedAnnouncements.length > 3 && (
                      <button style={{
                        width: '100%',
                        padding: '0.75rem',
                        border: '1px solid #e5e7eb',
                        borderRadius: '12px',
                        background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',
                        color: '#3b82f6',
                        fontSize: '0.875rem',
                        fontWeight: '600',
                        cursor: 'pointer',
                        transition: 'all 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.background = 'linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)';
                        e.currentTarget.style.borderColor = '#3b82f6';
                        e.currentTarget.style.transform = 'translateY(-1px)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.background = 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)';
                        e.currentTarget.style.borderColor = '#e5e7eb';
                        e.currentTarget.style.transform = 'translateY(0)';
                      }}>
                        📌 View All {pinnedAnnouncements.length} Important Updates
                      </button>
                    )}
                  </>
                ) : (
                  <div style={{
                    padding: '2rem 1rem',
                    textAlign: 'center',
                    color: '#6b7280'
                  }}>
                    <Pin size={24} style={{ marginBottom: '0.5rem', opacity: 0.5 }} />
                    <p style={{ margin: 0, fontSize: '0.875rem' }}>
                      No pinned posts available
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Content: Main Feed */}
          <div style={{ flex: 1, minWidth: 0 }}>
          {/* Loading State */}
          {(loading || calendarLoading) && (
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              padding: '3rem'
            }}>
              <div style={{
                width: '2.5rem',
                height: '2.5rem',
                border: '3px solid #e5e7eb',
                borderTop: '3px solid #3b82f6',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }}></div>
            </div>
          )}

          {/* Error Messages */}
          {(error || calendarError) && (
            <div style={{
              background: 'rgba(239, 68, 68, 0.1)',
              border: '1px solid rgba(239, 68, 68, 0.2)',
              borderRadius: '12px',
              padding: '1rem',
              marginBottom: '1.5rem',
              color: '#dc2626'
            }}>
              {error && <div>Announcements: {error}</div>}
              {calendarError && <div>Calendar: {calendarError}</div>}
            </div>
          )}

          {/* No Content */}
          {!loading && !calendarLoading && displayAnnouncements.length === 0 && displayEvents.length === 0 && (
            <div style={{
              background: 'rgba(255, 255, 255, 0.8)',
              borderRadius: '16px',
              padding: '3rem',
              textAlign: 'center',
              border: '1px solid rgba(0, 0, 0, 0.1)',
              backdropFilter: 'blur(10px)'
            }}>
              <div style={{
                marginBottom: '1rem'
              }}>
                <Filter size={48} color="#9ca3af" />
              </div>
              <h3 style={{
                color: '#374151',
                fontSize: '1.25rem',
                fontWeight: '600',
                margin: '0 0 0.5rem 0'
              }}>
                No updates found
              </h3>
              <p style={{
                color: '#6b7280',
                margin: 0
              }}>
                {searchTerm || filterCategory || filterGradeLevel
                  ? 'Try adjusting your filters to see more content.'
                  : 'Check back later for new updates.'}
              </p>
            </div>
          )}

          {/* Content Feed */}
          {!loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && (
            <div style={{
              maxWidth: '1200px',
              margin: '0 auto',
              display: 'flex',
              flexDirection: 'column',
              gap: '1.5rem'
            }}>
              {/* Calendar Events */}
              {displayEvents.length > 0 && (
                <>
                  {displayEvents.map(event => (
                    <div
                      key={`event-${event.calendar_id}`}
                      style={{
                        background: 'rgba(255, 255, 255, 0.95)',
                        borderRadius: '16px',
                        padding: '1.5rem',
                        border: '1px solid rgba(0, 0, 0, 0.1)',
                        backdropFilter: 'blur(10px)',
                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',
                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-2px)';
                        e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';
                      }}
                    >
                      {/* Event Header */}
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '1rem',
                        marginBottom: '1rem'
                      }}>
                        {(() => {
                          const holidayTypeName = (event.holiday_type_name || 'SCHOOL EVENT').toUpperCase();
                          const holidayStyle = getHolidayTypeStyle(holidayTypeName);
                          const IconComponent = holidayStyle.icon;

                          return (
                            <div style={{
                              width: '50px',
                              height: '50px',
                              borderRadius: '12px',
                              background: holidayStyle.background,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center'
                            }}>
                              <IconComponent size={20} color="white" />
                            </div>
                          );
                        })()}
                        <div style={{ flex: 1 }}>
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.5rem',
                            marginBottom: '0.25rem'
                          }}>
                            {(() => {
                              const holidayTypeName = (event.holiday_type_name || 'SCHOOL EVENT').toUpperCase();
                              const holidayStyle = getHolidayTypeStyle(holidayTypeName);
                              const IconComponent = holidayStyle.icon;

                              return (
                                <span style={{
                                  background: holidayStyle.background,
                                  color: 'white',
                                  padding: '0.25rem 0.75rem',
                                  borderRadius: '12px',
                                  fontSize: '0.75rem',
                                  fontWeight: '600',
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: '0.25rem'
                                }}>
                                  <IconComponent size={12} color="white" />
                                  {holidayTypeName}
                                </span>
                              );
                            })()}
                          </div>
                          <div style={{
                            color: '#9ca3af',
                            fontSize: '0.8rem'
                          }}>
                            {new Date(event.event_date).toLocaleDateString('en-US', {
                              weekday: 'long',
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </div>
                        </div>
                      </div>

                      {/* Event Content */}
                      <h3 style={{
                        margin: '0 0 0.75rem 0',
                        color: '#1f2937',
                        fontSize: '1.25rem',
                        fontWeight: '700',
                        lineHeight: '1.3'
                      }}>
                        {event.title}
                      </h3>

                      {event.description && (
                        <p style={{
                          margin: '0 0 1rem 0',
                          color: '#4b5563',
                          fontSize: '0.95rem',
                          lineHeight: '1.6'
                        }}>
                          {event.description}
                        </p>
                      )}

                      {/* Event Images */}
                      {(() => {
                        // Get event images if they exist
                        const eventImageUrls: string[] = [];

                        if ((event as any).images && (event as any).images.length > 0) {
                          (event as any).images.forEach((img: any) => {
                            if (img.file_path) {
                              // Convert file_path to full URL
                              const imageUrl = getImageUrl(img.file_path);
                              if (imageUrl) {
                                eventImageUrls.push(imageUrl);
                              }
                            }
                          });
                        }

                        return eventImageUrls.length > 0 ? (
                          <div style={{ marginBottom: '1rem' }}>
                            <FacebookImageGallery
                              images={eventImageUrls.filter(Boolean) as string[]}
                              altPrefix={event.title}
                              maxVisible={4}
                              onImageClick={(index) => {
                                const filteredImages = eventImageUrls.filter(Boolean) as string[];
                                openLightbox(filteredImages, index);
                              }}
                            />
                          </div>
                        ) : null;
                      })()}

                      {/* Event Footer */}
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        paddingTop: '1rem',
                        borderTop: '1px solid rgba(0, 0, 0, 0.1)'
                      }}>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '1rem',
                          color: '#6b7280',
                          fontSize: '0.875rem'
                        }}>
                          <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                            <MapPin size={14} color="#6b7280" />
                            School Event
                          </span>
                          {event.end_date && event.end_date !== event.event_date && (
                            <span>
                              Until {new Date(event.end_date).toLocaleDateString()}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </>
              )}

              {/* Announcements */}
              {displayAnnouncements.length > 0 && (
                <>
                  {displayAnnouncements.map(announcement => (
                    <div
                      key={`announcement-${announcement.announcement_id}`}
                      style={{
                        background: 'rgba(255, 255, 255, 0.95)',
                        borderRadius: '16px',
                        padding: '1.5rem',
                        border: announcement.is_alert
                          ? '2px solid rgba(239, 68, 68, 0.3)'
                          : '1px solid rgba(0, 0, 0, 0.1)',
                        backdropFilter: 'blur(10px)',
                        boxShadow: announcement.is_alert
                          ? '0 4px 20px rgba(239, 68, 68, 0.15)'
                          : '0 4px 20px rgba(0, 0, 0, 0.08)',
                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.transform = 'translateY(-2px)';
                        e.currentTarget.style.boxShadow = announcement.is_alert
                          ? '0 8px 30px rgba(239, 68, 68, 0.25)'
                          : '0 8px 30px rgba(0, 0, 0, 0.12)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.transform = 'translateY(0)';
                        e.currentTarget.style.boxShadow = announcement.is_alert
                          ? '0 4px 20px rgba(239, 68, 68, 0.15)'
                          : '0 4px 20px rgba(0, 0, 0, 0.08)';
                      }}
                    >
                      {/* Announcement Header */}
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '1rem',
                        marginBottom: '1rem'
                      }}>
                        {(() => {
                          if (announcement.is_alert) {
                            return (
                              <div style={{
                                width: '50px',
                                height: '50px',
                                background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                                borderRadius: '12px',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                flexShrink: 0
                              }}>
                                <AlertTriangle size={20} color="white" />
                              </div>
                            );
                          } else {
                            const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();
                            const categoryStyle = getCategoryStyle(categoryName);
                            const IconComponent = categoryStyle.icon;

                            return (
                              <div style={{
                                width: '50px',
                                height: '50px',
                                borderRadius: '12px',
                                background: categoryStyle.background,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                              }}>
                                <IconComponent size={20} color="white" />
                              </div>
                            );
                          }
                        })()}
                        <div style={{ flex: 1 }}>
                          <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '0.5rem',
                            marginBottom: '0.25rem'
                          }}>
                            {(() => {
                              if (announcement.is_alert) {
                                return (
                                  <span style={{
                                    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                                    color: 'white',
                                    fontSize: '0.75rem',
                                    fontWeight: '600',
                                    padding: '0.25rem 0.75rem',
                                    borderRadius: '12px',
                                    textTransform: 'uppercase',
                                    letterSpacing: '0.5px',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '0.25rem'
                                  }}>
                                    <AlertTriangle size={12} color="white" />
                                    Alert
                                  </span>
                                );
                              } else {
                                const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();
                                const categoryStyle = getCategoryStyle(categoryName);
                                const IconComponent = categoryStyle.icon;

                                return (
                                  <span style={{
                                    background: categoryStyle.background,
                                    color: 'white',
                                    padding: '0.25rem 0.75rem',
                                    borderRadius: '12px',
                                    fontSize: '0.75rem',
                                    fontWeight: '600',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '0.25rem'
                                  }}>
                                    <IconComponent size={12} color="white" />
                                    {categoryName}
                                  </span>
                                );
                              }
                            })()}
                            {announcement.is_pinned === 1 && (
                              <span style={{
                                background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                                color: 'white',
                                padding: '0.25rem 0.75rem',
                                borderRadius: '12px',
                                fontSize: '0.75rem',
                                fontWeight: '600',
                                display: 'flex',
                                alignItems: 'center',
                                gap: '0.25rem'
                              }}>
                                <Pin size={12} color="white" />
                                PINNED
                              </span>
                            )}
                          </div>
                          <div style={{
                            color: '#9ca3af',
                            fontSize: '0.8rem'
                          }}>
                            By {announcement.author_name} • {new Date(announcement.published_at).toLocaleDateString('en-US', {
                              weekday: 'short',
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </div>
                        </div>
                      </div>

                      {/* Announcement Content */}
                      <h3 style={{
                        margin: '0 0 0.75rem 0',
                        color: '#1f2937',
                        fontSize: '1.25rem',
                        fontWeight: '700',
                        lineHeight: '1.3'
                      }}>
                        {announcement.title}
                      </h3>

                      {/* Images - Facebook-style Gallery */}
                      {(() => {
                        // Get images from multiple sources
                        const imageUrls: string[] = [];

                        // Add images from attachments (new multiple image system)
                        if (announcement.attachments && announcement.attachments.length > 0) {
                          announcement.attachments.forEach((img: AnnouncementAttachment) => {
                            if (img.file_path) {
                              // Use getImageUrl to construct the full URL
                              const fullUrl = getImageUrl(img.file_path);
                              if (fullUrl) {
                                imageUrls.push(fullUrl);
                              }
                            }
                          });
                        }

                        // Fallback to legacy single image
                        if (imageUrls.length === 0 && (announcement.image_url || announcement.image_path)) {
                          const legacyUrl = getImageUrl(announcement.image_url || announcement.image_path);
                          if (legacyUrl) {
                            imageUrls.push(legacyUrl);
                          }
                        }

                        return imageUrls.length > 0 ? (
                          <FacebookImageGallery
                            images={imageUrls.filter(Boolean) as string[]}
                            altPrefix={announcement.title}
                            maxVisible={4}
                            onImageClick={(index) => {
                              const filteredImages = imageUrls.filter(Boolean) as string[];
                              openLightbox(filteredImages, index);
                            }}
                          />
                        ) : null;
                      })()}

                      <div style={{
                        margin: '0 0 1.5rem 0',
                        color: '#4b5563',
                        fontSize: '0.95rem',
                        lineHeight: '1.6'
                      }}
                      dangerouslySetInnerHTML={{ __html: announcement.content }}
                      />

                      {/* Announcement Actions */}
                      <div style={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        paddingTop: '1rem',
                        borderTop: '1px solid rgba(0, 0, 0, 0.1)'
                      }}>
                        <div style={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: '1rem'
                        }}>
                          {/* Like Button */}
                          <button
                            onClick={() => handleLikeToggle(announcement)}
                            style={{
                              background: announcement.user_reaction ? 'rgba(239, 68, 68, 0.1)' : 'transparent',
                              color: announcement.user_reaction ? '#dc2626' : '#6b7280',
                              border: 'none',
                              borderRadius: '8px',
                              padding: '0.5rem 1rem',
                              fontSize: '0.875rem',
                              fontWeight: '600',
                              cursor: 'pointer',
                              display: 'flex',
                              alignItems: 'center',
                              gap: '0.5rem',
                              transition: 'all 0.2s ease'
                            }}
                            onMouseEnter={(e) => {
                              if (!announcement.user_reaction) {
                                e.currentTarget.style.background = 'rgba(239, 68, 68, 0.1)';
                                e.currentTarget.style.color = '#dc2626';
                              }
                            }}
                            onMouseLeave={(e) => {
                              if (!announcement.user_reaction) {
                                e.currentTarget.style.background = 'transparent';
                                e.currentTarget.style.color = '#6b7280';
                              }
                            }}
                          >
                            <Heart
                              size={16}
                              color={announcement.user_reaction ? "#dc2626" : "#9ca3af"}
                              fill={announcement.user_reaction ? "#dc2626" : "none"}
                            />
                            <span>{announcement.reaction_count || 0}</span>
                          </button>

                          {/* Comment Button */}
                          {announcement.allow_comments && (
                            <button
                              onClick={() => setShowComments(
                                showComments === announcement.announcement_id ? null : announcement.announcement_id
                              )}
                              style={{
                                background: showComments === announcement.announcement_id ? 'rgba(59, 130, 246, 0.1)' : 'transparent',
                                color: showComments === announcement.announcement_id ? '#3b82f6' : '#6b7280',
                                border: 'none',
                                borderRadius: '8px',
                                padding: '0.5rem 1rem',
                                fontSize: '0.875rem',
                                fontWeight: '600',
                                cursor: 'pointer',
                                display: 'flex',
                                alignItems: 'center',
                                gap: '0.5rem',
                                transition: 'all 0.2s ease'
                              }}
                              onMouseEnter={(e) => {
                                if (showComments !== announcement.announcement_id) {
                                  e.currentTarget.style.background = 'rgba(59, 130, 246, 0.1)';
                                  e.currentTarget.style.color = '#3b82f6';
                                }
                              }}
                              onMouseLeave={(e) => {
                                if (showComments !== announcement.announcement_id) {
                                  e.currentTarget.style.background = 'transparent';
                                  e.currentTarget.style.color = '#6b7280';
                                }
                              }}
                            >
                              <MessageSquare size={16} color="#6b7280" />
                              <span>{announcement.comment_count || 0}</span>
                            </button>
                          )}
                        </div>

                        <div style={{
                          color: '#9ca3af',
                          fontSize: '0.8rem'
                        }}>
                          {announcement.view_count || 0} views
                        </div>
                      </div>

                      {/* Comments Section */}
                      {showComments === announcement.announcement_id && announcement.allow_comments && (
                        <div style={{
                          marginTop: '1rem',
                          paddingTop: '1rem',
                          borderTop: '1px solid rgba(0, 0, 0, 0.1)'
                        }}>
                          {/* Comment Input */}
                          <div style={{
                            display: 'flex',
                            gap: '0.75rem',
                            marginBottom: '1rem'
                          }}>
                            <div style={{
                              width: '40px',
                              height: '40px',
                              borderRadius: '50%',
                              background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              color: 'white',
                              fontWeight: '600',
                              fontSize: '0.875rem'
                            }}>
                              S
                            </div>
                            <div style={{ flex: 1 }}>
                              <textarea
                                value={newComment[announcement.announcement_id] || ''}
                                onChange={(e) => setNewComment(prev => ({
                                  ...prev,
                                  [announcement.announcement_id]: e.target.value
                                }))}
                                placeholder="Write a comment..."
                                style={{
                                  width: '100%',
                                  minHeight: '80px',
                                  padding: '0.75rem',
                                  border: '1px solid #e5e7eb',
                                  borderRadius: '12px',
                                  fontSize: '0.9rem',
                                  outline: 'none',
                                  resize: 'vertical',
                                  fontFamily: 'inherit'
                                }}
                              />
                              <div style={{
                                display: 'flex',
                                justifyContent: 'flex-end',
                                marginTop: '0.5rem'
                              }}>
                                <button
                                  onClick={() => handleCommentSubmit(
                                    announcement.announcement_id,
                                    newComment[announcement.announcement_id] || ''
                                  )}
                                  disabled={!newComment[announcement.announcement_id]?.trim() || submittingComment === announcement.announcement_id}
                                  style={{
                                    background: newComment[announcement.announcement_id]?.trim()
                                      ? 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)'
                                      : '#e5e7eb',
                                    color: newComment[announcement.announcement_id]?.trim() ? 'white' : '#9ca3af',
                                    border: 'none',
                                    borderRadius: '8px',
                                    padding: '0.5rem 1rem',
                                    fontSize: '0.875rem',
                                    fontWeight: '600',
                                    cursor: newComment[announcement.announcement_id]?.trim() ? 'pointer' : 'not-allowed',
                                    transition: 'all 0.2s ease'
                                  }}
                                >
                                  {submittingComment === announcement.announcement_id ? 'Posting...' : 'Post Comment'}
                                </button>
                              </div>
                            </div>
                          </div>

                          {/* Existing Comments */}
                          <CommentSection
                            announcementId={announcement.announcement_id}
                            allowComments={announcement.allow_comments}
                            currentUserType="student"
                          />
                        </div>
                      )}
                    </div>
                  ))}
                </>
              )}
            </div>
          )}
          </div>
        </div>
      </div>

      {/* Pinned Post Dialog */}
      {selectedPinnedPost && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          padding: '2rem'
        }}
        onClick={() => setSelectedPinnedPost(null)}
        >
          <div style={{
            backgroundColor: 'white',
            borderRadius: '16px',
            maxWidth: '600px',
            width: '100%',
            maxHeight: '80vh',
            overflow: 'auto',
            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'
          }}
          onClick={(e) => e.stopPropagation()}
          >
            {/* Dialog Header */}
            <div style={{
              padding: '1.5rem',
              borderBottom: '1px solid #e5e7eb',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem'
              }}>
                <Pin size={20} style={{ color: '#22c55e' }} />
                <h3 style={{
                  margin: 0,
                  fontSize: '1.25rem',
                  fontWeight: '600',
                  color: '#111827'
                }}>
                  Pinned Post
                </h3>
              </div>
              <button
                onClick={() => setSelectedPinnedPost(null)}
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: '1.5rem',
                  color: '#6b7280',
                  cursor: 'pointer',
                  padding: '0.25rem',
                  borderRadius: '4px',
                  transition: 'color 0.2s ease'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.color = '#374151';
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.color = '#6b7280';
                }}
              >
                ×
              </button>
            </div>

            {/* Dialog Content */}
            <div style={{ padding: '1.5rem' }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.75rem',
                marginBottom: '1rem'
              }}>
                {(() => {
                  if (selectedPinnedPost.is_alert) {
                    return (
                      <span style={{
                        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                        color: 'white',
                        fontSize: '0.75rem',
                        fontWeight: '600',
                        padding: '0.25rem 0.75rem',
                        borderRadius: '20px',
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.25rem'
                      }}>
                        <AlertTriangle size={12} color="white" />
                        Alert
                      </span>
                    );
                  } else {
                    const categoryName = (selectedPinnedPost.category_name || 'GENERAL').toUpperCase();
                    const categoryStyle = getCategoryStyle(categoryName);
                    const IconComponent = categoryStyle.icon;

                    return (
                      <span style={{
                        background: categoryStyle.background,
                        color: 'white',
                        fontSize: '0.75rem',
                        fontWeight: '600',
                        padding: '0.25rem 0.75rem',
                        borderRadius: '20px',
                        textTransform: 'uppercase',
                        letterSpacing: '0.5px',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '0.25rem'
                      }}>
                        <IconComponent size={12} color="white" />
                        {categoryName}
                      </span>
                    );
                  }
                })()}

                <span style={{
                  background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',
                  color: 'white',
                  padding: '0.25rem 0.75rem',
                  borderRadius: '12px',
                  fontSize: '0.75rem',
                  fontWeight: '600',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.25rem'
                }}>
                  <Pin size={12} />
                  PINNED
                </span>
              </div>

              <h2 style={{
                margin: '0 0 1rem 0',
                fontSize: '1.5rem',
                fontWeight: '700',
                color: '#111827',
                lineHeight: '1.3'
              }}>
                {selectedPinnedPost.title}
              </h2>

              <div style={{
                color: '#4b5563',
                fontSize: '1rem',
                lineHeight: '1.6',
                marginBottom: '1.5rem'
              }}>
                {selectedPinnedPost.content}
              </div>

              {/* Images */}
              {selectedPinnedPost.attachments && selectedPinnedPost.attachments.length > 0 && (
                <div style={{ marginBottom: '1.5rem' }}>
                  <FacebookImageGallery
                    images={selectedPinnedPost.attachments.map((img: any) => getImageUrl(img.file_path)).filter(Boolean)}
                    altPrefix={selectedPinnedPost.title}
                    onImageClick={(index) => {
                      const imageUrls = selectedPinnedPost.attachments.map((img: any) => getImageUrl(img.file_path)).filter(Boolean);
                      openLightbox(imageUrls, index);
                    }}
                  />
                </div>
              )}

              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: '1rem',
                fontSize: '0.875rem',
                color: '#6b7280',
                paddingTop: '1rem',
                borderTop: '1px solid #e5e7eb'
              }}>
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: '0.5rem'
                }}>
                  <Calendar size={16} />
                  <span>Published: {new Date(selectedPinnedPost.created_at).toLocaleDateString()}</span>
                </div>
                {selectedPinnedPost.author_name && (
                  <div>
                    By: {selectedPinnedPost.author_name}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Image Lightbox */}
      <ImageLightbox
        images={lightboxImages}
        initialIndex={lightboxInitialIndex}
        isOpen={lightboxOpen}
        onClose={() => setLightboxOpen(false)}
        altPrefix="Image"
      />
    </div>
    </>
  );
};

export default StudentNewsfeed;
