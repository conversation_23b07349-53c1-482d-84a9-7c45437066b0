{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 20h20\",\n  key: \"owomy5\"\n}], [\"path\", {\n  d: \"m9 10 2 2 4-4\",\n  key: \"1gnqz4\"\n}], [\"rect\", {\n  x: \"3\",\n  y: \"4\",\n  width: \"18\",\n  height: \"12\",\n  rx: \"2\",\n  key: \"8ur36m\"\n}]];\nconst LaptopMinimalCheck = createLucideIcon(\"laptop-minimal-check\", __iconNode);\nexport { __iconNode, LaptopMinimalCheck as default };\n//# sourceMappingURL=laptop-minimal-check.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}