{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"polygon\", {\n  points: \"11 19 2 12 11 5 11 19\",\n  key: \"14yba5\"\n}], [\"polygon\", {\n  points: \"22 19 13 12 22 5 22 19\",\n  key: \"1pi1cj\"\n}]];\nconst Rewind = createLucideIcon(\"rewind\", __iconNode);\nexport { __iconNode, Rewind as default };\n//# sourceMappingURL=rewind.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}