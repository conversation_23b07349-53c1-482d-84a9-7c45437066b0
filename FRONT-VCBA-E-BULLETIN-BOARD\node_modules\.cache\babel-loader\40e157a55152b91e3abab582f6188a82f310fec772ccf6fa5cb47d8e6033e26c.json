{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m14.5 9.5 1 1\",\n  key: \"159eiq\"\n}], [\"path\", {\n  d: \"m15.5 8.5-4 4\",\n  key: \"iirg3q\"\n}], [\"path\", {\n  d: \"M3 12a9 9 0 1 0 9-9 9.74 9.74 0 0 0-6.74 2.74L3 8\",\n  key: \"g2jlw\"\n}], [\"path\", {\n  d: \"M3 3v5h5\",\n  key: \"1xhq8a\"\n}], [\"circle\", {\n  cx: \"10\",\n  cy: \"14\",\n  r: \"2\",\n  key: \"1239so\"\n}]];\nconst RotateCcwKey = createLucideIcon(\"rotate-ccw-key\", __iconNode);\nexport { __iconNode, RotateCcwKey as default };\n//# sourceMappingURL=rotate-ccw-key.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}