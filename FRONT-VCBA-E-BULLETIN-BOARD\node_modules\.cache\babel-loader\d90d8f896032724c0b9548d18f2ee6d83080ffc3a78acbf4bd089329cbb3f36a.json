{"ast": null, "code": "var _jsxFileName = \"D:\\\\capstone-e-bulletin-reactjs-proj\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\student\\\\StudentNewsfeed.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useCategories } from '../../hooks/useAnnouncements';\nimport { announcementService } from '../../services';\nimport CommentSection from '../../components/student/CommentSection';\nimport { getImageUrl, API_BASE_URL } from '../../config/constants';\nimport { Home, Search, Pin, Calendar, MessageSquare, Heart, Filter, MapPin, BookOpen, Users, PartyPopper, AlertTriangle, Clock, Trophy, Briefcase, GraduationCap, Flag, Coffee, Plane } from 'lucide-react';\n\n// Custom hook for CORS-safe image loading\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst useImageLoader = imagePath => {\n  _s();\n  const [imageUrl, setImageUrl] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n        console.log('🔄 Fetching image via CORS-safe method:', fullUrl);\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Origin': window.location.origin\n          },\n          mode: 'cors'\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n        setImageUrl(objectUrl);\n        console.log('✅ Image loaded successfully via fetch');\n      } catch (err) {\n        console.error('❌ Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadImage();\n\n    // Cleanup object URL on unmount\n    return () => {\n      if (imageUrl && imageUrl.startsWith('blob:')) {\n        URL.revokeObjectURL(imageUrl);\n      }\n    };\n  }, [imagePath]);\n  return {\n    imageUrl,\n    loading,\n    error\n  };\n};\n\n// Reusable CORS-safe image component\n_s(useImageLoader, \"RmtsSrtaIxi3XNLTaMW1wv0GUMw=\");\nconst ImageDisplay = ({\n  imagePath,\n  alt,\n  style,\n  className,\n  onLoad,\n  onMouseEnter,\n  onMouseLeave\n}) => {\n  _s2();\n  const {\n    imageUrl,\n    loading,\n    error\n  } = useImageLoader(imagePath);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b'\n      },\n      className: className,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.5rem',\n            fontSize: '1.5rem'\n          },\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem'\n          },\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !imageUrl) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b',\n        border: '2px dashed #cbd5e1'\n      },\n      className: className,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.5rem',\n            fontSize: '1.5rem'\n          },\n          children: \"\\uD83D\\uDDBC\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem',\n            fontWeight: '500'\n          },\n          children: \"Image unavailable\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.75rem',\n            marginTop: '0.25rem',\n            color: '#9ca3af'\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"img\", {\n    src: imageUrl,\n    alt: alt,\n    style: style,\n    className: className,\n    onLoad: e => {\n      console.log('✅ Image rendered successfully via CORS-safe method');\n      onLoad === null || onLoad === void 0 ? void 0 : onLoad(e);\n    },\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 161,\n    columnNumber: 5\n  }, this);\n};\n\n// Facebook-style image gallery component\n_s2(ImageDisplay, \"PvWnh8aQTIWDcUxVyGaJg2nxP24=\", false, function () {\n  return [useImageLoader];\n});\n_c = ImageDisplay;\nconst FacebookImageGallery = ({\n  images,\n  altPrefix,\n  maxVisible = 4,\n  onImageClick\n}) => {\n  if (!images || images.length === 0) return null;\n  const visibleImages = images.slice(0, maxVisible);\n  const remainingCount = images.length - maxVisible;\n  const getImageStyle = (index, total) => {\n    const baseStyle = {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover',\n      cursor: 'pointer',\n      transition: 'transform 0.2s ease, filter 0.2s ease',\n      borderRadius: index === 0 && total === 1 ? '12px' : '8px'\n    };\n    return baseStyle;\n  };\n  const getContainerStyle = (index, total) => {\n    const baseStyle = {\n      position: 'relative',\n      overflow: 'hidden',\n      backgroundColor: '#f3f4f6'\n    };\n    if (total === 1) {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '400px',\n        borderRadius: '12px'\n      };\n    }\n    if (total === 2) {\n      return {\n        ...baseStyle,\n        width: '50%',\n        height: '300px',\n        borderRadius: '8px'\n      };\n    }\n    if (total === 3) {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '60%',\n          height: '300px',\n          borderRadius: '8px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '100%',\n          height: '148px',\n          borderRadius: '8px'\n        };\n      }\n    }\n\n    // 4+ images\n    if (index === 0) {\n      return {\n        ...baseStyle,\n        width: '60%',\n        height: '300px',\n        borderRadius: '8px'\n      };\n    } else {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '96px',\n        borderRadius: '8px'\n      };\n    }\n  };\n  const renderOverlay = (index, count) => {\n    if (index === maxVisible - 1 && count > 0) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.6)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontSize: '1.5rem',\n          fontWeight: '600',\n          borderRadius: '8px'\n        },\n        children: [\"+\", count]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 272,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: getContainerStyle(0, visibleImages.length),\n      children: [/*#__PURE__*/_jsxDEV(ImageDisplay, {\n        imagePath: visibleImages[0],\n        alt: `${altPrefix} - Image 1`,\n        style: getImageStyle(0, visibleImages.length),\n        onMouseEnter: e => {\n          e.currentTarget.style.transform = 'scale(1.02)';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.transform = 'scale(1)';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 303,\n        columnNumber: 9\n      }, this), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          cursor: 'pointer'\n        },\n        onClick: () => onImageClick(0)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 302,\n      columnNumber: 7\n    }, this), visibleImages.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '4px',\n        width: visibleImages.length === 2 ? '50%' : '40%'\n      },\n      children: visibleImages.slice(1).map((image, idx) => {\n        const actualIndex = idx + 1;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getContainerStyle(actualIndex, visibleImages.length),\n          children: [/*#__PURE__*/_jsxDEV(ImageDisplay, {\n            imagePath: image,\n            alt: `${altPrefix} - Image ${actualIndex + 1}`,\n            style: getImageStyle(actualIndex, visibleImages.length),\n            onMouseEnter: e => {\n              e.currentTarget.style.transform = 'scale(1.02)';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.transform = 'scale(1)';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 17\n          }, this), renderOverlay(actualIndex, remainingCount), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            },\n            onClick: () => onImageClick(actualIndex)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 19\n          }, this)]\n        }, actualIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 340,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 295,\n    columnNumber: 5\n  }, this);\n};\n_c2 = FacebookImageGallery;\nconst StudentNewsfeed = () => {\n  _s3();\n  const navigate = useNavigate();\n\n  // Category styling function\n  const getCategoryStyle = categoryName => {\n    const styles = {\n      'ACADEMIC': {\n        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n        icon: BookOpen\n      },\n      'GENERAL': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Users\n      },\n      'EVENTS': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: PartyPopper\n      },\n      'EMERGENCY': {\n        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n        icon: AlertTriangle\n      },\n      'SPORTS': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Trophy\n      },\n      'DEADLINES': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Clock\n      }\n    };\n    return styles[categoryName] || styles['GENERAL'];\n  };\n\n  // Holiday type styling function\n  const getHolidayTypeStyle = holidayTypeName => {\n    const styles = {\n      'NATIONAL HOLIDAY': {\n        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n        icon: Flag\n      },\n      'SCHOOL EVENT': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: GraduationCap\n      },\n      'ACADEMIC BREAK': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Coffee\n      },\n      'SPORTS EVENT': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Trophy\n      },\n      'FIELD TRIP': {\n        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n        icon: Plane\n      },\n      'MEETING': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Briefcase\n      }\n    };\n    return styles[holidayTypeName] || styles['SCHOOL EVENT'];\n  };\n\n  // Filter states\n  const [filterCategory, setFilterCategory] = useState('');\n  const [filterGradeLevel, setFilterGradeLevel] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // UI states\n  const [showComments, setShowComments] = useState(null);\n  const [newComment, setNewComment] = useState({});\n  const [submittingComment, setSubmittingComment] = useState(null);\n\n  // Data states\n  const [calendarEvents, setCalendarEvents] = useState([]);\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState();\n  const [announcements, setAnnouncements] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState();\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState([]);\n  const {\n    categories\n  } = useCategories();\n\n  // Grade levels for dropdown (11-12)\n  const gradeLevels = Array.from({\n    length: 2\n  }, (_, i) => i + 11); // [11, 12]\n\n  // Fetch published announcements with images\n  const fetchPublishedAnnouncements = async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const response = await fetch(`${API_BASE_URL}/api/announcements?status=published&page=1&limit=50&sort_by=created_at&sort_order=DESC`);\n      const data = await response.json();\n      if (data.success && data.data) {\n        const announcementsData = data.data.announcements || [];\n\n        // Fetch images for each announcement\n        const announcementsWithImages = await Promise.all(announcementsData.map(async announcement => {\n          try {\n            const imageResponse = await fetch(`${API_BASE_URL}/api/announcements/${announcement.announcement_id}/images`);\n            const imageData = await imageResponse.json();\n            if (imageData.success && imageData.data) {\n              announcement.images = imageData.data.images || [];\n            } else {\n              announcement.images = [];\n            }\n          } catch (imgErr) {\n            console.warn(`Failed to fetch images for announcement ${announcement.announcement_id}:`, imgErr);\n            announcement.images = [];\n          }\n          return announcement;\n        }));\n        setAnnouncements(announcementsWithImages);\n\n        // Separate pinned announcements\n        const pinned = announcementsWithImages.filter(ann => ann.is_pinned === 1);\n        setPinnedAnnouncements(pinned);\n      } else {\n        setError('Failed to load announcements');\n      }\n    } catch (err) {\n      console.error('Error fetching announcements:', err);\n      setError(err.message || 'Failed to load announcements');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch calendar events\n  const fetchCalendarEvents = async () => {\n    try {\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`);\n      const data = await response.json();\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(eventsData.map(async event => {\n          try {\n            const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`);\n            const imageData = await imageResponse.json();\n            if (imageData.success && imageData.data) {\n              event.images = imageData.data.attachments || [];\n            } else {\n              event.images = [];\n            }\n          } catch (imgErr) {\n            console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n            event.images = [];\n          }\n          return event;\n        }));\n        setCalendarEvents(eventsWithImages);\n      } else {\n        setCalendarError('Failed to load calendar events');\n      }\n    } catch (err) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to load calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  };\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchPublishedAnnouncements();\n    fetchCalendarEvents();\n  }, []);\n\n  // Handle like/unlike functionality\n  const handleLikeToggle = async announcement => {\n    try {\n      if (announcement.user_reaction) {\n        await announcementService.removeReaction(announcement.announcement_id);\n      } else {\n        const response = await fetch(`${API_BASE_URL}/api/announcements/${announcement.announcement_id}/reactions`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            reaction_type_id: 1,\n            notify_admin: true\n          })\n        });\n        if (!response.ok) {\n          throw new Error('Failed to add reaction');\n        }\n      }\n\n      // Refresh data\n      await fetchPublishedAnnouncements();\n    } catch (error) {\n      console.error('Error toggling like:', error);\n    }\n  };\n\n  // Handle comment submission\n  const handleCommentSubmit = async (announcementId, commentText) => {\n    if (!commentText.trim()) return;\n    try {\n      setSubmittingComment(announcementId);\n      const response = await fetch(`${API_BASE_URL}/api/announcements/${announcementId}/comments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          content: commentText,\n          commenter_name: 'Student User',\n          commenter_email: '<EMAIL>',\n          notify_admin: true\n        })\n      });\n      if (response.ok) {\n        setNewComment(prev => ({\n          ...prev,\n          [announcementId]: ''\n        }));\n        await fetchPublishedAnnouncements();\n      } else {\n        console.error('Failed to submit comment');\n      }\n    } catch (error) {\n      console.error('Error submitting comment:', error);\n    } finally {\n      setSubmittingComment(null);\n    }\n  };\n\n  // Filter announcements\n  const filteredAnnouncements = announcements.filter(announcement => {\n    const matchesSearch = !searchTerm || announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) || announcement.content.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = !filterCategory || announcement.category_id.toString() === filterCategory;\n\n    // Note: Grade level filtering would require additional backend support\n    // For now, we'll show all announcements regardless of grade level filter\n\n    return matchesSearch && matchesCategory;\n  });\n\n  // Filter calendar events with date-based filtering\n  const filteredCalendarEvents = calendarEvents.filter(event => {\n    const matchesSearch = !searchTerm || event.title.toLowerCase().includes(searchTerm.toLowerCase()) || event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase());\n\n    // Only show events that are published and on or after today's date\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    const eventDate = new Date(event.event_date);\n    eventDate.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    const isEventDateValid = eventDate >= today;\n    const isPublished = event.is_published === 1;\n    return matchesSearch && isEventDateValid && isPublished;\n  });\n\n  // Combine and sort all content by date (most recent first)\n  const displayAnnouncements = filteredAnnouncements;\n  const displayEvents = filteredCalendarEvents;\n\n  // Create combined content array for better chronological display\n  const combinedContent = [...displayAnnouncements.map(item => ({\n    ...item,\n    type: 'announcement',\n    sortDate: new Date(item.created_at)\n  })), ...displayEvents.map(item => ({\n    ...item,\n    type: 'event',\n    sortDate: new Date(item.event_date)\n  }))].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n          html {\n            scroll-behavior: smooth;\n          }\n\n          @keyframes spin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n          }\n\n          @media (max-width: 768px) {\n            .mobile-hide { display: none !important; }\n            .mobile-full { width: 100% !important; margin-left: 0 !important; }\n            .mobile-stack {\n              flex-direction: column !important;\n              gap: 0.75rem !important;\n            }\n            .mobile-grid { grid-template-columns: 1fr !important; }\n          }\n\n          @media (max-width: 480px) {\n            .mobile-small-padding { padding: 0.75rem !important; }\n            .mobile-small-text { font-size: 0.8rem !important; }\n            .mobile-compact-header {\n              padding: 0.75rem 1rem !important;\n            }\n            .mobile-compact-title {\n              font-size: 1.25rem !important;\n            }\n            .mobile-compact-search {\n              max-width: 200px !important;\n            }\n          }\n        `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 675,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        minHeight: '100vh',\n        background: 'linear-gradient(135deg, #f0f9ff 0%, #fefce8 100%)',\n        scrollBehavior: 'smooth'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mobile-full\",\n        style: {\n          flex: 1,\n          display: 'flex',\n          flexDirection: 'column',\n          minHeight: '100vh',\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"header\", {\n          style: {\n            background: 'white',\n            borderBottom: '1px solid #e5e7eb',\n            position: 'sticky',\n            top: 0,\n            zIndex: 100,\n            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '100%',\n              padding: '0 2rem',\n              height: '72px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1.5rem',\n                minWidth: '300px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"/logo/vcba1.png\",\n                alt: \"VCBA Logo\",\n                style: {\n                  width: '48px',\n                  height: '48px',\n                  objectFit: 'contain'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '1.5rem',\n                    fontWeight: '700',\n                    color: '#111827',\n                    lineHeight: '1.2'\n                  },\n                  children: \"VCBA E-Bulletin Board\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 763,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '0.875rem',\n                    color: '#6b7280'\n                  },\n                  children: \"Student Newsfeed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 772,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 762,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 747,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1,\n                maxWidth: '500px',\n                margin: '0 2rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Search, {\n                  size: 20,\n                  style: {\n                    position: 'absolute',\n                    left: '1rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: '#9ca3af'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 789,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Search announcements and events...\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value),\n                  style: {\n                    width: '100%',\n                    height: '44px',\n                    padding: '0 1rem 0 3rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '12px',\n                    background: '#f9fafb',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    transition: 'all 0.2s ease'\n                  },\n                  onFocus: e => {\n                    e.target.style.borderColor = '#3b82f6';\n                    e.target.style.background = 'white';\n                    e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';\n                  },\n                  onBlur: e => {\n                    e.target.style.borderColor = '#d1d5db';\n                    e.target.style.background = '#f9fafb';\n                    e.target.style.boxShadow = 'none';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 799,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 788,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 783,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem',\n                minWidth: '400px',\n                justifyContent: 'flex-end'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  padding: '0.5rem',\n                  background: '#f9fafb',\n                  borderRadius: '12px',\n                  border: '1px solid #e5e7eb'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                  value: filterCategory,\n                  onChange: e => setFilterCategory(e.target.value),\n                  style: {\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '110px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"All Categories\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 863,\n                    columnNumber: 19\n                  }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: category.category_id.toString(),\n                    children: category.name\n                  }, category.category_id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 865,\n                    columnNumber: 21\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 848,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: filterGradeLevel,\n                  onChange: e => setFilterGradeLevel(e.target.value),\n                  style: {\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '100px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"All Grades\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 886,\n                    columnNumber: 19\n                  }, this), gradeLevels.map(grade => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: grade.toString(),\n                    children: [\"Grade \", grade]\n                  }, grade, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 888,\n                    columnNumber: 21\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 871,\n                  columnNumber: 17\n                }, this), (searchTerm || filterCategory || filterGradeLevel) && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setSearchTerm('');\n                    setFilterCategory('');\n                    setFilterGradeLevel('');\n                  },\n                  style: {\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: '#ef4444',\n                    color: 'white',\n                    fontSize: '0.875rem',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.background = '#dc2626';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.background = '#ef4444';\n                  },\n                  children: \"Clear\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 895,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 839,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => navigate('/student/dashboard'),\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  padding: '0.5rem 1rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '8px',\n                  background: 'white',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.borderColor = '#3b82f6';\n                  e.currentTarget.style.color = '#3b82f6';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.borderColor = '#d1d5db';\n                  e.currentTarget.style.color = '#374151';\n                },\n                children: [/*#__PURE__*/_jsxDEV(Home, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 952,\n                  columnNumber: 17\n                }, this), \"Dashboard\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 927,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 831,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 738,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 730,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            display: 'flex',\n            gap: '1.5rem',\n            padding: '1.5rem 2rem',\n            background: 'transparent',\n            alignItems: 'flex-start'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '280px',\n              flexShrink: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: 'white',\n                borderRadius: '14px',\n                border: '1px solid #e5e7eb',\n                overflow: 'hidden',\n                position: 'sticky',\n                top: '80px',\n                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '1rem 1.25rem 0.75rem',\n                  borderBottom: '1px solid #f3f4f6',\n                  background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.75rem',\n                    marginBottom: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Pin, {\n                    size: 20,\n                    style: {\n                      color: 'white'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 994,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    style: {\n                      margin: 0,\n                      fontSize: '1.125rem',\n                      fontWeight: '600',\n                      color: 'white'\n                    },\n                    children: \"Important Updates\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 995,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 988,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '0.875rem',\n                    color: 'rgba(255, 255, 255, 0.8)'\n                  },\n                  children: \"Don't miss these announcements\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1004,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 983,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '0.75rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: '0.75rem',\n                    background: 'linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)',\n                    borderRadius: '10px',\n                    border: '1px solid #f59e0b',\n                    marginBottom: '0.75rem',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.transform = 'translateY(-2px)';\n                    e.currentTarget.style.boxShadow = '0 8px 25px rgba(245, 158, 11, 0.3)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = 'none';\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'flex-start',\n                      gap: '0.75rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        width: '10px',\n                        height: '10px',\n                        background: '#f59e0b',\n                        borderRadius: '50%',\n                        marginTop: '0.5rem',\n                        flexShrink: 0,\n                        boxShadow: '0 0 0 3px rgba(245, 158, 11, 0.3)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1038,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        flex: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        style: {\n                          margin: '0 0 0.5rem 0',\n                          fontSize: '0.875rem',\n                          fontWeight: '600',\n                          color: '#92400e',\n                          lineHeight: '1.4'\n                        },\n                        children: \"\\uD83D\\uDCDA Final Exams Schedule Released\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1048,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        style: {\n                          margin: '0 0 0.5rem 0',\n                          fontSize: '0.8rem',\n                          color: '#a16207',\n                          lineHeight: '1.4'\n                        },\n                        children: \"Check your exam dates and prepare accordingly. Good luck!\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1057,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          fontSize: '0.75rem',\n                          color: '#a16207'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                          size: 12\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1072,\n                          columnNumber: 25\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"3 days ago\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1073,\n                          columnNumber: 25\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1065,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1047,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1033,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1016,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: '0.75rem',\n                    background: 'linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)',\n                    borderRadius: '10px',\n                    border: '1px solid #3b82f6',\n                    marginBottom: '0.75rem',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.transform = 'translateY(-2px)';\n                    e.currentTarget.style.boxShadow = '0 8px 25px rgba(59, 130, 246, 0.3)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = 'none';\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'flex-start',\n                      gap: '0.75rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        width: '10px',\n                        height: '10px',\n                        background: '#3b82f6',\n                        borderRadius: '50%',\n                        marginTop: '0.5rem',\n                        flexShrink: 0,\n                        boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.3)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1102,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        flex: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        style: {\n                          margin: '0 0 0.5rem 0',\n                          fontSize: '0.875rem',\n                          fontWeight: '600',\n                          color: '#1e40af',\n                          lineHeight: '1.4'\n                        },\n                        children: \"\\uD83C\\uDF93 Graduation Ceremony Details\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1112,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        style: {\n                          margin: '0 0 0.5rem 0',\n                          fontSize: '0.8rem',\n                          color: '#1d4ed8',\n                          lineHeight: '1.4'\n                        },\n                        children: \"Important information about the upcoming graduation ceremony...\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1121,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          fontSize: '0.75rem',\n                          color: '#1d4ed8'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                          size: 12\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1136,\n                          columnNumber: 25\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"1 week ago\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1137,\n                          columnNumber: 25\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1129,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1111,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1097,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1080,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: '0.75rem',\n                    background: 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)',\n                    borderRadius: '10px',\n                    border: '1px solid #22c55e',\n                    marginBottom: '0.75rem',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.transform = 'translateY(-2px)';\n                    e.currentTarget.style.boxShadow = '0 8px 25px rgba(34, 197, 94, 0.3)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = 'none';\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'flex-start',\n                      gap: '0.75rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        width: '10px',\n                        height: '10px',\n                        background: '#22c55e',\n                        borderRadius: '50%',\n                        marginTop: '0.5rem',\n                        flexShrink: 0,\n                        boxShadow: '0 0 0 3px rgba(34, 197, 94, 0.3)'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1166,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        flex: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        style: {\n                          margin: '0 0 0.5rem 0',\n                          fontSize: '0.875rem',\n                          fontWeight: '600',\n                          color: '#15803d',\n                          lineHeight: '1.4'\n                        },\n                        children: \"\\uD83C\\uDFC6 Academic Excellence Awards\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1176,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        style: {\n                          margin: '0 0 0.5rem 0',\n                          fontSize: '0.8rem',\n                          color: '#16a34a',\n                          lineHeight: '1.4'\n                        },\n                        children: \"Congratulations to all students who achieved academic excellence...\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1185,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          fontSize: '0.75rem',\n                          color: '#16a34a'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                          size: 12\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1200,\n                          columnNumber: 25\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"2 weeks ago\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1201,\n                          columnNumber: 25\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1193,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1175,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1161,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1144,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #e5e7eb',\n                    borderRadius: '12px',\n                    background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',\n                    color: '#3b82f6',\n                    fontSize: '0.875rem',\n                    fontWeight: '600',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.background = 'linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)';\n                    e.currentTarget.style.borderColor = '#3b82f6';\n                    e.currentTarget.style.transform = 'translateY(-1px)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.background = 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)';\n                    e.currentTarget.style.borderColor = '#e5e7eb';\n                    e.currentTarget.style.transform = 'translateY(0)';\n                  },\n                  children: \"\\uD83D\\uDCCC View All Important Updates\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1208,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1014,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 973,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 969,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1,\n              minWidth: 0\n            },\n            children: [(loading || calendarLoading) && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'center',\n                alignItems: 'center',\n                padding: '3rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '2.5rem',\n                  height: '2.5rem',\n                  border: '3px solid #e5e7eb',\n                  borderTop: '3px solid #3b82f6',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1246,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1240,\n              columnNumber: 13\n            }, this), (error || calendarError) && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: 'rgba(239, 68, 68, 0.1)',\n                border: '1px solid rgba(239, 68, 68, 0.2)',\n                borderRadius: '12px',\n                padding: '1rem',\n                marginBottom: '1.5rem',\n                color: '#dc2626'\n              },\n              children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"Announcements: \", error]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1267,\n                columnNumber: 25\n              }, this), calendarError && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"Calendar: \", calendarError]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1268,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1259,\n              columnNumber: 13\n            }, this), !loading && !calendarLoading && displayAnnouncements.length === 0 && displayEvents.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: 'rgba(255, 255, 255, 0.8)',\n                borderRadius: '16px',\n                padding: '3rem',\n                textAlign: 'center',\n                border: '1px solid rgba(0, 0, 0, 0.1)',\n                backdropFilter: 'blur(10px)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '1rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(Filter, {\n                  size: 48,\n                  color: \"#9ca3af\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1285,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1282,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  color: '#374151',\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  margin: '0 0 0.5rem 0'\n                },\n                children: \"No updates found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1287,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#6b7280',\n                  margin: 0\n                },\n                children: searchTerm || filterCategory || filterGradeLevel ? 'Try adjusting your filters to see more content.' : 'Check back later for new updates.'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1295,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1274,\n              columnNumber: 13\n            }, this), !loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                maxWidth: '1200px',\n                margin: '0 auto',\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '1.5rem'\n              },\n              children: [displayEvents.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: displayEvents.map(event => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    background: 'rgba(255, 255, 255, 0.95)',\n                    borderRadius: '16px',\n                    padding: '1.5rem',\n                    border: '1px solid rgba(0, 0, 0, 0.1)',\n                    backdropFilter: 'blur(10px)',\n                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                    transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.transform = 'translateY(-2px)';\n                    e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1rem',\n                      marginBottom: '1rem'\n                    },\n                    children: [(() => {\n                      const holidayTypeName = (event.holiday_type_name || 'SCHOOL EVENT').toUpperCase();\n                      const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                      const IconComponent = holidayStyle.icon;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '50px',\n                          height: '50px',\n                          borderRadius: '12px',\n                          background: holidayStyle.background,\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                          size: 20,\n                          color: \"white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1361,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1352,\n                        columnNumber: 29\n                      }, this);\n                    })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        flex: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          marginBottom: '0.25rem'\n                        },\n                        children: (() => {\n                          const holidayTypeName = (event.holiday_type_name || 'SCHOOL EVENT').toUpperCase();\n                          const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                          const IconComponent = holidayStyle.icon;\n                          return /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              background: holidayStyle.background,\n                              color: 'white',\n                              padding: '0.25rem 0.75rem',\n                              borderRadius: '12px',\n                              fontSize: '0.75rem',\n                              fontWeight: '600',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                              size: 12,\n                              color: \"white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1389,\n                              columnNumber: 35\n                            }, this), holidayTypeName]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1378,\n                            columnNumber: 33\n                          }, this);\n                        })()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1366,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          color: '#9ca3af',\n                          fontSize: '0.8rem'\n                        },\n                        children: new Date(event.event_date).toLocaleDateString('en-US', {\n                          weekday: 'long',\n                          year: 'numeric',\n                          month: 'long',\n                          day: 'numeric'\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1395,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1365,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1340,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    style: {\n                      margin: '0 0 0.75rem 0',\n                      color: '#1f2937',\n                      fontSize: '1.25rem',\n                      fontWeight: '700',\n                      lineHeight: '1.3'\n                    },\n                    children: event.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1410,\n                    columnNumber: 23\n                  }, this), event.description && /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '0 0 1rem 0',\n                      color: '#4b5563',\n                      fontSize: '0.95rem',\n                      lineHeight: '1.6'\n                    },\n                    children: event.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1421,\n                    columnNumber: 25\n                  }, this), (() => {\n                    // Get event images if they exist\n                    const eventImageUrls = [];\n                    if (event.images && event.images.length > 0) {\n                      event.images.forEach(img => {\n                        if (img.file_path) {\n                          // Convert file_path to full URL\n                          const imageUrl = getImageUrl(img.file_path);\n                          if (imageUrl) {\n                            eventImageUrls.push(imageUrl);\n                          }\n                        }\n                      });\n                    }\n                    return eventImageUrls.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginBottom: '1rem'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(FacebookImageGallery, {\n                        images: eventImageUrls.filter(Boolean),\n                        altPrefix: event.title,\n                        maxVisible: 4,\n                        onImageClick: index => {\n                          console.log(`Clicked image ${index + 1} for event: ${event.title}`);\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1450,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1449,\n                      columnNumber: 27\n                    }, this) : null;\n                  })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'space-between',\n                      paddingTop: '1rem',\n                      borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem',\n                        color: '#6b7280',\n                        fontSize: '0.875rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.25rem'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(MapPin, {\n                          size: 14,\n                          color: \"#6b7280\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1478,\n                          columnNumber: 29\n                        }, this), \"School Event\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1477,\n                        columnNumber: 27\n                      }, this), event.end_date && event.end_date !== event.event_date && /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [\"Until \", new Date(event.end_date).toLocaleDateString()]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1482,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1470,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1463,\n                    columnNumber: 23\n                  }, this)]\n                }, `event-${event.calendar_id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1319,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false), displayAnnouncements.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: displayAnnouncements.map(announcement => {\n                  var _newComment$announcem, _newComment$announcem2, _newComment$announcem3, _newComment$announcem4;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      background: 'rgba(255, 255, 255, 0.95)',\n                      borderRadius: '16px',\n                      padding: '1.5rem',\n                      border: '1px solid rgba(0, 0, 0, 0.1)',\n                      backdropFilter: 'blur(10px)',\n                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                      transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.transform = 'translateY(-2px)';\n                      e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.transform = 'translateY(0)';\n                      e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      },\n                      children: [(() => {\n                        const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                        const categoryStyle = getCategoryStyle(categoryName);\n                        const IconComponent = categoryStyle.icon;\n                        return /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            width: '50px',\n                            height: '50px',\n                            borderRadius: '12px',\n                            background: categoryStyle.background,\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                            size: 20,\n                            color: \"white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1539,\n                            columnNumber: 31\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1530,\n                          columnNumber: 29\n                        }, this);\n                      })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          flex: 1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            marginBottom: '0.25rem'\n                          },\n                          children: [(() => {\n                            const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                            const categoryStyle = getCategoryStyle(categoryName);\n                            const IconComponent = categoryStyle.icon;\n                            return /*#__PURE__*/_jsxDEV(\"span\", {\n                              style: {\n                                background: categoryStyle.background,\n                                color: 'white',\n                                padding: '0.25rem 0.75rem',\n                                borderRadius: '12px',\n                                fontSize: '0.75rem',\n                                fontWeight: '600',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.25rem'\n                              },\n                              children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                                size: 12,\n                                color: \"white\"\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1567,\n                                columnNumber: 35\n                              }, this), categoryName]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1556,\n                              columnNumber: 33\n                            }, this);\n                          })(), announcement.is_pinned === 1 && /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n                              color: 'white',\n                              padding: '0.25rem 0.75rem',\n                              borderRadius: '12px',\n                              fontSize: '0.75rem',\n                              fontWeight: '600',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(Pin, {\n                              size: 12,\n                              color: \"white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1584,\n                              columnNumber: 33\n                            }, this), \"PINNED\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1573,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1544,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            color: '#9ca3af',\n                            fontSize: '0.8rem'\n                          },\n                          children: [\"By \", announcement.author_name, \" \\u2022 \", new Date(announcement.published_at).toLocaleDateString('en-US', {\n                            weekday: 'short',\n                            year: 'numeric',\n                            month: 'short',\n                            day: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit'\n                          })]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1589,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1543,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1518,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      style: {\n                        margin: '0 0 0.75rem 0',\n                        color: '#1f2937',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        lineHeight: '1.3'\n                      },\n                      children: announcement.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1606,\n                      columnNumber: 23\n                    }, this), (() => {\n                      // Get images from multiple sources\n                      const imageUrls = [];\n\n                      // Add images from attachments (new multiple image system)\n                      if (announcement.images && announcement.images.length > 0) {\n                        announcement.images.forEach(img => {\n                          if (img.file_url) {\n                            imageUrls.push(img.file_url);\n                          }\n                        });\n                      }\n\n                      // Fallback to legacy single image\n                      if (imageUrls.length === 0 && (announcement.image_url || announcement.image_path)) {\n                        imageUrls.push(announcement.image_url || announcement.image_path);\n                      }\n                      return imageUrls.length > 0 ? /*#__PURE__*/_jsxDEV(FacebookImageGallery, {\n                        images: imageUrls.filter(Boolean),\n                        altPrefix: announcement.title,\n                        maxVisible: 4,\n                        onImageClick: index => {\n                          console.log(`Clicked image ${index + 1} for announcement: ${announcement.title}`);\n                          // Future: Open image viewer/lightbox\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1636,\n                        columnNumber: 27\n                      }, this) : null;\n                    })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        margin: '0 0 1.5rem 0',\n                        color: '#4b5563',\n                        fontSize: '0.95rem',\n                        lineHeight: '1.6'\n                      },\n                      dangerouslySetInnerHTML: {\n                        __html: announcement.content\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1648,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        paddingTop: '1rem',\n                        borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => handleLikeToggle(announcement),\n                          style: {\n                            background: announcement.user_reaction ? 'rgba(239, 68, 68, 0.1)' : 'transparent',\n                            color: announcement.user_reaction ? '#dc2626' : '#6b7280',\n                            border: 'none',\n                            borderRadius: '8px',\n                            padding: '0.5rem 1rem',\n                            fontSize: '0.875rem',\n                            fontWeight: '600',\n                            cursor: 'pointer',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            transition: 'all 0.2s ease'\n                          },\n                          onMouseEnter: e => {\n                            if (!announcement.user_reaction) {\n                              e.currentTarget.style.background = 'rgba(239, 68, 68, 0.1)';\n                              e.currentTarget.style.color = '#dc2626';\n                            }\n                          },\n                          onMouseLeave: e => {\n                            if (!announcement.user_reaction) {\n                              e.currentTarget.style.background = 'transparent';\n                              e.currentTarget.style.color = '#6b7280';\n                            }\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Heart, {\n                            size: 16,\n                            color: announcement.user_reaction ? \"#dc2626\" : \"#9ca3af\",\n                            fill: announcement.user_reaction ? \"#dc2626\" : \"none\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1700,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: announcement.reaction_count || 0\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1705,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1671,\n                          columnNumber: 27\n                        }, this), announcement.allow_comments && /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => setShowComments(showComments === announcement.announcement_id ? null : announcement.announcement_id),\n                          style: {\n                            background: showComments === announcement.announcement_id ? 'rgba(59, 130, 246, 0.1)' : 'transparent',\n                            color: showComments === announcement.announcement_id ? '#3b82f6' : '#6b7280',\n                            border: 'none',\n                            borderRadius: '8px',\n                            padding: '0.5rem 1rem',\n                            fontSize: '0.875rem',\n                            fontWeight: '600',\n                            cursor: 'pointer',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            transition: 'all 0.2s ease'\n                          },\n                          onMouseEnter: e => {\n                            if (showComments !== announcement.announcement_id) {\n                              e.currentTarget.style.background = 'rgba(59, 130, 246, 0.1)';\n                              e.currentTarget.style.color = '#3b82f6';\n                            }\n                          },\n                          onMouseLeave: e => {\n                            if (showComments !== announcement.announcement_id) {\n                              e.currentTarget.style.background = 'transparent';\n                              e.currentTarget.style.color = '#6b7280';\n                            }\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n                            size: 16,\n                            color: \"#6b7280\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1741,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: announcement.comment_count || 0\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1742,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1710,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1665,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          color: '#9ca3af',\n                          fontSize: '0.8rem'\n                        },\n                        children: [announcement.view_count || 0, \" views\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1747,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1658,\n                      columnNumber: 23\n                    }, this), showComments === announcement.announcement_id && announcement.allow_comments && /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginTop: '1rem',\n                        paddingTop: '1rem',\n                        borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          gap: '0.75rem',\n                          marginBottom: '1rem'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            width: '40px',\n                            height: '40px',\n                            borderRadius: '50%',\n                            background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            color: 'white',\n                            fontWeight: '600',\n                            fontSize: '0.875rem'\n                          },\n                          children: \"S\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1768,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            flex: 1\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                            value: newComment[announcement.announcement_id] || '',\n                            onChange: e => setNewComment(prev => ({\n                              ...prev,\n                              [announcement.announcement_id]: e.target.value\n                            })),\n                            placeholder: \"Write a comment...\",\n                            style: {\n                              width: '100%',\n                              minHeight: '80px',\n                              padding: '0.75rem',\n                              border: '1px solid #e5e7eb',\n                              borderRadius: '12px',\n                              fontSize: '0.9rem',\n                              outline: 'none',\n                              resize: 'vertical',\n                              fontFamily: 'inherit'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1783,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              display: 'flex',\n                              justifyContent: 'flex-end',\n                              marginTop: '0.5rem'\n                            },\n                            children: /*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: () => handleCommentSubmit(announcement.announcement_id, newComment[announcement.announcement_id] || ''),\n                              disabled: !((_newComment$announcem = newComment[announcement.announcement_id]) !== null && _newComment$announcem !== void 0 && _newComment$announcem.trim()) || submittingComment === announcement.announcement_id,\n                              style: {\n                                background: (_newComment$announcem2 = newComment[announcement.announcement_id]) !== null && _newComment$announcem2 !== void 0 && _newComment$announcem2.trim() ? 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)' : '#e5e7eb',\n                                color: (_newComment$announcem3 = newComment[announcement.announcement_id]) !== null && _newComment$announcem3 !== void 0 && _newComment$announcem3.trim() ? 'white' : '#9ca3af',\n                                border: 'none',\n                                borderRadius: '8px',\n                                padding: '0.5rem 1rem',\n                                fontSize: '0.875rem',\n                                fontWeight: '600',\n                                cursor: (_newComment$announcem4 = newComment[announcement.announcement_id]) !== null && _newComment$announcem4 !== void 0 && _newComment$announcem4.trim() ? 'pointer' : 'not-allowed',\n                                transition: 'all 0.2s ease'\n                              },\n                              children: submittingComment === announcement.announcement_id ? 'Posting...' : 'Post Comment'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1807,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1802,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1782,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1763,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(CommentSection, {\n                        announcementId: announcement.announcement_id,\n                        allowComments: announcement.allow_comments,\n                        currentUserType: \"student\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1834,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1757,\n                      columnNumber: 25\n                    }, this)]\n                  }, `announcement-${announcement.announcement_id}`, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1497,\n                    columnNumber: 21\n                  }, this);\n                })\n              }, void 0, false)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1308,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1237,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 960,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 720,\n        columnNumber: 7\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 712,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s3(StudentNewsfeed, \"Hz7fNDe/iDGw4AN4DckO79VyftA=\", false, function () {\n  return [useNavigate, useCategories];\n});\n_c3 = StudentNewsfeed;\nexport default StudentNewsfeed;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ImageDisplay\");\n$RefreshReg$(_c2, \"FacebookImageGallery\");\n$RefreshReg$(_c3, \"StudentNewsfeed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useCategories", "announcementService", "CommentSection", "getImageUrl", "API_BASE_URL", "Home", "Search", "<PERSON>n", "Calendar", "MessageSquare", "Heart", "Filter", "MapPin", "BookOpen", "Users", "PartyPopper", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clock", "Trophy", "Briefcase", "GraduationCap", "Flag", "Coffee", "Plane", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "useImageLoader", "imagePath", "_s", "imageUrl", "setImageUrl", "loading", "setLoading", "error", "setError", "loadImage", "fullUrl", "Error", "console", "log", "response", "fetch", "method", "headers", "window", "location", "origin", "mode", "ok", "status", "statusText", "blob", "objectUrl", "URL", "createObjectURL", "err", "message", "startsWith", "revokeObjectURL", "ImageDisplay", "alt", "style", "className", "onLoad", "onMouseEnter", "onMouseLeave", "_s2", "display", "alignItems", "justifyContent", "backgroundColor", "color", "children", "textAlign", "marginBottom", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "border", "fontWeight", "marginTop", "src", "e", "_c", "FacebookImageGallery", "images", "altPrefix", "maxVisible", "onImageClick", "length", "visibleImages", "slice", "remainingCount", "getImageStyle", "index", "total", "baseStyle", "width", "height", "objectFit", "cursor", "transition", "borderRadius", "getContainerStyle", "position", "overflow", "renderOverlay", "count", "top", "left", "right", "bottom", "gap", "currentTarget", "transform", "onClick", "flexDirection", "map", "image", "idx", "actualIndex", "_c2", "StudentNewsfeed", "_s3", "navigate", "getCategoryStyle", "categoryName", "styles", "background", "icon", "getHolidayTypeStyle", "holidayTypeName", "filterCategory", "setFilterCategory", "filterGradeLevel", "setFilterGradeLevel", "searchTerm", "setSearchTerm", "showComments", "setShowComments", "newComment", "setNewComment", "submittingComment", "setSubmittingComment", "calendarEvents", "setCalendarEvents", "calendarLoading", "setCalendarLoading", "calendarError", "setCalendarError", "announcements", "setAnnouncements", "pinnedAnnouncements", "setPinnedAnnouncements", "categories", "gradeLevels", "Array", "from", "_", "i", "fetchPublishedAnnouncements", "undefined", "data", "json", "success", "announcementsData", "announcementsWithImages", "Promise", "all", "announcement", "imageResponse", "announcement_id", "imageData", "imgErr", "warn", "pinned", "filter", "ann", "is_pinned", "fetchCalendarEvents", "eventsData", "events", "eventsWithImages", "event", "calendar_id", "attachments", "handleLikeToggle", "user_reaction", "removeReaction", "body", "JSON", "stringify", "reaction_type_id", "notify_admin", "handleCommentSubmit", "announcementId", "commentText", "trim", "content", "commenter_name", "commenter_email", "prev", "filteredAnnouncements", "matchesSearch", "title", "toLowerCase", "includes", "matchesCategory", "category_id", "toString", "filteredCalendarEvents", "description", "today", "Date", "setHours", "eventDate", "event_date", "isEventDateValid", "isPublished", "is_published", "displayAnnouncements", "displayEvents", "combinedContent", "item", "type", "sortDate", "created_at", "sort", "a", "b", "getTime", "minHeight", "scroll<PERSON>eh<PERSON>or", "flex", "borderBottom", "zIndex", "boxShadow", "padding", "min<PERSON><PERSON><PERSON>", "margin", "lineHeight", "max<PERSON><PERSON><PERSON>", "size", "placeholder", "value", "onChange", "target", "outline", "onFocus", "borderColor", "onBlur", "category", "name", "grade", "flexShrink", "borderTop", "animation", "<PERSON><PERSON>ilter", "holiday_type_name", "toUpperCase", "holidayStyle", "IconComponent", "toLocaleDateString", "weekday", "year", "month", "day", "eventImageUrls", "for<PERSON>ach", "img", "file_path", "push", "Boolean", "paddingTop", "end_date", "_newComment$announcem", "_newComment$announcem2", "_newComment$announcem3", "_newComment$announcem4", "category_name", "categoryStyle", "author_name", "published_at", "hour", "minute", "imageUrls", "file_url", "image_url", "image_path", "dangerouslySetInnerHTML", "__html", "fill", "reaction_count", "allow_comments", "comment_count", "view_count", "resize", "fontFamily", "disabled", "allowComments", "currentUserType", "_c3", "$RefreshReg$"], "sources": ["D:/capstone-e-bulletin-reactjs-proj/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/student/StudentNewsfeed.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAnnouncements, useCategories } from '../../hooks/useAnnouncements';\nimport { announcementService, calendarService } from '../../services';\nimport CommentSection from '../../components/student/CommentSection';\nimport type { Announcement } from '../../types/announcement.types';\nimport type { AnnouncementAttachment } from '../../services/announcementService';\nimport type { CalendarEvent } from '../../types/calendar.types';\nimport { getImageUrl, API_BASE_URL } from '../../config/constants';\nimport {\n  Home,\n  Search,\n  Pin,\n  Calendar,\n  MessageSquare,\n  Heart,\n  Filter,\n  MapPin,\n  BookOpen,\n  Users,\n  PartyPopper,\n  AlertTriangle,\n  Clock,\n  Trophy,\n  Briefcase,\n  GraduationCap,\n  Flag,\n  Coffee,\n  Plane\n} from 'lucide-react';\n\n// Custom hook for CORS-safe image loading\nconst useImageLoader = (imagePath: string | null) => {\n  const [imageUrl, setImageUrl] = useState<string | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n\n        console.log('🔄 Fetching image via CORS-safe method:', fullUrl);\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Origin': window.location.origin,\n          },\n          mode: 'cors',\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n        setImageUrl(objectUrl);\n\n        console.log('✅ Image loaded successfully via fetch');\n\n      } catch (err) {\n        console.error('❌ Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadImage();\n\n    // Cleanup object URL on unmount\n    return () => {\n      if (imageUrl && imageUrl.startsWith('blob:')) {\n        URL.revokeObjectURL(imageUrl);\n      }\n    };\n  }, [imagePath]);\n\n  return { imageUrl, loading, error };\n};\n\n// Reusable CORS-safe image component\ninterface ImageDisplayProps {\n  imagePath: string | null;\n  alt: string;\n  style?: React.CSSProperties;\n  className?: string;\n  onLoad?: (e: React.SyntheticEvent<HTMLImageElement>) => void;\n  onMouseEnter?: (e: React.MouseEvent<HTMLImageElement>) => void;\n  onMouseLeave?: (e: React.MouseEvent<HTMLImageElement>) => void;\n}\n\nconst ImageDisplay: React.FC<ImageDisplayProps> = ({\n  imagePath,\n  alt,\n  style,\n  className,\n  onLoad,\n  onMouseEnter,\n  onMouseLeave\n}) => {\n  const { imageUrl, loading, error } = useImageLoader(imagePath);\n\n  if (loading) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b'\n      }} className={className}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>⏳</div>\n          <div style={{ fontSize: '0.875rem' }}>Loading...</div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !imageUrl) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b',\n        border: '2px dashed #cbd5e1'\n      }} className={className}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>🖼️</div>\n          <div style={{ fontSize: '0.875rem', fontWeight: '500' }}>Image unavailable</div>\n          {error && (\n            <div style={{ fontSize: '0.75rem', marginTop: '0.25rem', color: '#9ca3af' }}>\n              {error}\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <img\n      src={imageUrl}\n      alt={alt}\n      style={style}\n      className={className}\n      onLoad={(e) => {\n        console.log('✅ Image rendered successfully via CORS-safe method');\n        onLoad?.(e);\n      }}\n      onMouseEnter={onMouseEnter}\n      onMouseLeave={onMouseLeave}\n    />\n  );\n};\n\n// Facebook-style image gallery component\ninterface FacebookImageGalleryProps {\n  images: string[];\n  altPrefix: string;\n  maxVisible?: number;\n  onImageClick?: (index: number) => void;\n}\n\nconst FacebookImageGallery: React.FC<FacebookImageGalleryProps> = ({\n  images,\n  altPrefix,\n  maxVisible = 4,\n  onImageClick\n}) => {\n  if (!images || images.length === 0) return null;\n\n  const visibleImages = images.slice(0, maxVisible);\n  const remainingCount = images.length - maxVisible;\n\n  const getImageStyle = (index: number, total: number): React.CSSProperties => {\n    const baseStyle: React.CSSProperties = {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover',\n      cursor: 'pointer',\n      transition: 'transform 0.2s ease, filter 0.2s ease',\n      borderRadius: index === 0 && total === 1 ? '12px' : '8px'\n    };\n\n    return baseStyle;\n  };\n\n  const getContainerStyle = (index: number, total: number): React.CSSProperties => {\n    const baseStyle: React.CSSProperties = {\n      position: 'relative',\n      overflow: 'hidden',\n      backgroundColor: '#f3f4f6'\n    };\n\n    if (total === 1) {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '400px',\n        borderRadius: '12px'\n      };\n    }\n\n    if (total === 2) {\n      return {\n        ...baseStyle,\n        width: '50%',\n        height: '300px',\n        borderRadius: '8px'\n      };\n    }\n\n    if (total === 3) {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '60%',\n          height: '300px',\n          borderRadius: '8px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '100%',\n          height: '148px',\n          borderRadius: '8px'\n        };\n      }\n    }\n\n    // 4+ images\n    if (index === 0) {\n      return {\n        ...baseStyle,\n        width: '60%',\n        height: '300px',\n        borderRadius: '8px'\n      };\n    } else {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '96px',\n        borderRadius: '8px'\n      };\n    }\n  };\n\n  const renderOverlay = (index: number, count: number) => {\n    if (index === maxVisible - 1 && count > 0) {\n      return (\n        <div style={{\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.6)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontSize: '1.5rem',\n          fontWeight: '600',\n          borderRadius: '8px'\n        }}>\n          +{count}\n        </div>\n      );\n    }\n    return null;\n  };\n\n  return (\n    <div style={{\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1rem'\n    }}>\n      {/* Main image or left side */}\n      <div style={getContainerStyle(0, visibleImages.length)}>\n        <ImageDisplay\n          imagePath={visibleImages[0]}\n          alt={`${altPrefix} - Image 1`}\n          style={getImageStyle(0, visibleImages.length)}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.transform = 'scale(1.02)';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.transform = 'scale(1)';\n          }}\n        />\n        {onImageClick && (\n          <div\n            style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            }}\n            onClick={() => onImageClick(0)}\n          />\n        )}\n      </div>\n\n      {/* Right side images (for 2+ images) */}\n      {visibleImages.length > 1 && (\n        <div style={{\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '4px',\n          width: visibleImages.length === 2 ? '50%' : '40%'\n        }}>\n          {visibleImages.slice(1).map((image, idx) => {\n            const actualIndex = idx + 1;\n            return (\n              <div\n                key={actualIndex}\n                style={getContainerStyle(actualIndex, visibleImages.length)}\n              >\n                <ImageDisplay\n                  imagePath={image}\n                  alt={`${altPrefix} - Image ${actualIndex + 1}`}\n                  style={getImageStyle(actualIndex, visibleImages.length)}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'scale(1.02)';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'scale(1)';\n                  }}\n                />\n                {renderOverlay(actualIndex, remainingCount)}\n                {onImageClick && (\n                  <div\n                    style={{\n                      position: 'absolute',\n                      top: 0,\n                      left: 0,\n                      right: 0,\n                      bottom: 0,\n                      cursor: 'pointer'\n                    }}\n                    onClick={() => onImageClick(actualIndex)}\n                  />\n                )}\n              </div>\n            );\n          })}\n        </div>\n      )}\n    </div>\n  );\n};\n\nconst StudentNewsfeed: React.FC = () => {\n  const navigate = useNavigate();\n\n  // Category styling function\n  const getCategoryStyle = (categoryName: string) => {\n    const styles = {\n      'ACADEMIC': {\n        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n        icon: BookOpen\n      },\n      'GENERAL': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Users\n      },\n      'EVENTS': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: PartyPopper\n      },\n      'EMERGENCY': {\n        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n        icon: AlertTriangle\n      },\n      'SPORTS': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Trophy\n      },\n      'DEADLINES': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Clock\n      }\n    };\n\n    return styles[categoryName as keyof typeof styles] || styles['GENERAL'];\n  };\n\n  // Holiday type styling function\n  const getHolidayTypeStyle = (holidayTypeName: string) => {\n    const styles = {\n      'NATIONAL HOLIDAY': {\n        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n        icon: Flag\n      },\n      'SCHOOL EVENT': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: GraduationCap\n      },\n      'ACADEMIC BREAK': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Coffee\n      },\n      'SPORTS EVENT': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Trophy\n      },\n      'FIELD TRIP': {\n        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n        icon: Plane\n      },\n      'MEETING': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Briefcase\n      }\n    };\n\n    return styles[holidayTypeName as keyof typeof styles] || styles['SCHOOL EVENT'];\n  };\n\n  // Filter states\n  const [filterCategory, setFilterCategory] = useState<string>('');\n  const [filterGradeLevel, setFilterGradeLevel] = useState<string>('');\n  const [searchTerm, setSearchTerm] = useState('');\n  \n  // UI states\n  const [showComments, setShowComments] = useState<number | null>(null);\n  const [newComment, setNewComment] = useState<{ [key: number]: string }>({});\n  const [submittingComment, setSubmittingComment] = useState<number | null>(null);\n\n  // Data states\n  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState<string | undefined>();\n  const [announcements, setAnnouncements] = useState<any[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | undefined>();\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState<any[]>([]);\n\n  const { categories } = useCategories();\n\n  // Grade levels for dropdown (11-12)\n  const gradeLevels = Array.from({ length: 2 }, (_, i) => i + 11); // [11, 12]\n\n  // Fetch published announcements with images\n  const fetchPublishedAnnouncements = async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n\n      const response = await fetch(`${API_BASE_URL}/api/announcements?status=published&page=1&limit=50&sort_by=created_at&sort_order=DESC`);\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        const announcementsData = data.data.announcements || [];\n\n        // Fetch images for each announcement\n        const announcementsWithImages = await Promise.all(\n          announcementsData.map(async (announcement: any) => {\n            try {\n              const imageResponse = await fetch(`${API_BASE_URL}/api/announcements/${announcement.announcement_id}/images`);\n              const imageData = await imageResponse.json();\n\n              if (imageData.success && imageData.data) {\n                announcement.images = imageData.data.images || [];\n              } else {\n                announcement.images = [];\n              }\n            } catch (imgErr) {\n              console.warn(`Failed to fetch images for announcement ${announcement.announcement_id}:`, imgErr);\n              announcement.images = [];\n            }\n            return announcement;\n          })\n        );\n\n        setAnnouncements(announcementsWithImages);\n\n        // Separate pinned announcements\n        const pinned = announcementsWithImages.filter((ann: any) => ann.is_pinned === 1);\n        setPinnedAnnouncements(pinned);\n      } else {\n        setError('Failed to load announcements');\n      }\n    } catch (err: any) {\n      console.error('Error fetching announcements:', err);\n      setError(err.message || 'Failed to load announcements');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch calendar events\n  const fetchCalendarEvents = async () => {\n    try {\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n\n      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`);\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(\n          eventsData.map(async (event: any) => {\n            try {\n              const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`);\n              const imageData = await imageResponse.json();\n\n              if (imageData.success && imageData.data) {\n                event.images = imageData.data.attachments || [];\n              } else {\n                event.images = [];\n              }\n            } catch (imgErr) {\n              console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n              event.images = [];\n            }\n            return event;\n          })\n        );\n\n        setCalendarEvents(eventsWithImages);\n      } else {\n        setCalendarError('Failed to load calendar events');\n      }\n    } catch (err: any) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to load calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  };\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchPublishedAnnouncements();\n    fetchCalendarEvents();\n  }, []);\n\n  // Handle like/unlike functionality\n  const handleLikeToggle = async (announcement: any) => {\n    try {\n      if (announcement.user_reaction) {\n        await announcementService.removeReaction(announcement.announcement_id);\n      } else {\n        const response = await fetch(`${API_BASE_URL}/api/announcements/${announcement.announcement_id}/reactions`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            reaction_type_id: 1,\n            notify_admin: true\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error('Failed to add reaction');\n        }\n      }\n      \n      // Refresh data\n      await fetchPublishedAnnouncements();\n    } catch (error) {\n      console.error('Error toggling like:', error);\n    }\n  };\n\n  // Handle comment submission\n  const handleCommentSubmit = async (announcementId: number, commentText: string) => {\n    if (!commentText.trim()) return;\n\n    try {\n      setSubmittingComment(announcementId);\n\n      const response = await fetch(`${API_BASE_URL}/api/announcements/${announcementId}/comments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          content: commentText,\n          commenter_name: 'Student User',\n          commenter_email: '<EMAIL>',\n          notify_admin: true\n        })\n      });\n\n      if (response.ok) {\n        setNewComment(prev => ({ ...prev, [announcementId]: '' }));\n        await fetchPublishedAnnouncements();\n      } else {\n        console.error('Failed to submit comment');\n      }\n    } catch (error) {\n      console.error('Error submitting comment:', error);\n    } finally {\n      setSubmittingComment(null);\n    }\n  };\n\n  // Filter announcements\n  const filteredAnnouncements = announcements.filter(announcement => {\n    const matchesSearch = !searchTerm ||\n      announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      announcement.content.toLowerCase().includes(searchTerm.toLowerCase());\n    \n    const matchesCategory = !filterCategory || announcement.category_id.toString() === filterCategory;\n    \n    // Note: Grade level filtering would require additional backend support\n    // For now, we'll show all announcements regardless of grade level filter\n    \n    return matchesSearch && matchesCategory;\n  });\n\n  // Filter calendar events with date-based filtering\n  const filteredCalendarEvents = calendarEvents.filter(event => {\n    const matchesSearch = !searchTerm ||\n      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      (event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    // Only show events that are published and on or after today's date\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    const eventDate = new Date(event.event_date);\n    eventDate.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    const isEventDateValid = eventDate >= today;\n    const isPublished = (event as any).is_published === 1;\n\n    return matchesSearch && isEventDateValid && isPublished;\n  });\n\n  // Combine and sort all content by date (most recent first)\n  const displayAnnouncements = filteredAnnouncements;\n  const displayEvents = filteredCalendarEvents;\n\n  // Create combined content array for better chronological display\n  const combinedContent = [\n    ...displayAnnouncements.map(item => ({ ...item, type: 'announcement', sortDate: new Date(item.created_at) })),\n    ...displayEvents.map(item => ({ ...item, type: 'event', sortDate: new Date(item.event_date) }))\n  ].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());\n\n  return (\n    <>\n      {/* CSS Animations */}\n      <style>\n        {`\n          html {\n            scroll-behavior: smooth;\n          }\n\n          @keyframes spin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n          }\n\n          @media (max-width: 768px) {\n            .mobile-hide { display: none !important; }\n            .mobile-full { width: 100% !important; margin-left: 0 !important; }\n            .mobile-stack {\n              flex-direction: column !important;\n              gap: 0.75rem !important;\n            }\n            .mobile-grid { grid-template-columns: 1fr !important; }\n          }\n\n          @media (max-width: 480px) {\n            .mobile-small-padding { padding: 0.75rem !important; }\n            .mobile-small-text { font-size: 0.8rem !important; }\n            .mobile-compact-header {\n              padding: 0.75rem 1rem !important;\n            }\n            .mobile-compact-title {\n              font-size: 1.25rem !important;\n            }\n            .mobile-compact-search {\n              max-width: 200px !important;\n            }\n          }\n        `}\n      </style>\n\n      <div style={{\n        display: 'flex',\n        minHeight: '100vh',\n        background: 'linear-gradient(135deg, #f0f9ff 0%, #fefce8 100%)',\n        scrollBehavior: 'smooth'\n      }}>\n\n      {/* Main Content Area */}\n      <div\n        className=\"mobile-full\"\n        style={{\n          flex: 1,\n          display: 'flex',\n          flexDirection: 'column',\n          minHeight: '100vh',\n          width: '100%'\n        }}>\n        {/* Modern Student Header */}\n        <header style={{\n          background: 'white',\n          borderBottom: '1px solid #e5e7eb',\n          position: 'sticky',\n          top: 0,\n          zIndex: 100,\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n        }}>\n          <div style={{\n            width: '100%',\n            padding: '0 2rem',\n            height: '72px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          }}>\n            {/* Left Section: Logo + Page Title */}\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1.5rem',\n              minWidth: '300px'\n            }}>\n              <img\n                src=\"/logo/vcba1.png\"\n                alt=\"VCBA Logo\"\n                style={{\n                  width: '48px',\n                  height: '48px',\n                  objectFit: 'contain'\n                }}\n              />\n              <div>\n                <h1 style={{\n                  margin: 0,\n                  fontSize: '1.5rem',\n                  fontWeight: '700',\n                  color: '#111827',\n                  lineHeight: '1.2'\n                }}>\n                  VCBA E-Bulletin Board\n                </h1>\n                <p style={{\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                }}>\n                  Student Newsfeed\n                </p>\n              </div>\n            </div>\n\n            {/* Center Section: Search */}\n            <div style={{\n              flex: 1,\n              maxWidth: '500px',\n              margin: '0 2rem'\n            }}>\n              <div style={{ position: 'relative' }}>\n                <Search\n                  size={20}\n                  style={{\n                    position: 'absolute',\n                    left: '1rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: '#9ca3af'\n                  }}\n                />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search announcements and events...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  style={{\n                    width: '100%',\n                    height: '44px',\n                    padding: '0 1rem 0 3rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '12px',\n                    background: '#f9fafb',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onFocus={(e) => {\n                    e.target.style.borderColor = '#3b82f6';\n                    e.target.style.background = 'white';\n                    e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';\n                  }}\n                  onBlur={(e) => {\n                    e.target.style.borderColor = '#d1d5db';\n                    e.target.style.background = '#f9fafb';\n                    e.target.style.boxShadow = 'none';\n                  }}\n                />\n              </div>\n            </div>\n\n            {/* Right Section: Filters + Actions */}\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              minWidth: '400px',\n              justifyContent: 'flex-end'\n            }}>\n              {/* Filters Group */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.5rem',\n                background: '#f9fafb',\n                borderRadius: '12px',\n                border: '1px solid #e5e7eb'\n              }}>\n                <select\n                  value={filterCategory}\n                  onChange={(e) => setFilterCategory(e.target.value)}\n                  style={{\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '110px'\n                  }}\n                >\n                  <option value=\"\">All Categories</option>\n                  {categories.map(category => (\n                    <option key={category.category_id} value={category.category_id.toString()}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n\n                <select\n                  value={filterGradeLevel}\n                  onChange={(e) => setFilterGradeLevel(e.target.value)}\n                  style={{\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '100px'\n                  }}\n                >\n                  <option value=\"\">All Grades</option>\n                  {gradeLevels.map(grade => (\n                    <option key={grade} value={grade.toString()}>\n                      Grade {grade}\n                    </option>\n                  ))}\n                </select>\n\n                {(searchTerm || filterCategory || filterGradeLevel) && (\n                  <button\n                    onClick={() => {\n                      setSearchTerm('');\n                      setFilterCategory('');\n                      setFilterGradeLevel('');\n                    }}\n                    style={{\n                      padding: '0.5rem 0.75rem',\n                      border: 'none',\n                      borderRadius: '8px',\n                      background: '#ef4444',\n                      color: 'white',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.background = '#dc2626';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.background = '#ef4444';\n                    }}\n                  >\n                    Clear\n                  </button>\n                )}\n              </div>\n\n\n\n              {/* Dashboard Button */}\n              <button\n                onClick={() => navigate('/student/dashboard')}\n                style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  padding: '0.5rem 1rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '8px',\n                  background: 'white',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.borderColor = '#3b82f6';\n                  e.currentTarget.style.color = '#3b82f6';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.borderColor = '#d1d5db';\n                  e.currentTarget.style.color = '#374151';\n                }}\n              >\n                <Home size={16} />\n                Dashboard\n              </button>\n            </div>\n          </div>\n        </header>\n\n        {/* Main Content Layout */}\n        <div style={{\n          flex: 1,\n          display: 'flex',\n          gap: '1.5rem',\n          padding: '1.5rem 2rem',\n          background: 'transparent',\n          alignItems: 'flex-start'\n        }}>\n          {/* Left Sidebar: Pinned Posts */}\n          <div style={{\n            width: '280px',\n            flexShrink: 0\n          }}>\n            <div style={{\n              background: 'white',\n              borderRadius: '14px',\n              border: '1px solid #e5e7eb',\n              overflow: 'hidden',\n              position: 'sticky',\n              top: '80px',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n            }}>\n              {/* Pinned Posts Header */}\n              <div style={{\n                padding: '1rem 1.25rem 0.75rem',\n                borderBottom: '1px solid #f3f4f6',\n                background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.75rem',\n                  marginBottom: '0.5rem'\n                }}>\n                  <Pin size={20} style={{ color: 'white' }} />\n                  <h3 style={{\n                    margin: 0,\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: 'white'\n                  }}>\n                    Important Updates\n                  </h3>\n                </div>\n                <p style={{\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: 'rgba(255, 255, 255, 0.8)'\n                }}>\n                  Don't miss these announcements\n                </p>\n              </div>\n\n              {/* Pinned Posts List */}\n              <div style={{ padding: '0.75rem' }}>\n                {/* Sample Pinned Post 1 */}\n                <div style={{\n                  padding: '0.75rem',\n                  background: 'linear-gradient(135deg, #fef3c7 0%, #fde68a 100%)',\n                  borderRadius: '10px',\n                  border: '1px solid #f59e0b',\n                  marginBottom: '0.75rem',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.transform = 'translateY(-2px)';\n                  e.currentTarget.style.boxShadow = '0 8px 25px rgba(245, 158, 11, 0.3)';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = 'none';\n                }}>\n                  <div style={{\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    gap: '0.75rem'\n                  }}>\n                    <div style={{\n                      width: '10px',\n                      height: '10px',\n                      background: '#f59e0b',\n                      borderRadius: '50%',\n                      marginTop: '0.5rem',\n                      flexShrink: 0,\n                      boxShadow: '0 0 0 3px rgba(245, 158, 11, 0.3)'\n                    }} />\n                    <div style={{ flex: 1 }}>\n                      <h4 style={{\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '0.875rem',\n                        fontWeight: '600',\n                        color: '#92400e',\n                        lineHeight: '1.4'\n                      }}>\n                        📚 Final Exams Schedule Released\n                      </h4>\n                      <p style={{\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '0.8rem',\n                        color: '#a16207',\n                        lineHeight: '1.4'\n                      }}>\n                        Check your exam dates and prepare accordingly. Good luck!\n                      </p>\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        fontSize: '0.75rem',\n                        color: '#a16207'\n                      }}>\n                        <Calendar size={12} />\n                        <span>3 days ago</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Sample Pinned Post 2 */}\n                <div style={{\n                  padding: '0.75rem',\n                  background: 'linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)',\n                  borderRadius: '10px',\n                  border: '1px solid #3b82f6',\n                  marginBottom: '0.75rem',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.transform = 'translateY(-2px)';\n                  e.currentTarget.style.boxShadow = '0 8px 25px rgba(59, 130, 246, 0.3)';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = 'none';\n                }}>\n                  <div style={{\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    gap: '0.75rem'\n                  }}>\n                    <div style={{\n                      width: '10px',\n                      height: '10px',\n                      background: '#3b82f6',\n                      borderRadius: '50%',\n                      marginTop: '0.5rem',\n                      flexShrink: 0,\n                      boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.3)'\n                    }} />\n                    <div style={{ flex: 1 }}>\n                      <h4 style={{\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '0.875rem',\n                        fontWeight: '600',\n                        color: '#1e40af',\n                        lineHeight: '1.4'\n                      }}>\n                        🎓 Graduation Ceremony Details\n                      </h4>\n                      <p style={{\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '0.8rem',\n                        color: '#1d4ed8',\n                        lineHeight: '1.4'\n                      }}>\n                        Important information about the upcoming graduation ceremony...\n                      </p>\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        fontSize: '0.75rem',\n                        color: '#1d4ed8'\n                      }}>\n                        <Calendar size={12} />\n                        <span>1 week ago</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Sample Pinned Post 3 */}\n                <div style={{\n                  padding: '0.75rem',\n                  background: 'linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%)',\n                  borderRadius: '10px',\n                  border: '1px solid #22c55e',\n                  marginBottom: '0.75rem',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.transform = 'translateY(-2px)';\n                  e.currentTarget.style.boxShadow = '0 8px 25px rgba(34, 197, 94, 0.3)';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = 'none';\n                }}>\n                  <div style={{\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    gap: '0.75rem'\n                  }}>\n                    <div style={{\n                      width: '10px',\n                      height: '10px',\n                      background: '#22c55e',\n                      borderRadius: '50%',\n                      marginTop: '0.5rem',\n                      flexShrink: 0,\n                      boxShadow: '0 0 0 3px rgba(34, 197, 94, 0.3)'\n                    }} />\n                    <div style={{ flex: 1 }}>\n                      <h4 style={{\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '0.875rem',\n                        fontWeight: '600',\n                        color: '#15803d',\n                        lineHeight: '1.4'\n                      }}>\n                        🏆 Academic Excellence Awards\n                      </h4>\n                      <p style={{\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '0.8rem',\n                        color: '#16a34a',\n                        lineHeight: '1.4'\n                      }}>\n                        Congratulations to all students who achieved academic excellence...\n                      </p>\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        fontSize: '0.75rem',\n                        color: '#16a34a'\n                      }}>\n                        <Calendar size={12} />\n                        <span>2 weeks ago</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* View All Link */}\n                <button style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #e5e7eb',\n                  borderRadius: '12px',\n                  background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',\n                  color: '#3b82f6',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.background = 'linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)';\n                  e.currentTarget.style.borderColor = '#3b82f6';\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.background = 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)';\n                  e.currentTarget.style.borderColor = '#e5e7eb';\n                  e.currentTarget.style.transform = 'translateY(0)';\n                }}>\n                  📌 View All Important Updates\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Right Content: Main Feed */}\n          <div style={{ flex: 1, minWidth: 0 }}>\n          {/* Loading State */}\n          {(loading || calendarLoading) && (\n            <div style={{\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              padding: '3rem'\n            }}>\n              <div style={{\n                width: '2.5rem',\n                height: '2.5rem',\n                border: '3px solid #e5e7eb',\n                borderTop: '3px solid #3b82f6',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }}></div>\n            </div>\n          )}\n\n          {/* Error Messages */}\n          {(error || calendarError) && (\n            <div style={{\n              background: 'rgba(239, 68, 68, 0.1)',\n              border: '1px solid rgba(239, 68, 68, 0.2)',\n              borderRadius: '12px',\n              padding: '1rem',\n              marginBottom: '1.5rem',\n              color: '#dc2626'\n            }}>\n              {error && <div>Announcements: {error}</div>}\n              {calendarError && <div>Calendar: {calendarError}</div>}\n            </div>\n          )}\n\n          {/* No Content */}\n          {!loading && !calendarLoading && displayAnnouncements.length === 0 && displayEvents.length === 0 && (\n            <div style={{\n              background: 'rgba(255, 255, 255, 0.8)',\n              borderRadius: '16px',\n              padding: '3rem',\n              textAlign: 'center',\n              border: '1px solid rgba(0, 0, 0, 0.1)',\n              backdropFilter: 'blur(10px)'\n            }}>\n              <div style={{\n                marginBottom: '1rem'\n              }}>\n                <Filter size={48} color=\"#9ca3af\" />\n              </div>\n              <h3 style={{\n                color: '#374151',\n                fontSize: '1.25rem',\n                fontWeight: '600',\n                margin: '0 0 0.5rem 0'\n              }}>\n                No updates found\n              </h3>\n              <p style={{\n                color: '#6b7280',\n                margin: 0\n              }}>\n                {searchTerm || filterCategory || filterGradeLevel\n                  ? 'Try adjusting your filters to see more content.'\n                  : 'Check back later for new updates.'}\n              </p>\n            </div>\n          )}\n\n          {/* Content Feed */}\n          {!loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && (\n            <div style={{\n              maxWidth: '1200px',\n              margin: '0 auto',\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem'\n            }}>\n              {/* Calendar Events */}\n              {displayEvents.length > 0 && (\n                <>\n                  {displayEvents.map(event => (\n                    <div\n                      key={`event-${event.calendar_id}`}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Event Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        {(() => {\n                          const holidayTypeName = (event.holiday_type_name || 'SCHOOL EVENT').toUpperCase();\n                          const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                          const IconComponent = holidayStyle.icon;\n\n                          return (\n                            <div style={{\n                              width: '50px',\n                              height: '50px',\n                              borderRadius: '12px',\n                              background: holidayStyle.background,\n                              display: 'flex',\n                              alignItems: 'center',\n                              justifyContent: 'center'\n                            }}>\n                              <IconComponent size={20} color=\"white\" />\n                            </div>\n                          );\n                        })()}\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            marginBottom: '0.25rem'\n                          }}>\n                            {(() => {\n                              const holidayTypeName = (event.holiday_type_name || 'SCHOOL EVENT').toUpperCase();\n                              const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                              const IconComponent = holidayStyle.icon;\n\n                              return (\n                                <span style={{\n                                  background: holidayStyle.background,\n                                  color: 'white',\n                                  padding: '0.25rem 0.75rem',\n                                  borderRadius: '12px',\n                                  fontSize: '0.75rem',\n                                  fontWeight: '600',\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: '0.25rem'\n                                }}>\n                                  <IconComponent size={12} color=\"white\" />\n                                  {holidayTypeName}\n                                </span>\n                              );\n                            })()}\n                          </div>\n                          <div style={{\n                            color: '#9ca3af',\n                            fontSize: '0.8rem'\n                          }}>\n                            {new Date(event.event_date).toLocaleDateString('en-US', {\n                              weekday: 'long',\n                              year: 'numeric',\n                              month: 'long',\n                              day: 'numeric'\n                            })}\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Event Content */}\n                      <h3 style={{\n                        margin: '0 0 0.75rem 0',\n                        color: '#1f2937',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        lineHeight: '1.3'\n                      }}>\n                        {event.title}\n                      </h3>\n\n                      {event.description && (\n                        <p style={{\n                          margin: '0 0 1rem 0',\n                          color: '#4b5563',\n                          fontSize: '0.95rem',\n                          lineHeight: '1.6'\n                        }}>\n                          {event.description}\n                        </p>\n                      )}\n\n                      {/* Event Images */}\n                      {(() => {\n                        // Get event images if they exist\n                        const eventImageUrls: string[] = [];\n\n                        if ((event as any).images && (event as any).images.length > 0) {\n                          (event as any).images.forEach((img: any) => {\n                            if (img.file_path) {\n                              // Convert file_path to full URL\n                              const imageUrl = getImageUrl(img.file_path);\n                              if (imageUrl) {\n                                eventImageUrls.push(imageUrl);\n                              }\n                            }\n                          });\n                        }\n\n                        return eventImageUrls.length > 0 ? (\n                          <div style={{ marginBottom: '1rem' }}>\n                            <FacebookImageGallery\n                              images={eventImageUrls.filter(Boolean) as string[]}\n                              altPrefix={event.title}\n                              maxVisible={4}\n                              onImageClick={(index) => {\n                                console.log(`Clicked image ${index + 1} for event: ${event.title}`);\n                              }}\n                            />\n                          </div>\n                        ) : null;\n                      })()}\n\n                      {/* Event Footer */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        paddingTop: '1rem',\n                        borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem',\n                          color: '#6b7280',\n                          fontSize: '0.875rem'\n                        }}>\n                          <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                            <MapPin size={14} color=\"#6b7280\" />\n                            School Event\n                          </span>\n                          {event.end_date && event.end_date !== event.event_date && (\n                            <span>\n                              Until {new Date(event.end_date).toLocaleDateString()}\n                            </span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </>\n              )}\n\n              {/* Announcements */}\n              {displayAnnouncements.length > 0 && (\n                <>\n                  {displayAnnouncements.map(announcement => (\n                    <div\n                      key={`announcement-${announcement.announcement_id}`}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Announcement Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        {(() => {\n                          const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                          const categoryStyle = getCategoryStyle(categoryName);\n                          const IconComponent = categoryStyle.icon;\n\n                          return (\n                            <div style={{\n                              width: '50px',\n                              height: '50px',\n                              borderRadius: '12px',\n                              background: categoryStyle.background,\n                              display: 'flex',\n                              alignItems: 'center',\n                              justifyContent: 'center'\n                            }}>\n                              <IconComponent size={20} color=\"white\" />\n                            </div>\n                          );\n                        })()}\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            marginBottom: '0.25rem'\n                          }}>\n                            {(() => {\n                              const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                              const categoryStyle = getCategoryStyle(categoryName);\n                              const IconComponent = categoryStyle.icon;\n\n                              return (\n                                <span style={{\n                                  background: categoryStyle.background,\n                                  color: 'white',\n                                  padding: '0.25rem 0.75rem',\n                                  borderRadius: '12px',\n                                  fontSize: '0.75rem',\n                                  fontWeight: '600',\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: '0.25rem'\n                                }}>\n                                  <IconComponent size={12} color=\"white\" />\n                                  {categoryName}\n                                </span>\n                              );\n                            })()}\n                            {announcement.is_pinned === 1 && (\n                              <span style={{\n                                background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n                                color: 'white',\n                                padding: '0.25rem 0.75rem',\n                                borderRadius: '12px',\n                                fontSize: '0.75rem',\n                                fontWeight: '600',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.25rem'\n                              }}>\n                                <Pin size={12} color=\"white\" />\n                                PINNED\n                              </span>\n                            )}\n                          </div>\n                          <div style={{\n                            color: '#9ca3af',\n                            fontSize: '0.8rem'\n                          }}>\n                            By {announcement.author_name} • {new Date(announcement.published_at).toLocaleDateString('en-US', {\n                              weekday: 'short',\n                              year: 'numeric',\n                              month: 'short',\n                              day: 'numeric',\n                              hour: '2-digit',\n                              minute: '2-digit'\n                            })}\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Announcement Content */}\n                      <h3 style={{\n                        margin: '0 0 0.75rem 0',\n                        color: '#1f2937',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        lineHeight: '1.3'\n                      }}>\n                        {announcement.title}\n                      </h3>\n\n                      {/* Images - Facebook-style Gallery */}\n                      {(() => {\n                        // Get images from multiple sources\n                        const imageUrls: string[] = [];\n\n                        // Add images from attachments (new multiple image system)\n                        if (announcement.images && announcement.images.length > 0) {\n                          announcement.images.forEach((img: AnnouncementAttachment) => {\n                            if (img.file_url) {\n                              imageUrls.push(img.file_url);\n                            }\n                          });\n                        }\n\n                        // Fallback to legacy single image\n                        if (imageUrls.length === 0 && (announcement.image_url || announcement.image_path)) {\n                          imageUrls.push(announcement.image_url || announcement.image_path);\n                        }\n\n                        return imageUrls.length > 0 ? (\n                          <FacebookImageGallery\n                            images={imageUrls.filter(Boolean) as string[]}\n                            altPrefix={announcement.title}\n                            maxVisible={4}\n                            onImageClick={(index) => {\n                              console.log(`Clicked image ${index + 1} for announcement: ${announcement.title}`);\n                              // Future: Open image viewer/lightbox\n                            }}\n                          />\n                        ) : null;\n                      })()}\n\n                      <div style={{\n                        margin: '0 0 1.5rem 0',\n                        color: '#4b5563',\n                        fontSize: '0.95rem',\n                        lineHeight: '1.6'\n                      }}\n                      dangerouslySetInnerHTML={{ __html: announcement.content }}\n                      />\n\n                      {/* Announcement Actions */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        paddingTop: '1rem',\n                        borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem'\n                        }}>\n                          {/* Like Button */}\n                          <button\n                            onClick={() => handleLikeToggle(announcement)}\n                            style={{\n                              background: announcement.user_reaction ? 'rgba(239, 68, 68, 0.1)' : 'transparent',\n                              color: announcement.user_reaction ? '#dc2626' : '#6b7280',\n                              border: 'none',\n                              borderRadius: '8px',\n                              padding: '0.5rem 1rem',\n                              fontSize: '0.875rem',\n                              fontWeight: '600',\n                              cursor: 'pointer',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.5rem',\n                              transition: 'all 0.2s ease'\n                            }}\n                            onMouseEnter={(e) => {\n                              if (!announcement.user_reaction) {\n                                e.currentTarget.style.background = 'rgba(239, 68, 68, 0.1)';\n                                e.currentTarget.style.color = '#dc2626';\n                              }\n                            }}\n                            onMouseLeave={(e) => {\n                              if (!announcement.user_reaction) {\n                                e.currentTarget.style.background = 'transparent';\n                                e.currentTarget.style.color = '#6b7280';\n                              }\n                            }}\n                          >\n                            <Heart\n                              size={16}\n                              color={announcement.user_reaction ? \"#dc2626\" : \"#9ca3af\"}\n                              fill={announcement.user_reaction ? \"#dc2626\" : \"none\"}\n                            />\n                            <span>{announcement.reaction_count || 0}</span>\n                          </button>\n\n                          {/* Comment Button */}\n                          {announcement.allow_comments && (\n                            <button\n                              onClick={() => setShowComments(\n                                showComments === announcement.announcement_id ? null : announcement.announcement_id\n                              )}\n                              style={{\n                                background: showComments === announcement.announcement_id ? 'rgba(59, 130, 246, 0.1)' : 'transparent',\n                                color: showComments === announcement.announcement_id ? '#3b82f6' : '#6b7280',\n                                border: 'none',\n                                borderRadius: '8px',\n                                padding: '0.5rem 1rem',\n                                fontSize: '0.875rem',\n                                fontWeight: '600',\n                                cursor: 'pointer',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                transition: 'all 0.2s ease'\n                              }}\n                              onMouseEnter={(e) => {\n                                if (showComments !== announcement.announcement_id) {\n                                  e.currentTarget.style.background = 'rgba(59, 130, 246, 0.1)';\n                                  e.currentTarget.style.color = '#3b82f6';\n                                }\n                              }}\n                              onMouseLeave={(e) => {\n                                if (showComments !== announcement.announcement_id) {\n                                  e.currentTarget.style.background = 'transparent';\n                                  e.currentTarget.style.color = '#6b7280';\n                                }\n                              }}\n                            >\n                              <MessageSquare size={16} color=\"#6b7280\" />\n                              <span>{announcement.comment_count || 0}</span>\n                            </button>\n                          )}\n                        </div>\n\n                        <div style={{\n                          color: '#9ca3af',\n                          fontSize: '0.8rem'\n                        }}>\n                          {announcement.view_count || 0} views\n                        </div>\n                      </div>\n\n                      {/* Comments Section */}\n                      {showComments === announcement.announcement_id && announcement.allow_comments && (\n                        <div style={{\n                          marginTop: '1rem',\n                          paddingTop: '1rem',\n                          borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                        }}>\n                          {/* Comment Input */}\n                          <div style={{\n                            display: 'flex',\n                            gap: '0.75rem',\n                            marginBottom: '1rem'\n                          }}>\n                            <div style={{\n                              width: '40px',\n                              height: '40px',\n                              borderRadius: '50%',\n                              background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                              display: 'flex',\n                              alignItems: 'center',\n                              justifyContent: 'center',\n                              color: 'white',\n                              fontWeight: '600',\n                              fontSize: '0.875rem'\n                            }}>\n                              S\n                            </div>\n                            <div style={{ flex: 1 }}>\n                              <textarea\n                                value={newComment[announcement.announcement_id] || ''}\n                                onChange={(e) => setNewComment(prev => ({\n                                  ...prev,\n                                  [announcement.announcement_id]: e.target.value\n                                }))}\n                                placeholder=\"Write a comment...\"\n                                style={{\n                                  width: '100%',\n                                  minHeight: '80px',\n                                  padding: '0.75rem',\n                                  border: '1px solid #e5e7eb',\n                                  borderRadius: '12px',\n                                  fontSize: '0.9rem',\n                                  outline: 'none',\n                                  resize: 'vertical',\n                                  fontFamily: 'inherit'\n                                }}\n                              />\n                              <div style={{\n                                display: 'flex',\n                                justifyContent: 'flex-end',\n                                marginTop: '0.5rem'\n                              }}>\n                                <button\n                                  onClick={() => handleCommentSubmit(\n                                    announcement.announcement_id,\n                                    newComment[announcement.announcement_id] || ''\n                                  )}\n                                  disabled={!newComment[announcement.announcement_id]?.trim() || submittingComment === announcement.announcement_id}\n                                  style={{\n                                    background: newComment[announcement.announcement_id]?.trim()\n                                      ? 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)'\n                                      : '#e5e7eb',\n                                    color: newComment[announcement.announcement_id]?.trim() ? 'white' : '#9ca3af',\n                                    border: 'none',\n                                    borderRadius: '8px',\n                                    padding: '0.5rem 1rem',\n                                    fontSize: '0.875rem',\n                                    fontWeight: '600',\n                                    cursor: newComment[announcement.announcement_id]?.trim() ? 'pointer' : 'not-allowed',\n                                    transition: 'all 0.2s ease'\n                                  }}\n                                >\n                                  {submittingComment === announcement.announcement_id ? 'Posting...' : 'Post Comment'}\n                                </button>\n                              </div>\n                            </div>\n                          </div>\n\n                          {/* Existing Comments */}\n                          <CommentSection\n                            announcementId={announcement.announcement_id}\n                            allowComments={announcement.allow_comments}\n                            currentUserType=\"student\"\n                          />\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </>\n              )}\n            </div>\n          )}\n          </div>\n        </div>\n      </div>\n    </div>\n    </>\n  );\n};\n\nexport default StudentNewsfeed;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAA2BC,aAAa,QAAQ,8BAA8B;AAC9E,SAASC,mBAAmB,QAAyB,gBAAgB;AACrE,OAAOC,cAAc,MAAM,yCAAyC;AAIpE,SAASC,WAAW,EAAEC,YAAY,QAAQ,wBAAwB;AAClE,SACEC,IAAI,EACJC,MAAM,EACNC,GAAG,EACHC,QAAQ,EACRC,aAAa,EACbC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,aAAa,EACbC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,aAAa,EACbC,IAAI,EACJC,MAAM,EACNC,KAAK,QACA,cAAc;;AAErB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAIC,SAAwB,IAAK;EAAAC,EAAA;EACnD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnC,QAAQ,CAAgB,IAAI,CAAC;EAC7D,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,IAAI,CAAC+B,SAAS,EAAE;MACdG,WAAW,CAAC,IAAI,CAAC;MACjB;IACF;IAEA,MAAMK,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI;QACF,MAAME,OAAO,GAAGnC,WAAW,CAAC0B,SAAS,CAAC;QACtC,IAAI,CAACS,OAAO,EAAE;UACZ,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;QACvC;QAEAC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEH,OAAO,CAAC;;QAE/D;QACA,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAACL,OAAO,EAAE;UACpCM,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,QAAQ,EAAEC,MAAM,CAACC,QAAQ,CAACC;UAC5B,CAAC;UACDC,IAAI,EAAE;QACR,CAAC,CAAC;QAEF,IAAI,CAACP,QAAQ,CAACQ,EAAE,EAAE;UAChB,MAAM,IAAIX,KAAK,CAAC,QAAQG,QAAQ,CAACS,MAAM,KAAKT,QAAQ,CAACU,UAAU,EAAE,CAAC;QACpE;QAEA,MAAMC,IAAI,GAAG,MAAMX,QAAQ,CAACW,IAAI,CAAC,CAAC;QAClC,MAAMC,SAAS,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;QAC3CrB,WAAW,CAACsB,SAAS,CAAC;QAEtBd,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MAEtD,CAAC,CAAC,OAAOgB,GAAG,EAAE;QACZjB,OAAO,CAACL,KAAK,CAAC,uBAAuB,EAAEsB,GAAG,CAAC;QAC3CrB,QAAQ,CAACqB,GAAG,YAAYlB,KAAK,GAAGkB,GAAG,CAACC,OAAO,GAAG,sBAAsB,CAAC;MACvE,CAAC,SAAS;QACRxB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,SAAS,CAAC,CAAC;;IAEX;IACA,OAAO,MAAM;MACX,IAAIN,QAAQ,IAAIA,QAAQ,CAAC4B,UAAU,CAAC,OAAO,CAAC,EAAE;QAC5CJ,GAAG,CAACK,eAAe,CAAC7B,QAAQ,CAAC;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,CAACF,SAAS,CAAC,CAAC;EAEf,OAAO;IAAEE,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC;AACrC,CAAC;;AAED;AAAAL,EAAA,CA/DMF,cAAc;AA0EpB,MAAMiC,YAAyC,GAAGA,CAAC;EACjDhC,SAAS;EACTiC,GAAG;EACHC,KAAK;EACLC,SAAS;EACTC,MAAM;EACNC,YAAY;EACZC;AACF,CAAC,KAAK;EAAAC,GAAA;EACJ,MAAM;IAAErC,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC,GAAGP,cAAc,CAACC,SAAS,CAAC;EAE9D,IAAII,OAAO,EAAE;IACX,oBACER,OAAA;MAAKsC,KAAK,EAAE;QACV,GAAGA,KAAK;QACRM,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE;MACT,CAAE;MAACT,SAAS,EAAEA,SAAU;MAAAU,QAAA,eACtBjD,OAAA;QAAKsC,KAAK,EAAE;UAAEY,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCjD,OAAA;UAAKsC,KAAK,EAAE;YAAEa,YAAY,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAAH,QAAA,EAAC;QAAC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnExD,OAAA;UAAKsC,KAAK,EAAE;YAAEc,QAAQ,EAAE;UAAW,CAAE;UAAAH,QAAA,EAAC;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI9C,KAAK,IAAI,CAACJ,QAAQ,EAAE;IACtB,oBACEN,OAAA;MAAKsC,KAAK,EAAE;QACV,GAAGA,KAAK;QACRM,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE,SAAS;QAChBS,MAAM,EAAE;MACV,CAAE;MAAClB,SAAS,EAAEA,SAAU;MAAAU,QAAA,eACtBjD,OAAA;QAAKsC,KAAK,EAAE;UAAEY,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCjD,OAAA;UAAKsC,KAAK,EAAE;YAAEa,YAAY,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAAH,QAAA,EAAC;QAAG;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrExD,OAAA;UAAKsC,KAAK,EAAE;YAAEc,QAAQ,EAAE,UAAU;YAAEM,UAAU,EAAE;UAAM,CAAE;UAAAT,QAAA,EAAC;QAAiB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAC/E9C,KAAK,iBACJV,OAAA;UAAKsC,KAAK,EAAE;YAAEc,QAAQ,EAAE,SAAS;YAAEO,SAAS,EAAE,SAAS;YAAEX,KAAK,EAAE;UAAU,CAAE;UAAAC,QAAA,EACzEvC;QAAK;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACExD,OAAA;IACE4D,GAAG,EAAEtD,QAAS;IACd+B,GAAG,EAAEA,GAAI;IACTC,KAAK,EAAEA,KAAM;IACbC,SAAS,EAAEA,SAAU;IACrBC,MAAM,EAAGqB,CAAC,IAAK;MACb9C,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjEwB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAGqB,CAAC,CAAC;IACb,CAAE;IACFpB,YAAY,EAAEA,YAAa;IAC3BC,YAAY,EAAEA;EAAa;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5B,CAAC;AAEN,CAAC;;AAED;AAAAb,GAAA,CArEMP,YAAyC;EAAA,QASRjC,cAAc;AAAA;AAAA2D,EAAA,GAT/C1B,YAAyC;AA6E/C,MAAM2B,oBAAyD,GAAGA,CAAC;EACjEC,MAAM;EACNC,SAAS;EACTC,UAAU,GAAG,CAAC;EACdC;AACF,CAAC,KAAK;EACJ,IAAI,CAACH,MAAM,IAAIA,MAAM,CAACI,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAE/C,MAAMC,aAAa,GAAGL,MAAM,CAACM,KAAK,CAAC,CAAC,EAAEJ,UAAU,CAAC;EACjD,MAAMK,cAAc,GAAGP,MAAM,CAACI,MAAM,GAAGF,UAAU;EAEjD,MAAMM,aAAa,GAAGA,CAACC,KAAa,EAAEC,KAAa,KAA0B;IAC3E,MAAMC,SAA8B,GAAG;MACrCC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,OAAO;MAClBC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE,uCAAuC;MACnDC,YAAY,EAAER,KAAK,KAAK,CAAC,IAAIC,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG;IACtD,CAAC;IAED,OAAOC,SAAS;EAClB,CAAC;EAED,MAAMO,iBAAiB,GAAGA,CAACT,KAAa,EAAEC,KAAa,KAA0B;IAC/E,MAAMC,SAA8B,GAAG;MACrCQ,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBrC,eAAe,EAAE;IACnB,CAAC;IAED,IAAI2B,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QACL,GAAGC,SAAS;QACZC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,OAAO;QACfI,YAAY,EAAE;MAChB,CAAC;IACH;IAEA,IAAIP,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QACL,GAAGC,SAAS;QACZC,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,OAAO;QACfI,YAAY,EAAE;MAChB,CAAC;IACH;IAEA,IAAIP,KAAK,KAAK,CAAC,EAAE;MACf,IAAID,KAAK,KAAK,CAAC,EAAE;QACf,OAAO;UACL,GAAGE,SAAS;UACZC,KAAK,EAAE,KAAK;UACZC,MAAM,EAAE,OAAO;UACfI,YAAY,EAAE;QAChB,CAAC;MACH,CAAC,MAAM;QACL,OAAO;UACL,GAAGN,SAAS;UACZC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,OAAO;UACfI,YAAY,EAAE;QAChB,CAAC;MACH;IACF;;IAEA;IACA,IAAIR,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QACL,GAAGE,SAAS;QACZC,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,OAAO;QACfI,YAAY,EAAE;MAChB,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACL,GAAGN,SAAS;QACZC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdI,YAAY,EAAE;MAChB,CAAC;IACH;EACF,CAAC;EAED,MAAMI,aAAa,GAAGA,CAACZ,KAAa,EAAEa,KAAa,KAAK;IACtD,IAAIb,KAAK,KAAKP,UAAU,GAAG,CAAC,IAAIoB,KAAK,GAAG,CAAC,EAAE;MACzC,oBACEtF,OAAA;QAAKsC,KAAK,EAAE;UACV6C,QAAQ,EAAE,UAAU;UACpBI,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACT3C,eAAe,EAAE,oBAAoB;UACrCH,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBE,KAAK,EAAE,OAAO;UACdI,QAAQ,EAAE,QAAQ;UAClBM,UAAU,EAAE,KAAK;UACjBuB,YAAY,EAAE;QAChB,CAAE;QAAAhC,QAAA,GAAC,GACA,EAACqC,KAAK;MAAA;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAEV;IACA,OAAO,IAAI;EACb,CAAC;EAED,oBACExD,OAAA;IAAKsC,KAAK,EAAE;MACVM,OAAO,EAAE,MAAM;MACf+C,GAAG,EAAE,KAAK;MACVf,KAAK,EAAE,MAAM;MACbzB,YAAY,EAAE;IAChB,CAAE;IAAAF,QAAA,gBAEAjD,OAAA;MAAKsC,KAAK,EAAE4C,iBAAiB,CAAC,CAAC,EAAEb,aAAa,CAACD,MAAM,CAAE;MAAAnB,QAAA,gBACrDjD,OAAA,CAACoC,YAAY;QACXhC,SAAS,EAAEiE,aAAa,CAAC,CAAC,CAAE;QAC5BhC,GAAG,EAAE,GAAG4B,SAAS,YAAa;QAC9B3B,KAAK,EAAEkC,aAAa,CAAC,CAAC,EAAEH,aAAa,CAACD,MAAM,CAAE;QAC9C3B,YAAY,EAAGoB,CAAC,IAAK;UACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,aAAa;QACjD,CAAE;QACFnD,YAAY,EAAGmB,CAAC,IAAK;UACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,UAAU;QAC9C;MAAE;QAAAxC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACDW,YAAY,iBACXnE,OAAA;QACEsC,KAAK,EAAE;UACL6C,QAAQ,EAAE,UAAU;UACpBI,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTX,MAAM,EAAE;QACV,CAAE;QACFe,OAAO,EAAEA,CAAA,KAAM3B,YAAY,CAAC,CAAC;MAAE;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLa,aAAa,CAACD,MAAM,GAAG,CAAC,iBACvBpE,OAAA;MAAKsC,KAAK,EAAE;QACVM,OAAO,EAAE,MAAM;QACfmD,aAAa,EAAE,QAAQ;QACvBJ,GAAG,EAAE,KAAK;QACVf,KAAK,EAAEP,aAAa,CAACD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG;MAC9C,CAAE;MAAAnB,QAAA,EACCoB,aAAa,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC0B,GAAG,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;QAC1C,MAAMC,WAAW,GAAGD,GAAG,GAAG,CAAC;QAC3B,oBACElG,OAAA;UAEEsC,KAAK,EAAE4C,iBAAiB,CAACiB,WAAW,EAAE9B,aAAa,CAACD,MAAM,CAAE;UAAAnB,QAAA,gBAE5DjD,OAAA,CAACoC,YAAY;YACXhC,SAAS,EAAE6F,KAAM;YACjB5D,GAAG,EAAE,GAAG4B,SAAS,YAAYkC,WAAW,GAAG,CAAC,EAAG;YAC/C7D,KAAK,EAAEkC,aAAa,CAAC2B,WAAW,EAAE9B,aAAa,CAACD,MAAM,CAAE;YACxD3B,YAAY,EAAGoB,CAAC,IAAK;cACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,aAAa;YACjD,CAAE;YACFnD,YAAY,EAAGmB,CAAC,IAAK;cACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,UAAU;YAC9C;UAAE;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACD6B,aAAa,CAACc,WAAW,EAAE5B,cAAc,CAAC,EAC1CJ,YAAY,iBACXnE,OAAA;YACEsC,KAAK,EAAE;cACL6C,QAAQ,EAAE,UAAU;cACpBI,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTX,MAAM,EAAE;YACV,CAAE;YACFe,OAAO,EAAEA,CAAA,KAAM3B,YAAY,CAACgC,WAAW;UAAE;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CACF;QAAA,GA3BI2C,WAAW;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4Bb,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC4C,GAAA,GAhMIrC,oBAAyD;AAkM/D,MAAMsC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACtC,MAAMC,QAAQ,GAAGjI,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMkI,gBAAgB,GAAIC,YAAoB,IAAK;IACjD,MAAMC,MAAM,GAAG;MACb,UAAU,EAAE;QACVC,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAExH;MACR,CAAC;MACD,SAAS,EAAE;QACTuH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEvH;MACR,CAAC;MACD,QAAQ,EAAE;QACRsH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEtH;MACR,CAAC;MACD,WAAW,EAAE;QACXqH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAErH;MACR,CAAC;MACD,QAAQ,EAAE;QACRoH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEnH;MACR,CAAC;MACD,WAAW,EAAE;QACXkH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEpH;MACR;IACF,CAAC;IAED,OAAOkH,MAAM,CAACD,YAAY,CAAwB,IAAIC,MAAM,CAAC,SAAS,CAAC;EACzE,CAAC;;EAED;EACA,MAAMG,mBAAmB,GAAIC,eAAuB,IAAK;IACvD,MAAMJ,MAAM,GAAG;MACb,kBAAkB,EAAE;QAClBC,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEhH;MACR,CAAC;MACD,cAAc,EAAE;QACd+G,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEjH;MACR,CAAC;MACD,gBAAgB,EAAE;QAChBgH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE/G;MACR,CAAC;MACD,cAAc,EAAE;QACd8G,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEnH;MACR,CAAC;MACD,YAAY,EAAE;QACZkH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE9G;MACR,CAAC;MACD,SAAS,EAAE;QACT6G,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAElH;MACR;IACF,CAAC;IAED,OAAOgH,MAAM,CAACI,eAAe,CAAwB,IAAIJ,MAAM,CAAC,cAAc,CAAC;EACjF,CAAC;;EAED;EACA,MAAM,CAACK,cAAc,EAAEC,iBAAiB,CAAC,GAAG5I,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAAC6I,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9I,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAAC+I,UAAU,EAAEC,aAAa,CAAC,GAAGhJ,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAM,CAACiJ,YAAY,EAAEC,eAAe,CAAC,GAAGlJ,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAACmJ,UAAU,EAAEC,aAAa,CAAC,GAAGpJ,QAAQ,CAA4B,CAAC,CAAC,CAAC;EAC3E,MAAM,CAACqJ,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtJ,QAAQ,CAAgB,IAAI,CAAC;;EAE/E;EACA,MAAM,CAACuJ,cAAc,EAAEC,iBAAiB,CAAC,GAAGxJ,QAAQ,CAAkB,EAAE,CAAC;EACzE,MAAM,CAACyJ,eAAe,EAAEC,kBAAkB,CAAC,GAAG1J,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC2J,aAAa,EAAEC,gBAAgB,CAAC,GAAG5J,QAAQ,CAAqB,CAAC;EACxE,MAAM,CAAC6J,aAAa,EAAEC,gBAAgB,CAAC,GAAG9J,QAAQ,CAAQ,EAAE,CAAC;EAC7D,MAAM,CAACoC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsC,KAAK,EAAEC,QAAQ,CAAC,GAAGvC,QAAQ,CAAqB,CAAC;EACxD,MAAM,CAAC+J,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGhK,QAAQ,CAAQ,EAAE,CAAC;EAEzE,MAAM;IAAEiK;EAAW,CAAC,GAAG9J,aAAa,CAAC,CAAC;;EAEtC;EACA,MAAM+J,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAEpE,MAAM,EAAE;EAAE,CAAC,EAAE,CAACqE,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;;EAEjE;EACA,MAAMC,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,IAAI;MACFlI,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAACiI,SAAS,CAAC;MAEnB,MAAM3H,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGvC,YAAY,wFAAwF,CAAC;MACrI,MAAMkK,IAAI,GAAG,MAAM5H,QAAQ,CAAC6H,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC7B,MAAMG,iBAAiB,GAAGH,IAAI,CAACA,IAAI,CAACZ,aAAa,IAAI,EAAE;;QAEvD;QACA,MAAMgB,uBAAuB,GAAG,MAAMC,OAAO,CAACC,GAAG,CAC/CH,iBAAiB,CAAChD,GAAG,CAAC,MAAOoD,YAAiB,IAAK;UACjD,IAAI;YACF,MAAMC,aAAa,GAAG,MAAMnI,KAAK,CAAC,GAAGvC,YAAY,sBAAsByK,YAAY,CAACE,eAAe,SAAS,CAAC;YAC7G,MAAMC,SAAS,GAAG,MAAMF,aAAa,CAACP,IAAI,CAAC,CAAC;YAE5C,IAAIS,SAAS,CAACR,OAAO,IAAIQ,SAAS,CAACV,IAAI,EAAE;cACvCO,YAAY,CAACpF,MAAM,GAAGuF,SAAS,CAACV,IAAI,CAAC7E,MAAM,IAAI,EAAE;YACnD,CAAC,MAAM;cACLoF,YAAY,CAACpF,MAAM,GAAG,EAAE;YAC1B;UACF,CAAC,CAAC,OAAOwF,MAAM,EAAE;YACfzI,OAAO,CAAC0I,IAAI,CAAC,2CAA2CL,YAAY,CAACE,eAAe,GAAG,EAAEE,MAAM,CAAC;YAChGJ,YAAY,CAACpF,MAAM,GAAG,EAAE;UAC1B;UACA,OAAOoF,YAAY;QACrB,CAAC,CACH,CAAC;QAEDlB,gBAAgB,CAACe,uBAAuB,CAAC;;QAEzC;QACA,MAAMS,MAAM,GAAGT,uBAAuB,CAACU,MAAM,CAAEC,GAAQ,IAAKA,GAAG,CAACC,SAAS,KAAK,CAAC,CAAC;QAChFzB,sBAAsB,CAACsB,MAAM,CAAC;MAChC,CAAC,MAAM;QACL/I,QAAQ,CAAC,8BAA8B,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOqB,GAAQ,EAAE;MACjBjB,OAAO,CAACL,KAAK,CAAC,+BAA+B,EAAEsB,GAAG,CAAC;MACnDrB,QAAQ,CAACqB,GAAG,CAACC,OAAO,IAAI,8BAA8B,CAAC;IACzD,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMqJ,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFhC,kBAAkB,CAAC,IAAI,CAAC;MACxBE,gBAAgB,CAACY,SAAS,CAAC;MAE3B,MAAM3H,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGvC,YAAY,0DAA0D,CAAC;MACvG,MAAMkK,IAAI,GAAG,MAAM5H,QAAQ,CAAC6H,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC7B,MAAMkB,UAAU,GAAGlB,IAAI,CAACA,IAAI,CAACmB,MAAM,IAAInB,IAAI,CAACA,IAAI,IAAI,EAAE;;QAEtD;QACA,MAAMoB,gBAAgB,GAAG,MAAMf,OAAO,CAACC,GAAG,CACxCY,UAAU,CAAC/D,GAAG,CAAC,MAAOkE,KAAU,IAAK;UACnC,IAAI;YACF,MAAMb,aAAa,GAAG,MAAMnI,KAAK,CAAC,GAAGvC,YAAY,iBAAiBuL,KAAK,CAACC,WAAW,SAAS,CAAC;YAC7F,MAAMZ,SAAS,GAAG,MAAMF,aAAa,CAACP,IAAI,CAAC,CAAC;YAE5C,IAAIS,SAAS,CAACR,OAAO,IAAIQ,SAAS,CAACV,IAAI,EAAE;cACvCqB,KAAK,CAAClG,MAAM,GAAGuF,SAAS,CAACV,IAAI,CAACuB,WAAW,IAAI,EAAE;YACjD,CAAC,MAAM;cACLF,KAAK,CAAClG,MAAM,GAAG,EAAE;YACnB;UACF,CAAC,CAAC,OAAOwF,MAAM,EAAE;YACfzI,OAAO,CAAC0I,IAAI,CAAC,oCAAoCS,KAAK,CAACC,WAAW,GAAG,EAAEX,MAAM,CAAC;YAC9EU,KAAK,CAAClG,MAAM,GAAG,EAAE;UACnB;UACA,OAAOkG,KAAK;QACd,CAAC,CACH,CAAC;QAEDtC,iBAAiB,CAACqC,gBAAgB,CAAC;MACrC,CAAC,MAAM;QACLjC,gBAAgB,CAAC,gCAAgC,CAAC;MACpD;IACF,CAAC,CAAC,OAAOhG,GAAQ,EAAE;MACjBjB,OAAO,CAACL,KAAK,CAAC,iCAAiC,EAAEsB,GAAG,CAAC;MACrDgG,gBAAgB,CAAChG,GAAG,CAACC,OAAO,IAAI,gCAAgC,CAAC;IACnE,CAAC,SAAS;MACR6F,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACAzJ,SAAS,CAAC,MAAM;IACdsK,2BAA2B,CAAC,CAAC;IAC7BmB,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMO,gBAAgB,GAAG,MAAOjB,YAAiB,IAAK;IACpD,IAAI;MACF,IAAIA,YAAY,CAACkB,aAAa,EAAE;QAC9B,MAAM9L,mBAAmB,CAAC+L,cAAc,CAACnB,YAAY,CAACE,eAAe,CAAC;MACxE,CAAC,MAAM;QACL,MAAMrI,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGvC,YAAY,sBAAsByK,YAAY,CAACE,eAAe,YAAY,EAAE;UAC1GnI,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDoJ,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBC,gBAAgB,EAAE,CAAC;YACnBC,YAAY,EAAE;UAChB,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,CAAC3J,QAAQ,CAACQ,EAAE,EAAE;UAChB,MAAM,IAAIX,KAAK,CAAC,wBAAwB,CAAC;QAC3C;MACF;;MAEA;MACA,MAAM6H,2BAA2B,CAAC,CAAC;IACrC,CAAC,CAAC,OAAOjI,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,MAAMmK,mBAAmB,GAAG,MAAAA,CAAOC,cAAsB,EAAEC,WAAmB,KAAK;IACjF,IAAI,CAACA,WAAW,CAACC,IAAI,CAAC,CAAC,EAAE;IAEzB,IAAI;MACFtD,oBAAoB,CAACoD,cAAc,CAAC;MAEpC,MAAM7J,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGvC,YAAY,sBAAsBmM,cAAc,WAAW,EAAE;QAC3F3J,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDoJ,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBO,OAAO,EAAEF,WAAW;UACpBG,cAAc,EAAE,cAAc;UAC9BC,eAAe,EAAE,qBAAqB;UACtCP,YAAY,EAAE;QAChB,CAAC;MACH,CAAC,CAAC;MAEF,IAAI3J,QAAQ,CAACQ,EAAE,EAAE;QACf+F,aAAa,CAAC4D,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACN,cAAc,GAAG;QAAG,CAAC,CAAC,CAAC;QAC1D,MAAMnC,2BAA2B,CAAC,CAAC;MACrC,CAAC,MAAM;QACL5H,OAAO,CAACL,KAAK,CAAC,0BAA0B,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,SAAS;MACRgH,oBAAoB,CAAC,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAM2D,qBAAqB,GAAGpD,aAAa,CAAC0B,MAAM,CAACP,YAAY,IAAI;IACjE,MAAMkC,aAAa,GAAG,CAACnE,UAAU,IAC/BiC,YAAY,CAACmC,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtE,UAAU,CAACqE,WAAW,CAAC,CAAC,CAAC,IACnEpC,YAAY,CAAC6B,OAAO,CAACO,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtE,UAAU,CAACqE,WAAW,CAAC,CAAC,CAAC;IAEvE,MAAME,eAAe,GAAG,CAAC3E,cAAc,IAAIqC,YAAY,CAACuC,WAAW,CAACC,QAAQ,CAAC,CAAC,KAAK7E,cAAc;;IAEjG;IACA;;IAEA,OAAOuE,aAAa,IAAII,eAAe;EACzC,CAAC,CAAC;;EAEF;EACA,MAAMG,sBAAsB,GAAGlE,cAAc,CAACgC,MAAM,CAACO,KAAK,IAAI;IAC5D,MAAMoB,aAAa,GAAG,CAACnE,UAAU,IAC/B+C,KAAK,CAACqB,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtE,UAAU,CAACqE,WAAW,CAAC,CAAC,CAAC,IAC3DtB,KAAK,CAAC4B,WAAW,IAAI5B,KAAK,CAAC4B,WAAW,CAACN,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACtE,UAAU,CAACqE,WAAW,CAAC,CAAC,CAAE;;IAE3F;IACA,MAAMO,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAE5B,MAAMC,SAAS,GAAG,IAAIF,IAAI,CAAC9B,KAAK,CAACiC,UAAU,CAAC;IAC5CD,SAAS,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhC,MAAMG,gBAAgB,GAAGF,SAAS,IAAIH,KAAK;IAC3C,MAAMM,WAAW,GAAInC,KAAK,CAASoC,YAAY,KAAK,CAAC;IAErD,OAAOhB,aAAa,IAAIc,gBAAgB,IAAIC,WAAW;EACzD,CAAC,CAAC;;EAEF;EACA,MAAME,oBAAoB,GAAGlB,qBAAqB;EAClD,MAAMmB,aAAa,GAAGX,sBAAsB;;EAE5C;EACA,MAAMY,eAAe,GAAG,CACtB,GAAGF,oBAAoB,CAACvG,GAAG,CAAC0G,IAAI,KAAK;IAAE,GAAGA,IAAI;IAAEC,IAAI,EAAE,cAAc;IAAEC,QAAQ,EAAE,IAAIZ,IAAI,CAACU,IAAI,CAACG,UAAU;EAAE,CAAC,CAAC,CAAC,EAC7G,GAAGL,aAAa,CAACxG,GAAG,CAAC0G,IAAI,KAAK;IAAE,GAAGA,IAAI;IAAEC,IAAI,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAIZ,IAAI,CAACU,IAAI,CAACP,UAAU;EAAE,CAAC,CAAC,CAAC,CAChG,CAACW,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACJ,QAAQ,CAACK,OAAO,CAAC,CAAC,GAAGF,CAAC,CAACH,QAAQ,CAACK,OAAO,CAAC,CAAC,CAAC;EAE7D,oBACEjN,OAAA,CAAAE,SAAA;IAAA+C,QAAA,gBAEEjD,OAAA;MAAAiD,QAAA,EACG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAS;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAERxD,OAAA;MAAKsC,KAAK,EAAE;QACVM,OAAO,EAAE,MAAM;QACfsK,SAAS,EAAE,OAAO;QAClBvG,UAAU,EAAE,mDAAmD;QAC/DwG,cAAc,EAAE;MAClB,CAAE;MAAAlK,QAAA,eAGFjD,OAAA;QACEuC,SAAS,EAAC,aAAa;QACvBD,KAAK,EAAE;UACL8K,IAAI,EAAE,CAAC;UACPxK,OAAO,EAAE,MAAM;UACfmD,aAAa,EAAE,QAAQ;UACvBmH,SAAS,EAAE,OAAO;UAClBtI,KAAK,EAAE;QACT,CAAE;QAAA3B,QAAA,gBAEFjD,OAAA;UAAQsC,KAAK,EAAE;YACbqE,UAAU,EAAE,OAAO;YACnB0G,YAAY,EAAE,mBAAmB;YACjClI,QAAQ,EAAE,QAAQ;YAClBI,GAAG,EAAE,CAAC;YACN+H,MAAM,EAAE,GAAG;YACXC,SAAS,EAAE;UACb,CAAE;UAAAtK,QAAA,eACAjD,OAAA;YAAKsC,KAAK,EAAE;cACVsC,KAAK,EAAE,MAAM;cACb4I,OAAO,EAAE,QAAQ;cACjB3I,MAAM,EAAE,MAAM;cACdjC,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,gBAEAjD,OAAA;cAAKsC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB8C,GAAG,EAAE,QAAQ;gBACb8H,QAAQ,EAAE;cACZ,CAAE;cAAAxK,QAAA,gBACAjD,OAAA;gBACE4D,GAAG,EAAC,iBAAiB;gBACrBvB,GAAG,EAAC,WAAW;gBACfC,KAAK,EAAE;kBACLsC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdC,SAAS,EAAE;gBACb;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFxD,OAAA;gBAAAiD,QAAA,gBACEjD,OAAA;kBAAIsC,KAAK,EAAE;oBACToL,MAAM,EAAE,CAAC;oBACTtK,QAAQ,EAAE,QAAQ;oBAClBM,UAAU,EAAE,KAAK;oBACjBV,KAAK,EAAE,SAAS;oBAChB2K,UAAU,EAAE;kBACd,CAAE;kBAAA1K,QAAA,EAAC;gBAEH;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLxD,OAAA;kBAAGsC,KAAK,EAAE;oBACRoL,MAAM,EAAE,CAAC;oBACTtK,QAAQ,EAAE,UAAU;oBACpBJ,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,EAAC;gBAEH;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNxD,OAAA;cAAKsC,KAAK,EAAE;gBACV8K,IAAI,EAAE,CAAC;gBACPQ,QAAQ,EAAE,OAAO;gBACjBF,MAAM,EAAE;cACV,CAAE;cAAAzK,QAAA,eACAjD,OAAA;gBAAKsC,KAAK,EAAE;kBAAE6C,QAAQ,EAAE;gBAAW,CAAE;gBAAAlC,QAAA,gBACnCjD,OAAA,CAACnB,MAAM;kBACLgP,IAAI,EAAE,EAAG;kBACTvL,KAAK,EAAE;oBACL6C,QAAQ,EAAE,UAAU;oBACpBK,IAAI,EAAE,MAAM;oBACZD,GAAG,EAAE,KAAK;oBACVM,SAAS,EAAE,kBAAkB;oBAC7B7C,KAAK,EAAE;kBACT;gBAAE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFxD,OAAA;kBACE2M,IAAI,EAAC,MAAM;kBACXmB,WAAW,EAAC,oCAAoC;kBAChDC,KAAK,EAAE5G,UAAW;kBAClB6G,QAAQ,EAAGnK,CAAC,IAAKuD,aAAa,CAACvD,CAAC,CAACoK,MAAM,CAACF,KAAK,CAAE;kBAC/CzL,KAAK,EAAE;oBACLsC,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,MAAM;oBACd2I,OAAO,EAAE,eAAe;oBACxB/J,MAAM,EAAE,mBAAmB;oBAC3BwB,YAAY,EAAE,MAAM;oBACpB0B,UAAU,EAAE,SAAS;oBACrB3D,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,UAAU;oBACpB8K,OAAO,EAAE,MAAM;oBACflJ,UAAU,EAAE;kBACd,CAAE;kBACFmJ,OAAO,EAAGtK,CAAC,IAAK;oBACdA,CAAC,CAACoK,MAAM,CAAC3L,KAAK,CAAC8L,WAAW,GAAG,SAAS;oBACtCvK,CAAC,CAACoK,MAAM,CAAC3L,KAAK,CAACqE,UAAU,GAAG,OAAO;oBACnC9C,CAAC,CAACoK,MAAM,CAAC3L,KAAK,CAACiL,SAAS,GAAG,mCAAmC;kBAChE,CAAE;kBACFc,MAAM,EAAGxK,CAAC,IAAK;oBACbA,CAAC,CAACoK,MAAM,CAAC3L,KAAK,CAAC8L,WAAW,GAAG,SAAS;oBACtCvK,CAAC,CAACoK,MAAM,CAAC3L,KAAK,CAACqE,UAAU,GAAG,SAAS;oBACrC9C,CAAC,CAACoK,MAAM,CAAC3L,KAAK,CAACiL,SAAS,GAAG,MAAM;kBACnC;gBAAE;kBAAAlK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNxD,OAAA;cAAKsC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB8C,GAAG,EAAE,MAAM;gBACX8H,QAAQ,EAAE,OAAO;gBACjB3K,cAAc,EAAE;cAClB,CAAE;cAAAG,QAAA,gBAEAjD,OAAA;gBAAKsC,KAAK,EAAE;kBACVM,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpB8C,GAAG,EAAE,QAAQ;kBACb6H,OAAO,EAAE,QAAQ;kBACjB7G,UAAU,EAAE,SAAS;kBACrB1B,YAAY,EAAE,MAAM;kBACpBxB,MAAM,EAAE;gBACV,CAAE;gBAAAR,QAAA,gBACAjD,OAAA;kBACE+N,KAAK,EAAEhH,cAAe;kBACtBiH,QAAQ,EAAGnK,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,CAACoK,MAAM,CAACF,KAAK,CAAE;kBACnDzL,KAAK,EAAE;oBACLkL,OAAO,EAAE,gBAAgB;oBACzB/J,MAAM,EAAE,MAAM;oBACdwB,YAAY,EAAE,KAAK;oBACnB0B,UAAU,EAAE,OAAO;oBACnB3D,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,UAAU;oBACpB8K,OAAO,EAAE,MAAM;oBACfnJ,MAAM,EAAE,SAAS;oBACjB0I,QAAQ,EAAE;kBACZ,CAAE;kBAAAxK,QAAA,gBAEFjD,OAAA;oBAAQ+N,KAAK,EAAC,EAAE;oBAAA9K,QAAA,EAAC;kBAAc;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACvC6E,UAAU,CAACrC,GAAG,CAACsI,QAAQ,iBACtBtO,OAAA;oBAAmC+N,KAAK,EAAEO,QAAQ,CAAC3C,WAAW,CAACC,QAAQ,CAAC,CAAE;oBAAA3I,QAAA,EACvEqL,QAAQ,CAACC;kBAAI,GADHD,QAAQ,CAAC3C,WAAW;oBAAAtI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEzB,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAETxD,OAAA;kBACE+N,KAAK,EAAE9G,gBAAiB;kBACxB+G,QAAQ,EAAGnK,CAAC,IAAKqD,mBAAmB,CAACrD,CAAC,CAACoK,MAAM,CAACF,KAAK,CAAE;kBACrDzL,KAAK,EAAE;oBACLkL,OAAO,EAAE,gBAAgB;oBACzB/J,MAAM,EAAE,MAAM;oBACdwB,YAAY,EAAE,KAAK;oBACnB0B,UAAU,EAAE,OAAO;oBACnB3D,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,UAAU;oBACpB8K,OAAO,EAAE,MAAM;oBACfnJ,MAAM,EAAE,SAAS;oBACjB0I,QAAQ,EAAE;kBACZ,CAAE;kBAAAxK,QAAA,gBAEFjD,OAAA;oBAAQ+N,KAAK,EAAC,EAAE;oBAAA9K,QAAA,EAAC;kBAAU;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACnC8E,WAAW,CAACtC,GAAG,CAACwI,KAAK,iBACpBxO,OAAA;oBAAoB+N,KAAK,EAAES,KAAK,CAAC5C,QAAQ,CAAC,CAAE;oBAAA3I,QAAA,GAAC,QACrC,EAACuL,KAAK;kBAAA,GADDA,KAAK;oBAAAnL,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEV,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EAER,CAAC2D,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,kBAChDjH,OAAA;kBACE8F,OAAO,EAAEA,CAAA,KAAM;oBACbsB,aAAa,CAAC,EAAE,CAAC;oBACjBJ,iBAAiB,CAAC,EAAE,CAAC;oBACrBE,mBAAmB,CAAC,EAAE,CAAC;kBACzB,CAAE;kBACF5E,KAAK,EAAE;oBACLkL,OAAO,EAAE,gBAAgB;oBACzB/J,MAAM,EAAE,MAAM;oBACdwB,YAAY,EAAE,KAAK;oBACnB0B,UAAU,EAAE,SAAS;oBACrB3D,KAAK,EAAE,OAAO;oBACdI,QAAQ,EAAE,UAAU;oBACpBM,UAAU,EAAE,KAAK;oBACjBqB,MAAM,EAAE,SAAS;oBACjBC,UAAU,EAAE;kBACd,CAAE;kBACFvC,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACqE,UAAU,GAAG,SAAS;kBAC9C,CAAE;kBACFjE,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACqE,UAAU,GAAG,SAAS;kBAC9C,CAAE;kBAAA1D,QAAA,EACH;gBAED;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAKNxD,OAAA;gBACE8F,OAAO,EAAEA,CAAA,KAAMS,QAAQ,CAAC,oBAAoB,CAAE;gBAC9CjE,KAAK,EAAE;kBACLM,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpB8C,GAAG,EAAE,QAAQ;kBACb6H,OAAO,EAAE,aAAa;kBACtB/J,MAAM,EAAE,mBAAmB;kBAC3BwB,YAAY,EAAE,KAAK;kBACnB0B,UAAU,EAAE,OAAO;kBACnB3D,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,UAAU;kBACpBM,UAAU,EAAE,KAAK;kBACjBqB,MAAM,EAAE,SAAS;kBACjBC,UAAU,EAAE;gBACd,CAAE;gBACFvC,YAAY,EAAGoB,CAAC,IAAK;kBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAAC8L,WAAW,GAAG,SAAS;kBAC7CvK,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;gBACzC,CAAE;gBACFN,YAAY,EAAGmB,CAAC,IAAK;kBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAAC8L,WAAW,GAAG,SAAS;kBAC7CvK,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;gBACzC,CAAE;gBAAAC,QAAA,gBAEFjD,OAAA,CAACpB,IAAI;kBAACiP,IAAI,EAAE;gBAAG;kBAAAxK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,aAEpB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGTxD,OAAA;UAAKsC,KAAK,EAAE;YACV8K,IAAI,EAAE,CAAC;YACPxK,OAAO,EAAE,MAAM;YACf+C,GAAG,EAAE,QAAQ;YACb6H,OAAO,EAAE,aAAa;YACtB7G,UAAU,EAAE,aAAa;YACzB9D,UAAU,EAAE;UACd,CAAE;UAAAI,QAAA,gBAEAjD,OAAA;YAAKsC,KAAK,EAAE;cACVsC,KAAK,EAAE,OAAO;cACd6J,UAAU,EAAE;YACd,CAAE;YAAAxL,QAAA,eACAjD,OAAA;cAAKsC,KAAK,EAAE;gBACVqE,UAAU,EAAE,OAAO;gBACnB1B,YAAY,EAAE,MAAM;gBACpBxB,MAAM,EAAE,mBAAmB;gBAC3B2B,QAAQ,EAAE,QAAQ;gBAClBD,QAAQ,EAAE,QAAQ;gBAClBI,GAAG,EAAE,MAAM;gBACXgI,SAAS,EAAE;cACb,CAAE;cAAAtK,QAAA,gBAEAjD,OAAA;gBAAKsC,KAAK,EAAE;kBACVkL,OAAO,EAAE,sBAAsB;kBAC/BH,YAAY,EAAE,mBAAmB;kBACjC1G,UAAU,EAAE;gBACd,CAAE;gBAAA1D,QAAA,gBACAjD,OAAA;kBAAKsC,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpB8C,GAAG,EAAE,SAAS;oBACdxC,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,gBACAjD,OAAA,CAAClB,GAAG;oBAAC+O,IAAI,EAAE,EAAG;oBAACvL,KAAK,EAAE;sBAAEU,KAAK,EAAE;oBAAQ;kBAAE;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5CxD,OAAA;oBAAIsC,KAAK,EAAE;sBACToL,MAAM,EAAE,CAAC;sBACTtK,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE,KAAK;sBACjBV,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,EAAC;kBAEH;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACNxD,OAAA;kBAAGsC,KAAK,EAAE;oBACRoL,MAAM,EAAE,CAAC;oBACTtK,QAAQ,EAAE,UAAU;oBACpBJ,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,EAAC;gBAEH;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGNxD,OAAA;gBAAKsC,KAAK,EAAE;kBAAEkL,OAAO,EAAE;gBAAU,CAAE;gBAAAvK,QAAA,gBAEjCjD,OAAA;kBAAKsC,KAAK,EAAE;oBACVkL,OAAO,EAAE,SAAS;oBAClB7G,UAAU,EAAE,mDAAmD;oBAC/D1B,YAAY,EAAE,MAAM;oBACpBxB,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,SAAS;oBACvB4B,MAAM,EAAE,SAAS;oBACjBC,UAAU,EAAE;kBACd,CAAE;kBACFvC,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,kBAAkB;oBACpDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACiL,SAAS,GAAG,oCAAoC;kBACxE,CAAE;kBACF7K,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,eAAe;oBACjDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACiL,SAAS,GAAG,MAAM;kBAC1C,CAAE;kBAAAtK,QAAA,eACAjD,OAAA;oBAAKsC,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,YAAY;sBACxB8C,GAAG,EAAE;oBACP,CAAE;oBAAA1C,QAAA,gBACAjD,OAAA;sBAAKsC,KAAK,EAAE;wBACVsC,KAAK,EAAE,MAAM;wBACbC,MAAM,EAAE,MAAM;wBACd8B,UAAU,EAAE,SAAS;wBACrB1B,YAAY,EAAE,KAAK;wBACnBtB,SAAS,EAAE,QAAQ;wBACnB8K,UAAU,EAAE,CAAC;wBACblB,SAAS,EAAE;sBACb;oBAAE;sBAAAlK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACLxD,OAAA;sBAAKsC,KAAK,EAAE;wBAAE8K,IAAI,EAAE;sBAAE,CAAE;sBAAAnK,QAAA,gBACtBjD,OAAA;wBAAIsC,KAAK,EAAE;0BACToL,MAAM,EAAE,cAAc;0BACtBtK,QAAQ,EAAE,UAAU;0BACpBM,UAAU,EAAE,KAAK;0BACjBV,KAAK,EAAE,SAAS;0BAChB2K,UAAU,EAAE;wBACd,CAAE;wBAAA1K,QAAA,EAAC;sBAEH;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLxD,OAAA;wBAAGsC,KAAK,EAAE;0BACRoL,MAAM,EAAE,cAAc;0BACtBtK,QAAQ,EAAE,QAAQ;0BAClBJ,KAAK,EAAE,SAAS;0BAChB2K,UAAU,EAAE;wBACd,CAAE;wBAAA1K,QAAA,EAAC;sBAEH;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACJxD,OAAA;wBAAKsC,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE,QAAQ;0BACbvC,QAAQ,EAAE,SAAS;0BACnBJ,KAAK,EAAE;wBACT,CAAE;wBAAAC,QAAA,gBACAjD,OAAA,CAACjB,QAAQ;0BAAC8O,IAAI,EAAE;wBAAG;0BAAAxK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACtBxD,OAAA;0BAAAiD,QAAA,EAAM;wBAAU;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNxD,OAAA;kBAAKsC,KAAK,EAAE;oBACVkL,OAAO,EAAE,SAAS;oBAClB7G,UAAU,EAAE,mDAAmD;oBAC/D1B,YAAY,EAAE,MAAM;oBACpBxB,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,SAAS;oBACvB4B,MAAM,EAAE,SAAS;oBACjBC,UAAU,EAAE;kBACd,CAAE;kBACFvC,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,kBAAkB;oBACpDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACiL,SAAS,GAAG,oCAAoC;kBACxE,CAAE;kBACF7K,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,eAAe;oBACjDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACiL,SAAS,GAAG,MAAM;kBAC1C,CAAE;kBAAAtK,QAAA,eACAjD,OAAA;oBAAKsC,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,YAAY;sBACxB8C,GAAG,EAAE;oBACP,CAAE;oBAAA1C,QAAA,gBACAjD,OAAA;sBAAKsC,KAAK,EAAE;wBACVsC,KAAK,EAAE,MAAM;wBACbC,MAAM,EAAE,MAAM;wBACd8B,UAAU,EAAE,SAAS;wBACrB1B,YAAY,EAAE,KAAK;wBACnBtB,SAAS,EAAE,QAAQ;wBACnB8K,UAAU,EAAE,CAAC;wBACblB,SAAS,EAAE;sBACb;oBAAE;sBAAAlK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACLxD,OAAA;sBAAKsC,KAAK,EAAE;wBAAE8K,IAAI,EAAE;sBAAE,CAAE;sBAAAnK,QAAA,gBACtBjD,OAAA;wBAAIsC,KAAK,EAAE;0BACToL,MAAM,EAAE,cAAc;0BACtBtK,QAAQ,EAAE,UAAU;0BACpBM,UAAU,EAAE,KAAK;0BACjBV,KAAK,EAAE,SAAS;0BAChB2K,UAAU,EAAE;wBACd,CAAE;wBAAA1K,QAAA,EAAC;sBAEH;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLxD,OAAA;wBAAGsC,KAAK,EAAE;0BACRoL,MAAM,EAAE,cAAc;0BACtBtK,QAAQ,EAAE,QAAQ;0BAClBJ,KAAK,EAAE,SAAS;0BAChB2K,UAAU,EAAE;wBACd,CAAE;wBAAA1K,QAAA,EAAC;sBAEH;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACJxD,OAAA;wBAAKsC,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE,QAAQ;0BACbvC,QAAQ,EAAE,SAAS;0BACnBJ,KAAK,EAAE;wBACT,CAAE;wBAAAC,QAAA,gBACAjD,OAAA,CAACjB,QAAQ;0BAAC8O,IAAI,EAAE;wBAAG;0BAAAxK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACtBxD,OAAA;0BAAAiD,QAAA,EAAM;wBAAU;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACpB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNxD,OAAA;kBAAKsC,KAAK,EAAE;oBACVkL,OAAO,EAAE,SAAS;oBAClB7G,UAAU,EAAE,mDAAmD;oBAC/D1B,YAAY,EAAE,MAAM;oBACpBxB,MAAM,EAAE,mBAAmB;oBAC3BN,YAAY,EAAE,SAAS;oBACvB4B,MAAM,EAAE,SAAS;oBACjBC,UAAU,EAAE;kBACd,CAAE;kBACFvC,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,kBAAkB;oBACpDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACiL,SAAS,GAAG,mCAAmC;kBACvE,CAAE;kBACF7K,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,eAAe;oBACjDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACiL,SAAS,GAAG,MAAM;kBAC1C,CAAE;kBAAAtK,QAAA,eACAjD,OAAA;oBAAKsC,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,YAAY;sBACxB8C,GAAG,EAAE;oBACP,CAAE;oBAAA1C,QAAA,gBACAjD,OAAA;sBAAKsC,KAAK,EAAE;wBACVsC,KAAK,EAAE,MAAM;wBACbC,MAAM,EAAE,MAAM;wBACd8B,UAAU,EAAE,SAAS;wBACrB1B,YAAY,EAAE,KAAK;wBACnBtB,SAAS,EAAE,QAAQ;wBACnB8K,UAAU,EAAE,CAAC;wBACblB,SAAS,EAAE;sBACb;oBAAE;sBAAAlK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACLxD,OAAA;sBAAKsC,KAAK,EAAE;wBAAE8K,IAAI,EAAE;sBAAE,CAAE;sBAAAnK,QAAA,gBACtBjD,OAAA;wBAAIsC,KAAK,EAAE;0BACToL,MAAM,EAAE,cAAc;0BACtBtK,QAAQ,EAAE,UAAU;0BACpBM,UAAU,EAAE,KAAK;0BACjBV,KAAK,EAAE,SAAS;0BAChB2K,UAAU,EAAE;wBACd,CAAE;wBAAA1K,QAAA,EAAC;sBAEH;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLxD,OAAA;wBAAGsC,KAAK,EAAE;0BACRoL,MAAM,EAAE,cAAc;0BACtBtK,QAAQ,EAAE,QAAQ;0BAClBJ,KAAK,EAAE,SAAS;0BAChB2K,UAAU,EAAE;wBACd,CAAE;wBAAA1K,QAAA,EAAC;sBAEH;wBAAAI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC,eACJxD,OAAA;wBAAKsC,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE,QAAQ;0BACbvC,QAAQ,EAAE,SAAS;0BACnBJ,KAAK,EAAE;wBACT,CAAE;wBAAAC,QAAA,gBACAjD,OAAA,CAACjB,QAAQ;0BAAC8O,IAAI,EAAE;wBAAG;0BAAAxK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACtBxD,OAAA;0BAAAiD,QAAA,EAAM;wBAAW;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNxD,OAAA;kBAAQsC,KAAK,EAAE;oBACbsC,KAAK,EAAE,MAAM;oBACb4I,OAAO,EAAE,SAAS;oBAClB/J,MAAM,EAAE,mBAAmB;oBAC3BwB,YAAY,EAAE,MAAM;oBACpB0B,UAAU,EAAE,mDAAmD;oBAC/D3D,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,UAAU;oBACpBM,UAAU,EAAE,KAAK;oBACjBqB,MAAM,EAAE,SAAS;oBACjBC,UAAU,EAAE;kBACd,CAAE;kBACFvC,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACqE,UAAU,GAAG,mDAAmD;oBACtF9C,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAAC8L,WAAW,GAAG,SAAS;oBAC7CvK,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,kBAAkB;kBACtD,CAAE;kBACFnD,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACqE,UAAU,GAAG,mDAAmD;oBACtF9C,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAAC8L,WAAW,GAAG,SAAS;oBAC7CvK,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,eAAe;kBACnD,CAAE;kBAAA5C,QAAA,EAAC;gBAEH;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACN,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNxD,OAAA;YAAKsC,KAAK,EAAE;cAAE8K,IAAI,EAAE,CAAC;cAAEK,QAAQ,EAAE;YAAE,CAAE;YAAAxK,QAAA,GAEpC,CAACzC,OAAO,IAAIqH,eAAe,kBAC1B7H,OAAA;cAAKsC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfE,cAAc,EAAE,QAAQ;gBACxBD,UAAU,EAAE,QAAQ;gBACpB2K,OAAO,EAAE;cACX,CAAE;cAAAvK,QAAA,eACAjD,OAAA;gBAAKsC,KAAK,EAAE;kBACVsC,KAAK,EAAE,QAAQ;kBACfC,MAAM,EAAE,QAAQ;kBAChBpB,MAAM,EAAE,mBAAmB;kBAC3BiL,SAAS,EAAE,mBAAmB;kBAC9BzJ,YAAY,EAAE,KAAK;kBACnB0J,SAAS,EAAE;gBACb;cAAE;gBAAAtL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,EAGA,CAAC9C,KAAK,IAAIqH,aAAa,kBACtB/H,OAAA;cAAKsC,KAAK,EAAE;gBACVqE,UAAU,EAAE,wBAAwB;gBACpClD,MAAM,EAAE,kCAAkC;gBAC1CwB,YAAY,EAAE,MAAM;gBACpBuI,OAAO,EAAE,MAAM;gBACfrK,YAAY,EAAE,QAAQ;gBACtBH,KAAK,EAAE;cACT,CAAE;cAAAC,QAAA,GACCvC,KAAK,iBAAIV,OAAA;gBAAAiD,QAAA,GAAK,iBAAe,EAACvC,KAAK;cAAA;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC1CuE,aAAa,iBAAI/H,OAAA;gBAAAiD,QAAA,GAAK,YAAU,EAAC8E,aAAa;cAAA;gBAAA1E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CACN,EAGA,CAAChD,OAAO,IAAI,CAACqH,eAAe,IAAI0E,oBAAoB,CAACnI,MAAM,KAAK,CAAC,IAAIoI,aAAa,CAACpI,MAAM,KAAK,CAAC,iBAC9FpE,OAAA;cAAKsC,KAAK,EAAE;gBACVqE,UAAU,EAAE,0BAA0B;gBACtC1B,YAAY,EAAE,MAAM;gBACpBuI,OAAO,EAAE,MAAM;gBACftK,SAAS,EAAE,QAAQ;gBACnBO,MAAM,EAAE,8BAA8B;gBACtCmL,cAAc,EAAE;cAClB,CAAE;cAAA3L,QAAA,gBACAjD,OAAA;gBAAKsC,KAAK,EAAE;kBACVa,YAAY,EAAE;gBAChB,CAAE;gBAAAF,QAAA,eACAjD,OAAA,CAACd,MAAM;kBAAC2O,IAAI,EAAE,EAAG;kBAAC7K,KAAK,EAAC;gBAAS;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACNxD,OAAA;gBAAIsC,KAAK,EAAE;kBACTU,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,SAAS;kBACnBM,UAAU,EAAE,KAAK;kBACjBgK,MAAM,EAAE;gBACV,CAAE;gBAAAzK,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxD,OAAA;gBAAGsC,KAAK,EAAE;kBACRU,KAAK,EAAE,SAAS;kBAChB0K,MAAM,EAAE;gBACV,CAAE;gBAAAzK,QAAA,EACCkE,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,GAC7C,iDAAiD,GACjD;cAAmC;gBAAA5D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACN,EAGA,CAAChD,OAAO,IAAI,CAACqH,eAAe,KAAK0E,oBAAoB,CAACnI,MAAM,GAAG,CAAC,IAAIoI,aAAa,CAACpI,MAAM,GAAG,CAAC,CAAC,iBAC5FpE,OAAA;cAAKsC,KAAK,EAAE;gBACVsL,QAAQ,EAAE,QAAQ;gBAClBF,MAAM,EAAE,QAAQ;gBAChB9K,OAAO,EAAE,MAAM;gBACfmD,aAAa,EAAE,QAAQ;gBACvBJ,GAAG,EAAE;cACP,CAAE;cAAA1C,QAAA,GAECuJ,aAAa,CAACpI,MAAM,GAAG,CAAC,iBACvBpE,OAAA,CAAAE,SAAA;gBAAA+C,QAAA,EACGuJ,aAAa,CAACxG,GAAG,CAACkE,KAAK,iBACtBlK,OAAA;kBAEEsC,KAAK,EAAE;oBACLqE,UAAU,EAAE,2BAA2B;oBACvC1B,YAAY,EAAE,MAAM;oBACpBuI,OAAO,EAAE,QAAQ;oBACjB/J,MAAM,EAAE,8BAA8B;oBACtCmL,cAAc,EAAE,YAAY;oBAC5BrB,SAAS,EAAE,gCAAgC;oBAC3CvI,UAAU,EAAE;kBACd,CAAE;kBACFvC,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,kBAAkB;oBACpDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACiL,SAAS,GAAG,gCAAgC;kBACpE,CAAE;kBACF7K,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,eAAe;oBACjDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACiL,SAAS,GAAG,gCAAgC;kBACpE,CAAE;kBAAAtK,QAAA,gBAGFjD,OAAA;oBAAKsC,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB8C,GAAG,EAAE,MAAM;sBACXxC,YAAY,EAAE;oBAChB,CAAE;oBAAAF,QAAA,GACC,CAAC,MAAM;sBACN,MAAM6D,eAAe,GAAG,CAACoD,KAAK,CAAC2E,iBAAiB,IAAI,cAAc,EAAEC,WAAW,CAAC,CAAC;sBACjF,MAAMC,YAAY,GAAGlI,mBAAmB,CAACC,eAAe,CAAC;sBACzD,MAAMkI,aAAa,GAAGD,YAAY,CAACnI,IAAI;sBAEvC,oBACE5G,OAAA;wBAAKsC,KAAK,EAAE;0BACVsC,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdI,YAAY,EAAE,MAAM;0BACpB0B,UAAU,EAAEoI,YAAY,CAACpI,UAAU;0BACnC/D,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpBC,cAAc,EAAE;wBAClB,CAAE;wBAAAG,QAAA,eACAjD,OAAA,CAACgP,aAAa;0BAACnB,IAAI,EAAE,EAAG;0BAAC7K,KAAK,EAAC;wBAAO;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAEV,CAAC,EAAE,CAAC,eACJxD,OAAA;sBAAKsC,KAAK,EAAE;wBAAE8K,IAAI,EAAE;sBAAE,CAAE;sBAAAnK,QAAA,gBACtBjD,OAAA;wBAAKsC,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE,QAAQ;0BACbxC,YAAY,EAAE;wBAChB,CAAE;wBAAAF,QAAA,EACC,CAAC,MAAM;0BACN,MAAM6D,eAAe,GAAG,CAACoD,KAAK,CAAC2E,iBAAiB,IAAI,cAAc,EAAEC,WAAW,CAAC,CAAC;0BACjF,MAAMC,YAAY,GAAGlI,mBAAmB,CAACC,eAAe,CAAC;0BACzD,MAAMkI,aAAa,GAAGD,YAAY,CAACnI,IAAI;0BAEvC,oBACE5G,OAAA;4BAAMsC,KAAK,EAAE;8BACXqE,UAAU,EAAEoI,YAAY,CAACpI,UAAU;8BACnC3D,KAAK,EAAE,OAAO;8BACdwK,OAAO,EAAE,iBAAiB;8BAC1BvI,YAAY,EAAE,MAAM;8BACpB7B,QAAQ,EAAE,SAAS;8BACnBM,UAAU,EAAE,KAAK;8BACjBd,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpB8C,GAAG,EAAE;4BACP,CAAE;4BAAA1C,QAAA,gBACAjD,OAAA,CAACgP,aAAa;8BAACnB,IAAI,EAAE,EAAG;8BAAC7K,KAAK,EAAC;4BAAO;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EACxCsD,eAAe;0BAAA;4BAAAzD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACZ,CAAC;wBAEX,CAAC,EAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC,eACNxD,OAAA;wBAAKsC,KAAK,EAAE;0BACVU,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE;wBACZ,CAAE;wBAAAH,QAAA,EACC,IAAI+I,IAAI,CAAC9B,KAAK,CAACiC,UAAU,CAAC,CAAC8C,kBAAkB,CAAC,OAAO,EAAE;0BACtDC,OAAO,EAAE,MAAM;0BACfC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,MAAM;0BACbC,GAAG,EAAE;wBACP,CAAC;sBAAC;wBAAAhM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNxD,OAAA;oBAAIsC,KAAK,EAAE;sBACToL,MAAM,EAAE,eAAe;sBACvB1K,KAAK,EAAE,SAAS;sBAChBI,QAAQ,EAAE,SAAS;sBACnBM,UAAU,EAAE,KAAK;sBACjBiK,UAAU,EAAE;oBACd,CAAE;oBAAA1K,QAAA,EACCiH,KAAK,CAACqB;kBAAK;oBAAAlI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,EAEJ0G,KAAK,CAAC4B,WAAW,iBAChB9L,OAAA;oBAAGsC,KAAK,EAAE;sBACRoL,MAAM,EAAE,YAAY;sBACpB1K,KAAK,EAAE,SAAS;sBAChBI,QAAQ,EAAE,SAAS;sBACnBuK,UAAU,EAAE;oBACd,CAAE;oBAAA1K,QAAA,EACCiH,KAAK,CAAC4B;kBAAW;oBAAAzI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CACJ,EAGA,CAAC,MAAM;oBACN;oBACA,MAAM8L,cAAwB,GAAG,EAAE;oBAEnC,IAAKpF,KAAK,CAASlG,MAAM,IAAKkG,KAAK,CAASlG,MAAM,CAACI,MAAM,GAAG,CAAC,EAAE;sBAC5D8F,KAAK,CAASlG,MAAM,CAACuL,OAAO,CAAEC,GAAQ,IAAK;wBAC1C,IAAIA,GAAG,CAACC,SAAS,EAAE;0BACjB;0BACA,MAAMnP,QAAQ,GAAG5B,WAAW,CAAC8Q,GAAG,CAACC,SAAS,CAAC;0BAC3C,IAAInP,QAAQ,EAAE;4BACZgP,cAAc,CAACI,IAAI,CAACpP,QAAQ,CAAC;0BAC/B;wBACF;sBACF,CAAC,CAAC;oBACJ;oBAEA,OAAOgP,cAAc,CAAClL,MAAM,GAAG,CAAC,gBAC9BpE,OAAA;sBAAKsC,KAAK,EAAE;wBAAEa,YAAY,EAAE;sBAAO,CAAE;sBAAAF,QAAA,eACnCjD,OAAA,CAAC+D,oBAAoB;wBACnBC,MAAM,EAAEsL,cAAc,CAAC3F,MAAM,CAACgG,OAAO,CAAc;wBACnD1L,SAAS,EAAEiG,KAAK,CAACqB,KAAM;wBACvBrH,UAAU,EAAE,CAAE;wBACdC,YAAY,EAAGM,KAAK,IAAK;0BACvB1D,OAAO,CAACC,GAAG,CAAC,iBAAiByD,KAAK,GAAG,CAAC,eAAeyF,KAAK,CAACqB,KAAK,EAAE,CAAC;wBACrE;sBAAE;wBAAAlI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,GACJ,IAAI;kBACV,CAAC,EAAE,CAAC,eAGJxD,OAAA;oBAAKsC,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE,eAAe;sBAC/B8M,UAAU,EAAE,MAAM;sBAClBlB,SAAS,EAAE;oBACb,CAAE;oBAAAzL,QAAA,eACAjD,OAAA;sBAAKsC,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB8C,GAAG,EAAE,MAAM;wBACX3C,KAAK,EAAE,SAAS;wBAChBI,QAAQ,EAAE;sBACZ,CAAE;sBAAAH,QAAA,gBACAjD,OAAA;wBAAMsC,KAAK,EAAE;0BAAEM,OAAO,EAAE,MAAM;0BAAEC,UAAU,EAAE,QAAQ;0BAAE8C,GAAG,EAAE;wBAAU,CAAE;wBAAA1C,QAAA,gBACrEjD,OAAA,CAACb,MAAM;0BAAC0O,IAAI,EAAE,EAAG;0BAAC7K,KAAK,EAAC;wBAAS;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAEtC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,EACN0G,KAAK,CAAC2F,QAAQ,IAAI3F,KAAK,CAAC2F,QAAQ,KAAK3F,KAAK,CAACiC,UAAU,iBACpDnM,OAAA;wBAAAiD,QAAA,GAAM,QACE,EAAC,IAAI+I,IAAI,CAAC9B,KAAK,CAAC2F,QAAQ,CAAC,CAACZ,kBAAkB,CAAC,CAAC;sBAAA;wBAAA5L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD,CACP;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAvKD,SAAS0G,KAAK,CAACC,WAAW,EAAE;kBAAA9G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAwK9B,CACN;cAAC,gBACF,CACH,EAGA+I,oBAAoB,CAACnI,MAAM,GAAG,CAAC,iBAC9BpE,OAAA,CAAAE,SAAA;gBAAA+C,QAAA,EACGsJ,oBAAoB,CAACvG,GAAG,CAACoD,YAAY;kBAAA,IAAA0G,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;kBAAA,oBACpCjQ,OAAA;oBAEEsC,KAAK,EAAE;sBACLqE,UAAU,EAAE,2BAA2B;sBACvC1B,YAAY,EAAE,MAAM;sBACpBuI,OAAO,EAAE,QAAQ;sBACjB/J,MAAM,EAAE,8BAA8B;sBACtCmL,cAAc,EAAE,YAAY;sBAC5BrB,SAAS,EAAE,gCAAgC;sBAC3CvI,UAAU,EAAE;oBACd,CAAE;oBACFvC,YAAY,EAAGoB,CAAC,IAAK;sBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,kBAAkB;sBACpDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACiL,SAAS,GAAG,gCAAgC;oBACpE,CAAE;oBACF7K,YAAY,EAAGmB,CAAC,IAAK;sBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,eAAe;sBACjDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACiL,SAAS,GAAG,gCAAgC;oBACpE,CAAE;oBAAAtK,QAAA,gBAGFjD,OAAA;sBAAKsC,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB8C,GAAG,EAAE,MAAM;wBACXxC,YAAY,EAAE;sBAChB,CAAE;sBAAAF,QAAA,GACC,CAAC,MAAM;wBACN,MAAMwD,YAAY,GAAG,CAAC2C,YAAY,CAAC8G,aAAa,IAAI,SAAS,EAAEpB,WAAW,CAAC,CAAC;wBAC5E,MAAMqB,aAAa,GAAG3J,gBAAgB,CAACC,YAAY,CAAC;wBACpD,MAAMuI,aAAa,GAAGmB,aAAa,CAACvJ,IAAI;wBAExC,oBACE5G,OAAA;0BAAKsC,KAAK,EAAE;4BACVsC,KAAK,EAAE,MAAM;4BACbC,MAAM,EAAE,MAAM;4BACdI,YAAY,EAAE,MAAM;4BACpB0B,UAAU,EAAEwJ,aAAa,CAACxJ,UAAU;4BACpC/D,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpBC,cAAc,EAAE;0BAClB,CAAE;0BAAAG,QAAA,eACAjD,OAAA,CAACgP,aAAa;4BAACnB,IAAI,EAAE,EAAG;4BAAC7K,KAAK,EAAC;0BAAO;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtC,CAAC;sBAEV,CAAC,EAAE,CAAC,eACJxD,OAAA;wBAAKsC,KAAK,EAAE;0BAAE8K,IAAI,EAAE;wBAAE,CAAE;wBAAAnK,QAAA,gBACtBjD,OAAA;0BAAKsC,KAAK,EAAE;4BACVM,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpB8C,GAAG,EAAE,QAAQ;4BACbxC,YAAY,EAAE;0BAChB,CAAE;0BAAAF,QAAA,GACC,CAAC,MAAM;4BACN,MAAMwD,YAAY,GAAG,CAAC2C,YAAY,CAAC8G,aAAa,IAAI,SAAS,EAAEpB,WAAW,CAAC,CAAC;4BAC5E,MAAMqB,aAAa,GAAG3J,gBAAgB,CAACC,YAAY,CAAC;4BACpD,MAAMuI,aAAa,GAAGmB,aAAa,CAACvJ,IAAI;4BAExC,oBACE5G,OAAA;8BAAMsC,KAAK,EAAE;gCACXqE,UAAU,EAAEwJ,aAAa,CAACxJ,UAAU;gCACpC3D,KAAK,EAAE,OAAO;gCACdwK,OAAO,EAAE,iBAAiB;gCAC1BvI,YAAY,EAAE,MAAM;gCACpB7B,QAAQ,EAAE,SAAS;gCACnBM,UAAU,EAAE,KAAK;gCACjBd,OAAO,EAAE,MAAM;gCACfC,UAAU,EAAE,QAAQ;gCACpB8C,GAAG,EAAE;8BACP,CAAE;8BAAA1C,QAAA,gBACAjD,OAAA,CAACgP,aAAa;gCAACnB,IAAI,EAAE,EAAG;gCAAC7K,KAAK,EAAC;8BAAO;gCAAAK,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,EACxCiD,YAAY;4BAAA;8BAAApD,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACT,CAAC;0BAEX,CAAC,EAAE,CAAC,EACH4F,YAAY,CAACS,SAAS,KAAK,CAAC,iBAC3B7J,OAAA;4BAAMsC,KAAK,EAAE;8BACXqE,UAAU,EAAE,mDAAmD;8BAC/D3D,KAAK,EAAE,OAAO;8BACdwK,OAAO,EAAE,iBAAiB;8BAC1BvI,YAAY,EAAE,MAAM;8BACpB7B,QAAQ,EAAE,SAAS;8BACnBM,UAAU,EAAE,KAAK;8BACjBd,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpB8C,GAAG,EAAE;4BACP,CAAE;4BAAA1C,QAAA,gBACAjD,OAAA,CAAClB,GAAG;8BAAC+O,IAAI,EAAE,EAAG;8BAAC7K,KAAK,EAAC;4BAAO;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,UAEjC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CACP;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACNxD,OAAA;0BAAKsC,KAAK,EAAE;4BACVU,KAAK,EAAE,SAAS;4BAChBI,QAAQ,EAAE;0BACZ,CAAE;0BAAAH,QAAA,GAAC,KACE,EAACmG,YAAY,CAACgH,WAAW,EAAC,UAAG,EAAC,IAAIpE,IAAI,CAAC5C,YAAY,CAACiH,YAAY,CAAC,CAACpB,kBAAkB,CAAC,OAAO,EAAE;4BAC/FC,OAAO,EAAE,OAAO;4BAChBC,IAAI,EAAE,SAAS;4BACfC,KAAK,EAAE,OAAO;4BACdC,GAAG,EAAE,SAAS;4BACdiB,IAAI,EAAE,SAAS;4BACfC,MAAM,EAAE;0BACV,CAAC,CAAC;wBAAA;0BAAAlN,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGNxD,OAAA;sBAAIsC,KAAK,EAAE;wBACToL,MAAM,EAAE,eAAe;wBACvB1K,KAAK,EAAE,SAAS;wBAChBI,QAAQ,EAAE,SAAS;wBACnBM,UAAU,EAAE,KAAK;wBACjBiK,UAAU,EAAE;sBACd,CAAE;sBAAA1K,QAAA,EACCmG,YAAY,CAACmC;oBAAK;sBAAAlI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,EAGJ,CAAC,MAAM;sBACN;sBACA,MAAMgN,SAAmB,GAAG,EAAE;;sBAE9B;sBACA,IAAIpH,YAAY,CAACpF,MAAM,IAAIoF,YAAY,CAACpF,MAAM,CAACI,MAAM,GAAG,CAAC,EAAE;wBACzDgF,YAAY,CAACpF,MAAM,CAACuL,OAAO,CAAEC,GAA2B,IAAK;0BAC3D,IAAIA,GAAG,CAACiB,QAAQ,EAAE;4BAChBD,SAAS,CAACd,IAAI,CAACF,GAAG,CAACiB,QAAQ,CAAC;0BAC9B;wBACF,CAAC,CAAC;sBACJ;;sBAEA;sBACA,IAAID,SAAS,CAACpM,MAAM,KAAK,CAAC,KAAKgF,YAAY,CAACsH,SAAS,IAAItH,YAAY,CAACuH,UAAU,CAAC,EAAE;wBACjFH,SAAS,CAACd,IAAI,CAACtG,YAAY,CAACsH,SAAS,IAAItH,YAAY,CAACuH,UAAU,CAAC;sBACnE;sBAEA,OAAOH,SAAS,CAACpM,MAAM,GAAG,CAAC,gBACzBpE,OAAA,CAAC+D,oBAAoB;wBACnBC,MAAM,EAAEwM,SAAS,CAAC7G,MAAM,CAACgG,OAAO,CAAc;wBAC9C1L,SAAS,EAAEmF,YAAY,CAACmC,KAAM;wBAC9BrH,UAAU,EAAE,CAAE;wBACdC,YAAY,EAAGM,KAAK,IAAK;0BACvB1D,OAAO,CAACC,GAAG,CAAC,iBAAiByD,KAAK,GAAG,CAAC,sBAAsB2E,YAAY,CAACmC,KAAK,EAAE,CAAC;0BACjF;wBACF;sBAAE;wBAAAlI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,GACA,IAAI;oBACV,CAAC,EAAE,CAAC,eAEJxD,OAAA;sBAAKsC,KAAK,EAAE;wBACVoL,MAAM,EAAE,cAAc;wBACtB1K,KAAK,EAAE,SAAS;wBAChBI,QAAQ,EAAE,SAAS;wBACnBuK,UAAU,EAAE;sBACd,CAAE;sBACFiD,uBAAuB,EAAE;wBAAEC,MAAM,EAAEzH,YAAY,CAAC6B;sBAAQ;oBAAE;sBAAA5H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzD,CAAC,eAGFxD,OAAA;sBAAKsC,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpBC,cAAc,EAAE,eAAe;wBAC/B8M,UAAU,EAAE,MAAM;wBAClBlB,SAAS,EAAE;sBACb,CAAE;sBAAAzL,QAAA,gBACAjD,OAAA;wBAAKsC,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE;wBACP,CAAE;wBAAA1C,QAAA,gBAEAjD,OAAA;0BACE8F,OAAO,EAAEA,CAAA,KAAMuE,gBAAgB,CAACjB,YAAY,CAAE;0BAC9C9G,KAAK,EAAE;4BACLqE,UAAU,EAAEyC,YAAY,CAACkB,aAAa,GAAG,wBAAwB,GAAG,aAAa;4BACjFtH,KAAK,EAAEoG,YAAY,CAACkB,aAAa,GAAG,SAAS,GAAG,SAAS;4BACzD7G,MAAM,EAAE,MAAM;4BACdwB,YAAY,EAAE,KAAK;4BACnBuI,OAAO,EAAE,aAAa;4BACtBpK,QAAQ,EAAE,UAAU;4BACpBM,UAAU,EAAE,KAAK;4BACjBqB,MAAM,EAAE,SAAS;4BACjBnC,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpB8C,GAAG,EAAE,QAAQ;4BACbX,UAAU,EAAE;0BACd,CAAE;0BACFvC,YAAY,EAAGoB,CAAC,IAAK;4BACnB,IAAI,CAACuF,YAAY,CAACkB,aAAa,EAAE;8BAC/BzG,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACqE,UAAU,GAAG,wBAAwB;8BAC3D9C,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;4BACzC;0BACF,CAAE;0BACFN,YAAY,EAAGmB,CAAC,IAAK;4BACnB,IAAI,CAACuF,YAAY,CAACkB,aAAa,EAAE;8BAC/BzG,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACqE,UAAU,GAAG,aAAa;8BAChD9C,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;4BACzC;0BACF,CAAE;0BAAAC,QAAA,gBAEFjD,OAAA,CAACf,KAAK;4BACJ4O,IAAI,EAAE,EAAG;4BACT7K,KAAK,EAAEoG,YAAY,CAACkB,aAAa,GAAG,SAAS,GAAG,SAAU;4BAC1DwG,IAAI,EAAE1H,YAAY,CAACkB,aAAa,GAAG,SAAS,GAAG;0BAAO;4BAAAjH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvD,CAAC,eACFxD,OAAA;4BAAAiD,QAAA,EAAOmG,YAAY,CAAC2H,cAAc,IAAI;0BAAC;4BAAA1N,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CAAC,EAGR4F,YAAY,CAAC4H,cAAc,iBAC1BhR,OAAA;0BACE8F,OAAO,EAAEA,CAAA,KAAMwB,eAAe,CAC5BD,YAAY,KAAK+B,YAAY,CAACE,eAAe,GAAG,IAAI,GAAGF,YAAY,CAACE,eACtE,CAAE;0BACFhH,KAAK,EAAE;4BACLqE,UAAU,EAAEU,YAAY,KAAK+B,YAAY,CAACE,eAAe,GAAG,yBAAyB,GAAG,aAAa;4BACrGtG,KAAK,EAAEqE,YAAY,KAAK+B,YAAY,CAACE,eAAe,GAAG,SAAS,GAAG,SAAS;4BAC5E7F,MAAM,EAAE,MAAM;4BACdwB,YAAY,EAAE,KAAK;4BACnBuI,OAAO,EAAE,aAAa;4BACtBpK,QAAQ,EAAE,UAAU;4BACpBM,UAAU,EAAE,KAAK;4BACjBqB,MAAM,EAAE,SAAS;4BACjBnC,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpB8C,GAAG,EAAE,QAAQ;4BACbX,UAAU,EAAE;0BACd,CAAE;0BACFvC,YAAY,EAAGoB,CAAC,IAAK;4BACnB,IAAIwD,YAAY,KAAK+B,YAAY,CAACE,eAAe,EAAE;8BACjDzF,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACqE,UAAU,GAAG,yBAAyB;8BAC5D9C,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;4BACzC;0BACF,CAAE;0BACFN,YAAY,EAAGmB,CAAC,IAAK;4BACnB,IAAIwD,YAAY,KAAK+B,YAAY,CAACE,eAAe,EAAE;8BACjDzF,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACqE,UAAU,GAAG,aAAa;8BAChD9C,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;4BACzC;0BACF,CAAE;0BAAAC,QAAA,gBAEFjD,OAAA,CAAChB,aAAa;4BAAC6O,IAAI,EAAE,EAAG;4BAAC7K,KAAK,EAAC;0BAAS;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAC3CxD,OAAA;4BAAAiD,QAAA,EAAOmG,YAAY,CAAC6H,aAAa,IAAI;0BAAC;4BAAA5N,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CACT;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAENxD,OAAA;wBAAKsC,KAAK,EAAE;0BACVU,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE;wBACZ,CAAE;wBAAAH,QAAA,GACCmG,YAAY,CAAC8H,UAAU,IAAI,CAAC,EAAC,QAChC;sBAAA;wBAAA7N,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,EAGL6D,YAAY,KAAK+B,YAAY,CAACE,eAAe,IAAIF,YAAY,CAAC4H,cAAc,iBAC3EhR,OAAA;sBAAKsC,KAAK,EAAE;wBACVqB,SAAS,EAAE,MAAM;wBACjBiM,UAAU,EAAE,MAAM;wBAClBlB,SAAS,EAAE;sBACb,CAAE;sBAAAzL,QAAA,gBAEAjD,OAAA;wBAAKsC,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACf+C,GAAG,EAAE,SAAS;0BACdxC,YAAY,EAAE;wBAChB,CAAE;wBAAAF,QAAA,gBACAjD,OAAA;0BAAKsC,KAAK,EAAE;4BACVsC,KAAK,EAAE,MAAM;4BACbC,MAAM,EAAE,MAAM;4BACdI,YAAY,EAAE,KAAK;4BACnB0B,UAAU,EAAE,mDAAmD;4BAC/D/D,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpBC,cAAc,EAAE,QAAQ;4BACxBE,KAAK,EAAE,OAAO;4BACdU,UAAU,EAAE,KAAK;4BACjBN,QAAQ,EAAE;0BACZ,CAAE;0BAAAH,QAAA,EAAC;wBAEH;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACNxD,OAAA;0BAAKsC,KAAK,EAAE;4BAAE8K,IAAI,EAAE;0BAAE,CAAE;0BAAAnK,QAAA,gBACtBjD,OAAA;4BACE+N,KAAK,EAAExG,UAAU,CAAC6B,YAAY,CAACE,eAAe,CAAC,IAAI,EAAG;4BACtD0E,QAAQ,EAAGnK,CAAC,IAAK2D,aAAa,CAAC4D,IAAI,KAAK;8BACtC,GAAGA,IAAI;8BACP,CAAChC,YAAY,CAACE,eAAe,GAAGzF,CAAC,CAACoK,MAAM,CAACF;4BAC3C,CAAC,CAAC,CAAE;4BACJD,WAAW,EAAC,oBAAoB;4BAChCxL,KAAK,EAAE;8BACLsC,KAAK,EAAE,MAAM;8BACbsI,SAAS,EAAE,MAAM;8BACjBM,OAAO,EAAE,SAAS;8BAClB/J,MAAM,EAAE,mBAAmB;8BAC3BwB,YAAY,EAAE,MAAM;8BACpB7B,QAAQ,EAAE,QAAQ;8BAClB8K,OAAO,EAAE,MAAM;8BACfiD,MAAM,EAAE,UAAU;8BAClBC,UAAU,EAAE;4BACd;0BAAE;4BAAA/N,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eACFxD,OAAA;4BAAKsC,KAAK,EAAE;8BACVM,OAAO,EAAE,MAAM;8BACfE,cAAc,EAAE,UAAU;8BAC1Ba,SAAS,EAAE;4BACb,CAAE;4BAAAV,QAAA,eACAjD,OAAA;8BACE8F,OAAO,EAAEA,CAAA,KAAM+E,mBAAmB,CAChCzB,YAAY,CAACE,eAAe,EAC5B/B,UAAU,CAAC6B,YAAY,CAACE,eAAe,CAAC,IAAI,EAC9C,CAAE;8BACF+H,QAAQ,EAAE,GAAAvB,qBAAA,GAACvI,UAAU,CAAC6B,YAAY,CAACE,eAAe,CAAC,cAAAwG,qBAAA,eAAxCA,qBAAA,CAA0C9E,IAAI,CAAC,CAAC,KAAIvD,iBAAiB,KAAK2B,YAAY,CAACE,eAAgB;8BAClHhH,KAAK,EAAE;gCACLqE,UAAU,EAAE,CAAAoJ,sBAAA,GAAAxI,UAAU,CAAC6B,YAAY,CAACE,eAAe,CAAC,cAAAyG,sBAAA,eAAxCA,sBAAA,CAA0C/E,IAAI,CAAC,CAAC,GACxD,mDAAmD,GACnD,SAAS;gCACbhI,KAAK,EAAE,CAAAgN,sBAAA,GAAAzI,UAAU,CAAC6B,YAAY,CAACE,eAAe,CAAC,cAAA0G,sBAAA,eAAxCA,sBAAA,CAA0ChF,IAAI,CAAC,CAAC,GAAG,OAAO,GAAG,SAAS;gCAC7EvH,MAAM,EAAE,MAAM;gCACdwB,YAAY,EAAE,KAAK;gCACnBuI,OAAO,EAAE,aAAa;gCACtBpK,QAAQ,EAAE,UAAU;gCACpBM,UAAU,EAAE,KAAK;gCACjBqB,MAAM,EAAE,CAAAkL,sBAAA,GAAA1I,UAAU,CAAC6B,YAAY,CAACE,eAAe,CAAC,cAAA2G,sBAAA,eAAxCA,sBAAA,CAA0CjF,IAAI,CAAC,CAAC,GAAG,SAAS,GAAG,aAAa;gCACpFhG,UAAU,EAAE;8BACd,CAAE;8BAAA/B,QAAA,EAEDwE,iBAAiB,KAAK2B,YAAY,CAACE,eAAe,GAAG,YAAY,GAAG;4BAAc;8BAAAjG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC7E;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGNxD,OAAA,CAACvB,cAAc;wBACbqM,cAAc,EAAE1B,YAAY,CAACE,eAAgB;wBAC7CgI,aAAa,EAAElI,YAAY,CAAC4H,cAAe;wBAC3CO,eAAe,EAAC;sBAAS;wBAAAlO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CACN;kBAAA,GAtVI,gBAAgB4F,YAAY,CAACE,eAAe,EAAE;oBAAAjG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAuVhD,CAAC;gBAAA,CACP;cAAC,gBACF,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAAC8C,GAAA,CAn8CID,eAAyB;EAAA,QACZ/H,WAAW,EAqFLC,aAAa;AAAA;AAAAiT,GAAA,GAtFhCnL,eAAyB;AAq8C/B,eAAeA,eAAe;AAAC,IAAAvC,EAAA,EAAAsC,GAAA,EAAAoL,GAAA;AAAAC,YAAA,CAAA3N,EAAA;AAAA2N,YAAA,CAAArL,GAAA;AAAAqL,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}