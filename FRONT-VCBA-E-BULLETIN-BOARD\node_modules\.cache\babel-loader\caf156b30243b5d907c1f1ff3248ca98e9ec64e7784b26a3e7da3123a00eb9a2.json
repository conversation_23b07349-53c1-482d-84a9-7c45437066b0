{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 10a7.31 7.31 0 0 0 10 10Z\",\n  key: \"1fzpp3\"\n}], [\"path\", {\n  d: \"m9 15 3-3\",\n  key: \"88sc13\"\n}], [\"path\", {\n  d: \"M17 13a6 6 0 0 0-6-6\",\n  key: \"15cc6u\"\n}], [\"path\", {\n  d: \"M21 13A10 10 0 0 0 11 3\",\n  key: \"11nf8s\"\n}]];\nconst SatelliteDish = createLucideIcon(\"satellite-dish\", __iconNode);\nexport { __iconNode, SatelliteDish as default };\n//# sourceMappingURL=satellite-dish.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}