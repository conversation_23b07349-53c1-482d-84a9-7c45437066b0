{"ast": null, "code": "var _jsxFileName = \"D:\\\\capstone-e-bulletin-reactjs-proj\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\AdminNewsfeed.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { announcementService } from '../../services';\nimport { useCategories } from '../../hooks/useAnnouncements';\nimport AdminCommentSection from '../../components/admin/AdminCommentSection';\nimport FacebookImageGallery from '../../components/common/FacebookImageGallery';\nimport { getImageUrl, API_BASE_URL } from '../../config/constants';\nimport { Newspaper, Search, Pin, Calendar, MessageSquare, Heart, Eye, Edit, Users, LayoutDashboard, BookOpen, PartyPopper, AlertTriangle, Clock, Trophy, Briefcase, GraduationCap, Flag, Coffee, Plane } from 'lucide-react';\n\n// Image Display Component\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ImageDisplay = ({\n  imagePath,\n  alt,\n  style,\n  onMouseEnter,\n  onMouseLeave\n}) => {\n  _s();\n  const [imageError, setImageError] = useState(false);\n  const [imageLoading, setImageLoading] = useState(true);\n  const handleImageError = () => {\n    setImageError(true);\n    setImageLoading(false);\n  };\n  const handleImageLoad = () => {\n    setImageLoading(false);\n  };\n  if (imageError) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f3f4f6',\n        color: '#6b7280',\n        fontSize: '0.875rem'\n      },\n      children: \"Image not available\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'relative',\n      ...style\n    },\n    children: [imageLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f3f4f6',\n        color: '#6b7280'\n      },\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n      src: getImageUrl(imagePath || '') || '',\n      alt: alt,\n      style: {\n        ...style,\n        opacity: imageLoading ? 0 : 1,\n        transition: 'opacity 0.3s ease'\n      },\n      onError: handleImageError,\n      onLoad: handleImageLoad,\n      onMouseEnter: onMouseEnter,\n      onMouseLeave: onMouseLeave\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n};\n\n// Image Gallery Component\n_s(ImageDisplay, \"yA6MC4/13YXgE42AlKw5vrWMK58=\");\n_c = ImageDisplay;\nconst ImageGallery = ({\n  images,\n  altPrefix,\n  onImageClick\n}) => {\n  if (!images || images.length === 0) return null;\n  const visibleImages = images.slice(0, 4);\n  const remainingCount = Math.max(0, images.length - 4);\n  const getContainerStyle = (index, total) => {\n    const baseStyle = {\n      position: 'relative',\n      overflow: 'hidden',\n      borderRadius: '12px',\n      cursor: onImageClick ? 'pointer' : 'default'\n    };\n    if (total === 1) {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '300px'\n      };\n    } else if (total === 2) {\n      return {\n        ...baseStyle,\n        width: '50%',\n        height: '250px'\n      };\n    } else if (total === 3) {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '50%',\n          height: '250px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '50%',\n          height: '120px'\n        };\n      }\n    } else {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '50%',\n          height: '250px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '33.33%',\n          height: '120px'\n        };\n      }\n    }\n  };\n  const getImageStyle = (index, total) => {\n    return {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover',\n      transition: 'transform 0.3s ease'\n    };\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: getContainerStyle(0, visibleImages.length),\n      children: [/*#__PURE__*/_jsxDEV(ImageDisplay, {\n        imagePath: visibleImages[0].file_path,\n        alt: `${altPrefix} - Image 1`,\n        style: getImageStyle(0, visibleImages.length),\n        onMouseEnter: e => {\n          e.currentTarget.style.transform = 'scale(1.02)';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.transform = 'scale(1)';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          cursor: 'pointer'\n        },\n        onClick: () => onImageClick(0)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this), visibleImages.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: visibleImages.length === 2 ? 'row' : 'column',\n        gap: '4px',\n        width: visibleImages.length === 2 ? '50%' : '50%'\n      },\n      children: visibleImages.slice(1).map((image, idx) => {\n        const actualIndex = idx + 1;\n        const isLast = actualIndex === visibleImages.length - 1 && remainingCount > 0;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...getContainerStyle(actualIndex, visibleImages.length),\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ImageDisplay, {\n            imagePath: image.file_path,\n            alt: `${altPrefix} - Image ${actualIndex + 1}`,\n            style: getImageStyle(actualIndex, visibleImages.length),\n            onMouseEnter: e => {\n              e.currentTarget.style.transform = 'scale(1.02)';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.transform = 'scale(1)';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 17\n          }, this), isLast && remainingCount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              backgroundColor: 'rgba(0, 0, 0, 0.6)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              fontSize: '1.5rem',\n              fontWeight: '600'\n            },\n            children: [\"+\", remainingCount]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 19\n          }, this), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            },\n            onClick: () => onImageClick(actualIndex)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 19\n          }, this)]\n        }, actualIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 162,\n    columnNumber: 5\n  }, this);\n};\n\n// Main AdminNewsfeed Component\n_c2 = ImageGallery;\nconst AdminNewsfeed = () => {\n  _s2();\n  const navigate = useNavigate();\n\n  // Category styling function\n  const getCategoryStyle = categoryName => {\n    const styles = {\n      'ACADEMIC': {\n        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n        icon: BookOpen\n      },\n      'GENERAL': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Users\n      },\n      'EVENTS': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: PartyPopper\n      },\n      'EMERGENCY': {\n        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n        icon: AlertTriangle\n      },\n      'SPORTS': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Trophy\n      },\n      'DEADLINES': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Clock\n      }\n    };\n    return styles[categoryName] || styles['GENERAL'];\n  };\n\n  // Holiday type styling function\n  const getHolidayTypeStyle = holidayTypeName => {\n    const styles = {\n      'National Holiday': {\n        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n        icon: Flag\n      },\n      'School Event': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: GraduationCap\n      },\n      'Academic Break': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Coffee\n      },\n      'Sports Event': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Trophy\n      },\n      'Field Trip': {\n        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n        icon: Plane\n      },\n      'Meeting': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Briefcase\n      }\n    };\n    return styles[holidayTypeName] || styles['School Event'];\n  };\n\n  // Filter states\n  const [filterCategory, setFilterCategory] = useState('');\n  const [filterGradeLevel, setFilterGradeLevel] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // UI states\n  const [showComments, setShowComments] = useState(null);\n  const [newComment, setNewComment] = useState({});\n  const [submittingComment, setSubmittingComment] = useState(null);\n  const [selectedPinnedPost, setSelectedPinnedPost] = useState(null);\n\n  // Data states\n  const [announcements, setAnnouncements] = useState([]);\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState([]);\n  const [calendarEvents, setCalendarEvents] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState();\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState();\n  const [recentStudents, setRecentStudents] = useState([]);\n  const [studentLoading, setStudentLoading] = useState(false);\n  const {\n    categories\n  } = useCategories();\n\n  // Fetch published announcements with images (admin can see all)\n  const fetchPublishedAnnouncements = async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const response = await fetch(`${API_BASE_URL}/api/announcements?status=published&page=1&limit=50&sort_by=created_at&sort_order=DESC`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (data.success && data.data) {\n        const announcementsData = data.data.announcements || [];\n\n        // The announcements already include attachments/images from the API\n        // No need to fetch images separately\n        setAnnouncements(announcementsData);\n\n        // Separate pinned announcements\n        const pinned = announcementsData.filter(ann => ann.is_pinned === 1);\n        setPinnedAnnouncements(pinned);\n      } else {\n        setError('Failed to load announcements');\n      }\n    } catch (err) {\n      console.error('Error fetching announcements:', err);\n      setError(err.message || 'Failed to load announcements');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch calendar events\n  const fetchCalendarEvents = async () => {\n    try {\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(eventsData.map(async event => {\n          try {\n            const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`, {\n              headers: {\n                'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n                'Content-Type': 'application/json'\n              }\n            });\n            const imageData = await imageResponse.json();\n            if (imageData.success && imageData.data) {\n              event.images = imageData.data.attachments || [];\n            } else {\n              event.images = [];\n            }\n          } catch (imgErr) {\n            console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n            event.images = [];\n          }\n          return event;\n        }));\n        setCalendarEvents(eventsWithImages);\n      } else {\n        setCalendarError('Failed to load calendar events');\n      }\n    } catch (err) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to load calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  };\n\n  // Fetch recent student registrations\n  const fetchRecentStudents = async () => {\n    try {\n      setStudentLoading(true);\n      const response = await fetch(`${API_BASE_URL}/api/students?page=1&limit=5&sort_by=created_at&sort_order=DESC`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (data.success && data.data) {\n        setRecentStudents(data.data.students || []);\n      }\n    } catch (err) {\n      console.error('Error fetching recent students:', err);\n    } finally {\n      setStudentLoading(false);\n    }\n  };\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchPublishedAnnouncements();\n    fetchCalendarEvents();\n    fetchRecentStudents();\n  }, []);\n\n  // Handle like/unlike functionality (admin perspective)\n  const handleLikeToggle = async announcement => {\n    try {\n      if (announcement.user_reaction) {\n        await announcementService.removeReaction(announcement.announcement_id);\n      } else {\n        const response = await fetch(`${API_BASE_URL}/api/announcements/${announcement.announcement_id}/reactions`, {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            reaction_type_id: 1,\n            notify_admin: false // Admin doesn't need to notify themselves\n          })\n        });\n        if (!response.ok) {\n          throw new Error('Failed to add reaction');\n        }\n      }\n\n      // Refresh data\n      await fetchPublishedAnnouncements();\n    } catch (error) {\n      console.error('Error toggling like:', error);\n    }\n  };\n\n  // Handle comment submission (admin perspective)\n  const handleCommentSubmit = async (announcementId, commentText) => {\n    if (!commentText.trim()) return;\n    try {\n      setSubmittingComment(announcementId);\n      const response = await fetch(`${API_BASE_URL}/api/admin/announcements/${announcementId}/comments`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          content: commentText,\n          commenter_name: 'Admin User',\n          commenter_email: '<EMAIL>',\n          notify_admin: false\n        })\n      });\n      if (response.ok) {\n        setNewComment(prev => ({\n          ...prev,\n          [announcementId]: ''\n        }));\n        await fetchPublishedAnnouncements();\n      } else {\n        console.error('Failed to submit comment');\n      }\n    } catch (error) {\n      console.error('Error submitting comment:', error);\n    } finally {\n      setSubmittingComment(null);\n    }\n  };\n\n  // Filter announcements\n  const filteredAnnouncements = announcements.filter(announcement => {\n    var _announcement$categor, _announcement$grade_l;\n    const matchesSearch = !searchTerm || announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) || announcement.content.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = !filterCategory || ((_announcement$categor = announcement.category_id) === null || _announcement$categor === void 0 ? void 0 : _announcement$categor.toString()) === filterCategory;\n    const matchesGradeLevel = !filterGradeLevel || ((_announcement$grade_l = announcement.grade_level) === null || _announcement$grade_l === void 0 ? void 0 : _announcement$grade_l.toString()) === filterGradeLevel;\n    return matchesSearch && matchesCategory && matchesGradeLevel;\n  });\n\n  // Filter calendar events with date-based filtering\n  const filteredCalendarEvents = calendarEvents.filter(event => {\n    const matchesSearch = !searchTerm || event.title.toLowerCase().includes(searchTerm.toLowerCase()) || event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase());\n\n    // Only show events that are published and on or after today's date\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    const eventDate = new Date(event.event_date);\n    eventDate.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    const isEventDateValid = eventDate >= today;\n    const isPublished = event.is_published === 1;\n    return matchesSearch && isEventDateValid && isPublished;\n  });\n\n  // Combine and sort all content by date (most recent first)\n  const displayAnnouncements = filteredAnnouncements;\n  const displayEvents = filteredCalendarEvents;\n\n  // Create combined content array for better chronological display\n  const combinedContent = [...displayAnnouncements.map(item => ({\n    ...item,\n    type: 'announcement',\n    sortDate: new Date(item.created_at)\n  })), ...displayEvents.map(item => ({\n    ...item,\n    type: 'event',\n    sortDate: new Date(item.event_date)\n  }))].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)',\n      position: 'relative'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundImage: `\n          radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.03) 0%, transparent 50%),\n          radial-gradient(circle at 75% 75%, rgba(250, 204, 21, 0.03) 0%, transparent 50%)\n        `,\n        pointerEvents: 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 595,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'relative',\n        zIndex: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        style: {\n          background: 'white',\n          borderBottom: '1px solid #e5e7eb',\n          position: 'sticky',\n          top: 0,\n          zIndex: 100,\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '0 2rem',\n            height: '72px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1.5rem',\n              minWidth: '300px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/logo/vcba1.png\",\n              alt: \"VCBA Logo\",\n              style: {\n                width: '48px',\n                height: '48px',\n                objectFit: 'contain'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                style: {\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827',\n                  lineHeight: '1.2'\n                },\n                children: \"VCBA E-Bulletin Board\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 642,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280',\n                  lineHeight: '1.2'\n                },\n                children: \"Admin Newsfeed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 651,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 641,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 626,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1,\n              maxWidth: '500px',\n              margin: '0 2rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Search, {\n                size: 20,\n                style: {\n                  position: 'absolute',\n                  left: '1rem',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  color: '#9ca3af'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 669,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search announcements and events...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                style: {\n                  width: '100%',\n                  height: '44px',\n                  padding: '0 1rem 0 3rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '12px',\n                  background: '#f9fafb',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  transition: 'all 0.2s ease'\n                },\n                onFocus: e => {\n                  e.currentTarget.style.borderColor = '#22c55e';\n                  e.currentTarget.style.background = 'white';\n                  e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n                },\n                onBlur: e => {\n                  e.currentTarget.style.borderColor = '#d1d5db';\n                  e.currentTarget.style.background = '#f9fafb';\n                  e.currentTarget.style.boxShadow = 'none';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 679,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 668,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              minWidth: '400px',\n              justifyContent: 'flex-end'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.5rem',\n                background: '#f9fafb',\n                borderRadius: '12px',\n                border: '1px solid #e5e7eb'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                value: filterCategory,\n                onChange: e => setFilterCategory(e.target.value),\n                style: {\n                  padding: '0.5rem 0.75rem',\n                  border: 'none',\n                  borderRadius: '8px',\n                  background: 'white',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  cursor: 'pointer',\n                  minWidth: '110px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"All Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 744,\n                  columnNumber: 19\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category.category_id.toString(),\n                  children: category.name\n                }, category.category_id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 746,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 729,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filterGradeLevel,\n                onChange: e => setFilterGradeLevel(e.target.value),\n                style: {\n                  padding: '0.5rem 0.75rem',\n                  border: 'none',\n                  borderRadius: '8px',\n                  background: 'white',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  cursor: 'pointer',\n                  minWidth: '100px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"All Grades\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 767,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"11\",\n                  children: \"Grade 11\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 768,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"12\",\n                  children: \"Grade 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 769,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 752,\n                columnNumber: 17\n              }, this), (searchTerm || filterCategory || filterGradeLevel) && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setSearchTerm('');\n                  setFilterCategory('');\n                  setFilterGradeLevel('');\n                },\n                style: {\n                  padding: '0.5rem 0.75rem',\n                  border: 'none',\n                  borderRadius: '8px',\n                  background: '#ef4444',\n                  color: 'white',\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.background = '#dc2626';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.background = '#ef4444';\n                },\n                children: \"Clear\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 773,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 720,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate('/admin/dashboard'),\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.75rem 1rem',\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                border: 'none',\n                borderRadius: '12px',\n                color: 'white',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease',\n                boxShadow: '0 2px 8px rgba(34, 197, 94, 0.2)'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.2)';\n              },\n              children: [/*#__PURE__*/_jsxDEV(LayoutDashboard, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 829,\n                columnNumber: 17\n              }, this), \"Dashboard\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 803,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 711,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 618,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 610,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '2rem',\n          display: 'flex',\n          gap: '2rem',\n          alignItems: 'flex-start'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '320px',\n            flexShrink: 0\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              borderRadius: '16px',\n              border: '1px solid #e5e7eb',\n              overflow: 'hidden',\n              position: 'sticky',\n              top: '100px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '1.5rem 1.5rem 1rem',\n                borderBottom: '1px solid #f3f4f6'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.75rem',\n                  marginBottom: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Pin, {\n                  size: 20,\n                  style: {\n                    color: '#22c55e'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 869,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#111827'\n                  },\n                  children: \"Pinned Posts\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 870,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 863,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                },\n                children: \"Important announcements and updates\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 879,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 859,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '1rem'\n              },\n              children: pinnedAnnouncements.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [pinnedAnnouncements.slice(0, 3).map((announcement, index) => {\n                  const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                  const categoryStyle = getCategoryStyle(categoryName);\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      padding: '1rem',\n                      background: '#f8fafc',\n                      borderRadius: '12px',\n                      border: '1px solid #e2e8f0',\n                      marginBottom: '1rem',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.background = '#f1f5f9';\n                      e.currentTarget.style.borderColor = '#22c55e';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.background = '#f8fafc';\n                      e.currentTarget.style.borderColor = '#e2e8f0';\n                    },\n                    onClick: () => setSelectedPinnedPost(announcement),\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'flex-start',\n                        gap: '0.75rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '8px',\n                          height: '8px',\n                          background: categoryStyle.background.includes('#ef4444') ? '#ef4444' : '#22c55e',\n                          borderRadius: '50%',\n                          marginTop: '0.5rem',\n                          flexShrink: 0\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 923,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          flex: 1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          style: {\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '0.875rem',\n                            fontWeight: '600',\n                            color: '#111827',\n                            lineHeight: '1.4'\n                          },\n                          children: announcement.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 932,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          style: {\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '0.8rem',\n                            color: '#6b7280',\n                            lineHeight: '1.4'\n                          },\n                          children: announcement.content.length > 80 ? `${announcement.content.substring(0, 80)}...` : announcement.content\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 941,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            fontSize: '0.75rem',\n                            color: '#9ca3af'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                            size: 12\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 958,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: new Date(announcement.created_at).toLocaleDateString()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 959,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 951,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 931,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 918,\n                      columnNumber: 27\n                    }, this)\n                  }, announcement.announcement_id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 897,\n                    columnNumber: 25\n                  }, this);\n                }), pinnedAnnouncements.length > 3 && /*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #e5e7eb',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#22c55e',\n                    fontSize: '0.875rem',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.background = '#f0fdf4';\n                    e.currentTarget.style.borderColor = '#22c55e';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.background = 'white';\n                    e.currentTarget.style.borderColor = '#e5e7eb';\n                  },\n                  children: [\"View All \", pinnedAnnouncements.length, \" Pinned Posts\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 968,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '2rem 1rem',\n                  textAlign: 'center',\n                  color: '#6b7280'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Pin, {\n                  size: 24,\n                  style: {\n                    marginBottom: '0.5rem',\n                    opacity: 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 998,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '0.875rem'\n                  },\n                  children: \"No pinned posts available\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 999,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 993,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 889,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 850,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 846,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            minWidth: 0\n          },\n          children: [(loading || calendarLoading) && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              minHeight: '400px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                gap: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '3rem',\n                  height: '3rem',\n                  border: '4px solid rgba(34, 197, 94, 0.2)',\n                  borderTop: '4px solid #22c55e',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1024,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#6b7280',\n                  fontSize: '1rem',\n                  fontWeight: '500'\n                },\n                children: \"Loading content...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1032,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1018,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1012,\n            columnNumber: 13\n          }, this), (error || calendarError) && !loading && !calendarLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '2rem',\n              background: 'rgba(239, 68, 68, 0.1)',\n              border: '1px solid rgba(239, 68, 68, 0.2)',\n              borderRadius: '16px',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '4rem',\n                height: '4rem',\n                background: 'rgba(239, 68, 68, 0.1)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 1rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(MessageSquare, {\n                size: 24,\n                color: \"#ef4444\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1062,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1052,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                color: '#ef4444',\n                margin: '0 0 0.5rem 0',\n                fontSize: '1.25rem',\n                fontWeight: '600'\n              },\n              children: \"Error Loading Content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1064,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#6b7280',\n                margin: '0 0 1.5rem 0',\n                fontSize: '1rem'\n              },\n              children: error || calendarError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1072,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                fetchPublishedAnnouncements();\n                fetchCalendarEvents();\n              },\n              style: {\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '12px',\n                padding: '0.75rem 1.5rem',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = 'none';\n              },\n              children: \"Try Again\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1079,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1045,\n            columnNumber: 13\n          }, this), !loading && !calendarLoading && !error && !calendarError && displayAnnouncements.length === 0 && displayEvents.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '4rem 2rem',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '5rem',\n                height: '5rem',\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 2rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(Newspaper, {\n                size: 32,\n                color: \"white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1126,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1116,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                color: '#374151',\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '600'\n              },\n              children: \"No Content Available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#6b7280',\n                margin: '0 0 2rem 0',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                maxWidth: '500px',\n                marginLeft: 'auto',\n                marginRight: 'auto'\n              },\n              children: searchTerm || filterCategory || filterGradeLevel ? 'No content matches your current filters. Try adjusting your search criteria.' : 'There are no published announcements or events at the moment. Check back later for updates.'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1136,\n              columnNumber: 15\n            }, this), (searchTerm || filterCategory || filterGradeLevel) && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setSearchTerm('');\n                setFilterCategory('');\n                setFilterGradeLevel('');\n              },\n              style: {\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '12px',\n                padding: '0.75rem 1.5rem',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = 'none';\n              },\n              children: \"Clear Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1151,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1112,\n            columnNumber: 13\n          }, this), !studentLoading && recentStudents.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'rgba(255, 255, 255, 0.95)',\n              borderRadius: '16px',\n              padding: '1.5rem',\n              border: '1px solid rgba(0, 0, 0, 0.1)',\n              backdropFilter: 'blur(10px)',\n              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n              marginBottom: '1.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                fontSize: '1.25rem',\n                fontWeight: '600',\n                color: '#2d5016',\n                margin: '0 0 1rem 0',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              },\n              children: \"\\uD83D\\uDC65 Recent Student Registrations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1194,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gap: '0.75rem'\n              },\n              children: recentStudents.slice(0, 3).map(student => {\n                var _student$profile, _student$profile2, _student$profile3;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    padding: '0.75rem',\n                    backgroundColor: '#f8fdf8',\n                    borderRadius: '8px',\n                    border: '1px solid #e8f5e8'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontWeight: '500',\n                        color: '#2d5016',\n                        fontSize: '0.875rem'\n                      },\n                      children: [(_student$profile = student.profile) === null || _student$profile === void 0 ? void 0 : _student$profile.first_name, \" \", (_student$profile2 = student.profile) === null || _student$profile2 === void 0 ? void 0 : _student$profile2.last_name]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1223,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.75rem',\n                        color: '#6b7280'\n                      },\n                      children: [\"Grade \", (_student$profile3 = student.profile) === null || _student$profile3 === void 0 ? void 0 : _student$profile3.grade_level, \" \\u2022 \", student.student_number]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1230,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1222,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.75rem',\n                      color: '#6b7280'\n                    },\n                    children: new Date(student.created_at).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1237,\n                    columnNumber: 21\n                  }, this)]\n                }, student.student_id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1210,\n                  columnNumber: 19\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1205,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1185,\n            columnNumber: 13\n          }, this), !loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem'\n            },\n            children: [displayEvents.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: displayEvents.map(event => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'rgba(255, 255, 255, 0.95)',\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  border: '1px solid rgba(0, 0, 0, 0.1)',\n                  backdropFilter: 'blur(10px)',\n                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                  transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-2px)';\n                  e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    gap: '1rem',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '48px',\n                      height: '48px',\n                      background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                      borderRadius: '12px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      flexShrink: 0\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Calendar, {\n                      size: 24,\n                      color: \"white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1297,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1287,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        marginBottom: '0.5rem'\n                      },\n                      children: [(() => {\n                        const holidayTypeName = event.holiday_type_name || 'School Event';\n                        const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                        const IconComponent = holidayStyle.icon;\n                        return /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            background: holidayStyle.background,\n                            color: 'white',\n                            fontSize: '0.75rem',\n                            fontWeight: '600',\n                            padding: '0.25rem 0.75rem',\n                            borderRadius: '20px',\n                            textTransform: 'uppercase',\n                            letterSpacing: '0.5px',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.25rem'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                            size: 12,\n                            color: \"white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1326,\n                            columnNumber: 35\n                          }, this), holidayTypeName]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1313,\n                          columnNumber: 33\n                        }, this);\n                      })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.25rem',\n                          color: '#6b7280',\n                          fontSize: '0.875rem'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                          size: 14\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1339,\n                          columnNumber: 31\n                        }, this), new Date(event.event_date).toLocaleDateString('en-US', {\n                          weekday: 'long',\n                          year: 'numeric',\n                          month: 'long',\n                          day: 'numeric'\n                        })]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1332,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1301,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      style: {\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        color: '#1f2937',\n                        lineHeight: '1.3'\n                      },\n                      children: event.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1349,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1300,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      gap: '0.5rem'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => navigate(`/admin/calendar?event=${event.calendar_id}`),\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem',\n                        padding: '0.5rem 0.75rem',\n                        background: 'rgba(59, 130, 246, 0.1)',\n                        border: '1px solid rgba(59, 130, 246, 0.2)',\n                        borderRadius: '8px',\n                        color: '#3b82f6',\n                        fontSize: '0.75rem',\n                        fontWeight: '500',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = 'rgba(59, 130, 246, 0.2)';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'rgba(59, 130, 246, 0.1)';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Edit, {\n                        size: 12\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1385,\n                        columnNumber: 29\n                      }, this), \"Edit\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1362,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1361,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1281,\n                  columnNumber: 23\n                }, this), event.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#4b5563',\n                    fontSize: '0.95rem',\n                    lineHeight: '1.6',\n                    marginBottom: '1rem'\n                  },\n                  children: event.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1393,\n                  columnNumber: 25\n                }, this), (() => {\n                  // Get event images if they exist\n                  const eventImageUrls = [];\n                  if (event.images && event.images.length > 0) {\n                    event.images.forEach(img => {\n                      if (img.file_path) {\n                        // Convert file_path to full URL\n                        const imageUrl = getImageUrl(img.file_path);\n                        if (imageUrl) {\n                          eventImageUrls.push(imageUrl);\n                        }\n                      }\n                    });\n                  }\n                  return eventImageUrls.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginBottom: '1rem'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(FacebookImageGallery, {\n                      images: eventImageUrls.filter(Boolean),\n                      altPrefix: event.title,\n                      maxVisible: 4,\n                      onImageClick: index => {\n                        console.log(`Clicked image ${index + 1} for event: ${event.title}`);\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1422,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1421,\n                    columnNumber: 27\n                  }, this) : null;\n                })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1.5rem',\n                    padding: '1rem',\n                    background: 'rgba(59, 130, 246, 0.05)',\n                    borderRadius: '12px',\n                    fontSize: '0.875rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      color: '#6b7280'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1450,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: event.end_date && event.end_date !== event.event_date ? `${new Date(event.event_date).toLocaleDateString()} - ${new Date(event.end_date).toLocaleDateString()}` : new Date(event.event_date).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1451,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1444,\n                    columnNumber: 25\n                  }, this), event.holiday_type_name && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      color: '#6b7280'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        padding: '0.25rem 0.5rem',\n                        background: 'rgba(59, 130, 246, 0.1)',\n                        borderRadius: '6px',\n                        fontSize: '0.75rem',\n                        fontWeight: '500'\n                      },\n                      children: event.holiday_type_name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1466,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1460,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1435,\n                  columnNumber: 23\n                }, this)]\n              }, `event-${event.calendar_id}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1260,\n                columnNumber: 21\n              }, this))\n            }, void 0, false), displayAnnouncements.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: displayAnnouncements.map(announcement => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'rgba(255, 255, 255, 0.95)',\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  border: announcement.is_pinned ? '2px solid rgba(250, 204, 21, 0.3)' : '1px solid rgba(0, 0, 0, 0.1)',\n                  backdropFilter: 'blur(10px)',\n                  boxShadow: announcement.is_pinned ? '0 4px 20px rgba(250, 204, 21, 0.15)' : '0 4px 20px rgba(0, 0, 0, 0.08)',\n                  transition: 'transform 0.2s ease, box-shadow 0.2s ease',\n                  position: 'relative'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-2px)';\n                  e.currentTarget.style.boxShadow = announcement.is_pinned ? '0 8px 30px rgba(250, 204, 21, 0.25)' : '0 8px 30px rgba(0, 0, 0, 0.12)';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = announcement.is_pinned ? '0 4px 20px rgba(250, 204, 21, 0.15)' : '0 4px 20px rgba(0, 0, 0, 0.08)';\n                },\n                children: [announcement.is_pinned && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    top: '-8px',\n                    right: '1rem',\n                    background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                    color: 'white',\n                    padding: '0.25rem 0.75rem',\n                    borderRadius: '12px',\n                    fontSize: '0.75rem',\n                    fontWeight: '600',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem',\n                    boxShadow: '0 2px 8px rgba(250, 204, 21, 0.3)'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Pin, {\n                    size: 12\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1533,\n                    columnNumber: 27\n                  }, this), \"Pinned\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1518,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    gap: '1rem',\n                    marginBottom: '1rem'\n                  },\n                  children: [(() => {\n                    if (announcement.is_alert) {\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '48px',\n                          height: '48px',\n                          background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                          borderRadius: '12px',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          flexShrink: 0\n                        },\n                        children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                          size: 24,\n                          color: \"white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1558,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1548,\n                        columnNumber: 31\n                      }, this);\n                    } else {\n                      const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                      const categoryStyle = getCategoryStyle(categoryName);\n                      const IconComponent = categoryStyle.icon;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '48px',\n                          height: '48px',\n                          background: categoryStyle.background,\n                          borderRadius: '12px',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          flexShrink: 0\n                        },\n                        children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                          size: 24,\n                          color: \"white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1577,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1567,\n                        columnNumber: 31\n                      }, this);\n                    }\n                  })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        marginBottom: '0.5rem',\n                        flexWrap: 'wrap'\n                      },\n                      children: [(() => {\n                        if (announcement.is_alert) {\n                          return /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                              color: 'white',\n                              fontSize: '0.75rem',\n                              fontWeight: '600',\n                              padding: '0.25rem 0.75rem',\n                              borderRadius: '20px',\n                              textTransform: 'uppercase',\n                              letterSpacing: '0.5px',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                              size: 12,\n                              color: \"white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1607,\n                              columnNumber: 37\n                            }, this), \"Alert\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1594,\n                            columnNumber: 35\n                          }, this);\n                        } else {\n                          const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                          const categoryStyle = getCategoryStyle(categoryName);\n                          const IconComponent = categoryStyle.icon;\n                          return /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              background: categoryStyle.background,\n                              color: 'white',\n                              fontSize: '0.75rem',\n                              fontWeight: '600',\n                              padding: '0.25rem 0.75rem',\n                              borderRadius: '20px',\n                              textTransform: 'uppercase',\n                              letterSpacing: '0.5px',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                              size: 12,\n                              color: \"white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1630,\n                              columnNumber: 37\n                            }, this), categoryName]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1617,\n                            columnNumber: 35\n                          }, this);\n                        }\n                      })(), announcement.grade_level && /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          background: 'rgba(59, 130, 246, 0.1)',\n                          color: '#3b82f6',\n                          fontSize: '0.75rem',\n                          fontWeight: '500',\n                          padding: '0.25rem 0.75rem',\n                          borderRadius: '20px'\n                        },\n                        children: [\"Grade \", announcement.grade_level]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1638,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          color: '#6b7280',\n                          fontSize: '0.875rem'\n                        },\n                        children: new Date(announcement.created_at).toLocaleDateString('en-US', {\n                          weekday: 'short',\n                          year: 'numeric',\n                          month: 'short',\n                          day: 'numeric'\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1650,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1584,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      style: {\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        color: '#1f2937',\n                        lineHeight: '1.3'\n                      },\n                      children: announcement.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1663,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1583,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      gap: '0.5rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => navigate(`/admin/posts?edit=${announcement.announcement_id}`),\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem',\n                        padding: '0.5rem 0.75rem',\n                        background: 'rgba(34, 197, 94, 0.1)',\n                        border: '1px solid rgba(34, 197, 94, 0.2)',\n                        borderRadius: '8px',\n                        color: '#22c55e',\n                        fontSize: '0.75rem',\n                        fontWeight: '500',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = 'rgba(34, 197, 94, 0.2)';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'rgba(34, 197, 94, 0.1)';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Edit, {\n                        size: 12\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1699,\n                        columnNumber: 29\n                      }, this), \"Edit\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1676,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem',\n                        padding: '0.5rem 0.75rem',\n                        background: 'rgba(59, 130, 246, 0.1)',\n                        border: '1px solid rgba(59, 130, 246, 0.2)',\n                        borderRadius: '8px',\n                        color: '#3b82f6',\n                        fontSize: '0.75rem',\n                        fontWeight: '500',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = 'rgba(59, 130, 246, 0.2)';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'rgba(59, 130, 246, 0.1)';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Eye, {\n                        size: 12\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1725,\n                        columnNumber: 29\n                      }, this), \"Analytics\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1703,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1675,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1539,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#4b5563',\n                    fontSize: '0.95rem',\n                    lineHeight: '1.6',\n                    marginBottom: '1rem'\n                  },\n                  children: announcement.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1732,\n                  columnNumber: 23\n                }, this), announcement.attachments && announcement.attachments.length > 0 && /*#__PURE__*/_jsxDEV(ImageGallery, {\n                  images: announcement.attachments,\n                  altPrefix: announcement.title,\n                  onImageClick: index => {\n                    // Could open image viewer modal\n                    console.log('View image:', index);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1743,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    padding: '1rem',\n                    background: 'rgba(0, 0, 0, 0.02)',\n                    borderRadius: '12px',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1.5rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleLikeToggle(announcement),\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        background: 'none',\n                        border: 'none',\n                        color: announcement.user_reaction ? '#ef4444' : '#6b7280',\n                        cursor: 'pointer',\n                        padding: '0.5rem',\n                        borderRadius: '8px',\n                        transition: 'all 0.2s ease',\n                        fontSize: '0.875rem',\n                        fontWeight: '500'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'none';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Heart, {\n                        size: 18,\n                        fill: announcement.user_reaction ? '#ef4444' : 'none'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1792,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: announcement.reaction_count || 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1796,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1769,\n                      columnNumber: 27\n                    }, this), announcement.allow_comments && /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setShowComments(showComments === announcement.announcement_id ? null : announcement.announcement_id),\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        background: 'none',\n                        border: 'none',\n                        color: showComments === announcement.announcement_id ? '#22c55e' : '#6b7280',\n                        cursor: 'pointer',\n                        padding: '0.5rem',\n                        borderRadius: '8px',\n                        transition: 'all 0.2s ease',\n                        fontSize: '0.875rem',\n                        fontWeight: '500'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                        e.currentTarget.style.color = '#22c55e';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'none';\n                        e.currentTarget.style.color = showComments === announcement.announcement_id ? '#22c55e' : '#6b7280';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n                        size: 18\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1828,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: announcement.comment_count || 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1829,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1801,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        color: '#6b7280',\n                        fontSize: '0.875rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Eye, {\n                        size: 18\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1841,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [announcement.view_count || 0, \" views\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1842,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1834,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1763,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1rem',\n                      fontSize: '0.75rem',\n                      color: '#6b7280'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Users, {\n                        size: 14\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1859,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [\"Posted by \", announcement.posted_by_name || 'Admin']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1860,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1854,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        padding: '0.25rem 0.5rem',\n                        background: announcement.status === 'published' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(107, 114, 128, 0.1)',\n                        color: announcement.status === 'published' ? '#22c55e' : '#6b7280',\n                        borderRadius: '6px',\n                        fontWeight: '500'\n                      },\n                      children: announcement.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1863,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1847,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1754,\n                  columnNumber: 23\n                }, this), showComments === announcement.announcement_id && announcement.allow_comments && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '1rem',\n                    paddingTop: '1rem',\n                    borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(AdminCommentSection, {\n                    announcementId: announcement.announcement_id,\n                    allowComments: announcement.allow_comments,\n                    currentUserType: \"admin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1884,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1879,\n                  columnNumber: 25\n                }, this)]\n              }, `announcement-${announcement.announcement_id}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1487,\n                columnNumber: 21\n              }, this))\n            }, void 0, false)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1251,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1009,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 839,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 608,\n      columnNumber: 7\n    }, this), selectedPinnedPost && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000,\n        padding: '2rem'\n      },\n      onClick: () => setSelectedPinnedPost(null),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: 'white',\n          borderRadius: '16px',\n          maxWidth: '600px',\n          width: '100%',\n          maxHeight: '80vh',\n          overflow: 'auto',\n          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n        },\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1.5rem',\n            borderBottom: '1px solid #e5e7eb',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.75rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Pin, {\n              size: 20,\n              style: {\n                color: '#22c55e'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1942,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: 0,\n                fontSize: '1.25rem',\n                fontWeight: '600',\n                color: '#111827'\n              },\n              children: \"Pinned Post\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1943,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1937,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedPinnedPost(null),\n            style: {\n              background: 'none',\n              border: 'none',\n              fontSize: '1.5rem',\n              color: '#6b7280',\n              cursor: 'pointer',\n              padding: '0.25rem',\n              borderRadius: '4px',\n              transition: 'color 0.2s ease'\n            },\n            onMouseEnter: e => {\n              e.currentTarget.style.color = '#374151';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.color = '#6b7280';\n            },\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1952,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1930,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.75rem',\n              marginBottom: '1rem'\n            },\n            children: [(() => {\n              const categoryName = (selectedPinnedPost.category_name || 'GENERAL').toUpperCase();\n              const categoryStyle = getCategoryStyle(categoryName);\n              const IconComponent = categoryStyle.icon;\n              return /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  background: categoryStyle.background,\n                  color: 'white',\n                  fontSize: '0.75rem',\n                  fontWeight: '600',\n                  padding: '0.25rem 0.75rem',\n                  borderRadius: '20px',\n                  textTransform: 'uppercase',\n                  letterSpacing: '0.5px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                  size: 12,\n                  color: \"white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2002,\n                  columnNumber: 23\n                }, this), categoryName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1989,\n                columnNumber: 21\n              }, this);\n            })(), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                color: 'white',\n                padding: '0.25rem 0.75rem',\n                borderRadius: '12px',\n                fontSize: '0.75rem',\n                fontWeight: '600',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Pin, {\n                size: 12\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2019,\n                columnNumber: 19\n              }, this), \"PINNED\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2008,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1977,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              margin: '0 0 1rem 0',\n              fontSize: '1.5rem',\n              fontWeight: '700',\n              color: '#111827',\n              lineHeight: '1.3'\n            },\n            children: selectedPinnedPost.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2024,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#4b5563',\n              fontSize: '1rem',\n              lineHeight: '1.6',\n              marginBottom: '1.5rem'\n            },\n            children: selectedPinnedPost.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2034,\n            columnNumber: 15\n          }, this), selectedPinnedPost.attachments && selectedPinnedPost.attachments.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(ImageGallery, {\n              images: selectedPinnedPost.attachments,\n              altPrefix: selectedPinnedPost.title,\n              onImageClick: index => {\n                console.log('View image:', index);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2046,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2045,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              fontSize: '0.875rem',\n              color: '#6b7280',\n              paddingTop: '1rem',\n              borderTop: '1px solid #e5e7eb'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2070,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Published: \", new Date(selectedPinnedPost.created_at).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2071,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2065,\n              columnNumber: 17\n            }, this), selectedPinnedPost.author_name && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"By: \", selectedPinnedPost.author_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2074,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2056,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1976,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1918,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1903,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 589,\n    columnNumber: 5\n  }, this);\n};\n_s2(AdminNewsfeed, \"8+4RQ1uA4qxrG/igueau42mrY0o=\", false, function () {\n  return [useNavigate, useCategories];\n});\n_c3 = AdminNewsfeed;\nexport default AdminNewsfeed;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ImageDisplay\");\n$RefreshReg$(_c2, \"ImageGallery\");\n$RefreshReg$(_c3, \"AdminNewsfeed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "announcementService", "useCategories", "AdminCommentSection", "FacebookImageGallery", "getImageUrl", "API_BASE_URL", "Newspaper", "Search", "<PERSON>n", "Calendar", "MessageSquare", "Heart", "Eye", "Edit", "Users", "LayoutDashboard", "BookOpen", "PartyPopper", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clock", "Trophy", "Briefcase", "GraduationCap", "Flag", "Coffee", "Plane", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ImageDisplay", "imagePath", "alt", "style", "onMouseEnter", "onMouseLeave", "_s", "imageError", "setImageError", "imageLoading", "setImageLoading", "handleImageError", "handleImageLoad", "display", "alignItems", "justifyContent", "backgroundColor", "color", "fontSize", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "top", "left", "right", "bottom", "src", "opacity", "transition", "onError", "onLoad", "_c", "ImageGallery", "images", "altPrefix", "onImageClick", "length", "visibleImages", "slice", "remainingCount", "Math", "max", "getContainerStyle", "index", "total", "baseStyle", "overflow", "borderRadius", "cursor", "width", "height", "getImageStyle", "objectFit", "gap", "marginBottom", "file_path", "e", "currentTarget", "transform", "onClick", "flexDirection", "map", "image", "idx", "actualIndex", "isLast", "fontWeight", "_c2", "AdminNewsfeed", "_s2", "navigate", "getCategoryStyle", "categoryName", "styles", "background", "icon", "getHolidayTypeStyle", "holidayTypeName", "filterCategory", "setFilterCategory", "filterGradeLevel", "setFilterGradeLevel", "searchTerm", "setSearchTerm", "showComments", "setShowComments", "newComment", "setNewComment", "submittingComment", "setSubmittingComment", "selectedPinnedPost", "setSelectedPinnedPost", "announcements", "setAnnouncements", "pinnedAnnouncements", "setPinnedAnnouncements", "calendarEvents", "setCalendarEvents", "loading", "setLoading", "error", "setError", "calendarLoading", "setCalendarLoading", "calendarError", "setCalendarError", "recentStudents", "setRecentStudents", "studentLoading", "setStudentLoading", "categories", "fetchPublishedAnnouncements", "undefined", "response", "fetch", "headers", "localStorage", "getItem", "data", "json", "success", "announcementsData", "pinned", "filter", "ann", "is_pinned", "err", "console", "message", "fetchCalendarEvents", "eventsData", "events", "eventsWithImages", "Promise", "all", "event", "imageResponse", "calendar_id", "imageData", "attachments", "imgErr", "warn", "fetchRecentStudents", "students", "handleLikeToggle", "announcement", "user_reaction", "removeReaction", "announcement_id", "method", "body", "JSON", "stringify", "reaction_type_id", "notify_admin", "ok", "Error", "handleCommentSubmit", "announcementId", "commentText", "trim", "content", "commenter_name", "commenter_email", "prev", "filteredAnnouncements", "_announcement$categor", "_announcement$grade_l", "matchesSearch", "title", "toLowerCase", "includes", "matchesCategory", "category_id", "toString", "matchesGradeLevel", "grade_level", "filteredCalendarEvents", "description", "today", "Date", "setHours", "eventDate", "event_date", "isEventDateValid", "isPublished", "is_published", "displayAnnouncements", "displayEvents", "combinedContent", "item", "type", "sortDate", "created_at", "sort", "a", "b", "getTime", "minHeight", "backgroundImage", "pointerEvents", "zIndex", "borderBottom", "boxShadow", "padding", "min<PERSON><PERSON><PERSON>", "margin", "lineHeight", "flex", "max<PERSON><PERSON><PERSON>", "size", "placeholder", "value", "onChange", "target", "border", "outline", "onFocus", "borderColor", "onBlur", "category", "name", "flexShrink", "category_name", "toUpperCase", "categoryStyle", "marginTop", "substring", "toLocaleDateString", "textAlign", "borderTop", "animation", "marginLeft", "marginRight", "<PERSON><PERSON>ilter", "student", "_student$profile", "_student$profile2", "_student$profile3", "profile", "first_name", "last_name", "student_number", "student_id", "holiday_type_name", "holidayStyle", "IconComponent", "textTransform", "letterSpacing", "weekday", "year", "month", "day", "eventImageUrls", "for<PERSON>ach", "img", "imageUrl", "push", "Boolean", "maxVisible", "log", "end_date", "is_alert", "flexWrap", "fill", "reaction_count", "allow_comments", "comment_count", "view_count", "posted_by_name", "status", "paddingTop", "allowComments", "currentUserType", "maxHeight", "stopPropagation", "author_name", "_c3", "$RefreshReg$"], "sources": ["D:/capstone-e-bulletin-reactjs-proj/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/AdminNewsfeed.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { announcementService } from '../../services';\nimport { useCategories } from '../../hooks/useAnnouncements';\nimport AdminCommentSection from '../../components/admin/AdminCommentSection';\nimport FacebookImageGallery from '../../components/common/FacebookImageGallery';\nimport type { AnnouncementAttachment } from '../../services/announcementService';\nimport type { CalendarEvent } from '../../types/calendar.types';\nimport { getImageUrl, API_BASE_URL } from '../../config/constants';\nimport {\n  Newspaper,\n  Search,\n  Pin,\n  Calendar,\n  MessageSquare,\n  Heart,\n  Eye,\n  Edit,\n  Users,\n  LayoutDashboard,\n  BookOpen,\n  PartyPopper,\n  AlertTriangle,\n  Clock,\n  Trophy,\n  Briefcase,\n  GraduationCap,\n  Flag,\n  Coffee,\n  Plane\n} from 'lucide-react';\n\n// Image Display Component\ninterface ImageDisplayProps {\n  imagePath: string;\n  alt: string;\n  style?: React.CSSProperties;\n  onMouseEnter?: (e: React.MouseEvent<HTMLImageElement>) => void;\n  onMouseLeave?: (e: React.MouseEvent<HTMLImageElement>) => void;\n}\n\nconst ImageDisplay: React.FC<ImageDisplayProps> = ({ \n  imagePath, \n  alt, \n  style, \n  onMouseEnter, \n  onMouseLeave \n}) => {\n  const [imageError, setImageError] = useState(false);\n  const [imageLoading, setImageLoading] = useState(true);\n\n  const handleImageError = () => {\n    setImageError(true);\n    setImageLoading(false);\n  };\n\n  const handleImageLoad = () => {\n    setImageLoading(false);\n  };\n\n  if (imageError) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f3f4f6',\n        color: '#6b7280',\n        fontSize: '0.875rem'\n      }}>\n        Image not available\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ position: 'relative', ...style }}>\n      {imageLoading && (\n        <div style={{\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          backgroundColor: '#f3f4f6',\n          color: '#6b7280'\n        }}>\n          Loading...\n        </div>\n      )}\n      <img\n        src={getImageUrl(imagePath || '') || ''}\n        alt={alt}\n        style={{\n          ...style,\n          opacity: imageLoading ? 0 : 1,\n          transition: 'opacity 0.3s ease'\n        }}\n        onError={handleImageError}\n        onLoad={handleImageLoad}\n        onMouseEnter={onMouseEnter}\n        onMouseLeave={onMouseLeave}\n      />\n    </div>\n  );\n};\n\n// Image Gallery Component\ninterface ImageGalleryProps {\n  images: AnnouncementAttachment[];\n  altPrefix: string;\n  onImageClick?: (index: number) => void;\n}\n\nconst ImageGallery: React.FC<ImageGalleryProps> = ({ images, altPrefix, onImageClick }) => {\n  if (!images || images.length === 0) return null;\n\n  const visibleImages = images.slice(0, 4);\n  const remainingCount = Math.max(0, images.length - 4);\n\n  const getContainerStyle = (index: number, total: number): React.CSSProperties => {\n    const baseStyle: React.CSSProperties = {\n      position: 'relative',\n      overflow: 'hidden',\n      borderRadius: '12px',\n      cursor: onImageClick ? 'pointer' : 'default'\n    };\n\n    if (total === 1) {\n      return { ...baseStyle, width: '100%', height: '300px' };\n    } else if (total === 2) {\n      return { ...baseStyle, width: '50%', height: '250px' };\n    } else if (total === 3) {\n      if (index === 0) {\n        return { ...baseStyle, width: '50%', height: '250px' };\n      } else {\n        return { ...baseStyle, width: '50%', height: '120px' };\n      }\n    } else {\n      if (index === 0) {\n        return { ...baseStyle, width: '50%', height: '250px' };\n      } else {\n        return { ...baseStyle, width: '33.33%', height: '120px' };\n      }\n    }\n  };\n\n  const getImageStyle = (index: number, total: number): React.CSSProperties => {\n    return {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover' as const,\n      transition: 'transform 0.3s ease'\n    };\n  };\n\n  return (\n    <div style={{\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1rem'\n    }}>\n      {/* Main image or left side */}\n      <div style={getContainerStyle(0, visibleImages.length)}>\n        <ImageDisplay\n          imagePath={visibleImages[0].file_path}\n          alt={`${altPrefix} - Image 1`}\n          style={getImageStyle(0, visibleImages.length)}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.transform = 'scale(1.02)';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.transform = 'scale(1)';\n          }}\n        />\n        {onImageClick && (\n          <div\n            style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            }}\n            onClick={() => onImageClick(0)}\n          />\n        )}\n      </div>\n\n      {/* Right side images */}\n      {visibleImages.length > 1 && (\n        <div style={{\n          display: 'flex',\n          flexDirection: visibleImages.length === 2 ? 'row' : 'column',\n          gap: '4px',\n          width: visibleImages.length === 2 ? '50%' : '50%'\n        }}>\n          {visibleImages.slice(1).map((image, idx) => {\n            const actualIndex = idx + 1;\n            const isLast = actualIndex === visibleImages.length - 1 && remainingCount > 0;\n            \n            return (\n              <div\n                key={actualIndex}\n                style={{\n                  ...getContainerStyle(actualIndex, visibleImages.length),\n                  position: 'relative'\n                }}\n              >\n                <ImageDisplay\n                  imagePath={image.file_path}\n                  alt={`${altPrefix} - Image ${actualIndex + 1}`}\n                  style={getImageStyle(actualIndex, visibleImages.length)}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'scale(1.02)';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'scale(1)';\n                  }}\n                />\n                \n                {/* Overlay for remaining images count */}\n                {isLast && remainingCount > 0 && (\n                  <div style={{\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: 'white',\n                    fontSize: '1.5rem',\n                    fontWeight: '600'\n                  }}>\n                    +{remainingCount}\n                  </div>\n                )}\n                \n                {onImageClick && (\n                  <div\n                    style={{\n                      position: 'absolute',\n                      top: 0,\n                      left: 0,\n                      right: 0,\n                      bottom: 0,\n                      cursor: 'pointer'\n                    }}\n                    onClick={() => onImageClick(actualIndex)}\n                  />\n                )}\n              </div>\n            );\n          })}\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Main AdminNewsfeed Component\nconst AdminNewsfeed: React.FC = () => {\n  const navigate = useNavigate();\n\n  // Category styling function\n  const getCategoryStyle = (categoryName: string) => {\n    const styles = {\n      'ACADEMIC': {\n        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n        icon: BookOpen\n      },\n      'GENERAL': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Users\n      },\n      'EVENTS': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: PartyPopper\n      },\n      'EMERGENCY': {\n        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n        icon: AlertTriangle\n      },\n      'SPORTS': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Trophy\n      },\n      'DEADLINES': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Clock\n      }\n    };\n\n    return styles[categoryName as keyof typeof styles] || styles['GENERAL'];\n  };\n\n  // Holiday type styling function\n  const getHolidayTypeStyle = (holidayTypeName: string) => {\n    const styles = {\n      'National Holiday': {\n        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n        icon: Flag\n      },\n      'School Event': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: GraduationCap\n      },\n      'Academic Break': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Coffee\n      },\n      'Sports Event': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Trophy\n      },\n      'Field Trip': {\n        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n        icon: Plane\n      },\n      'Meeting': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Briefcase\n      }\n    };\n\n    return styles[holidayTypeName as keyof typeof styles] || styles['School Event'];\n  };\n\n  // Filter states\n  const [filterCategory, setFilterCategory] = useState<string>('');\n  const [filterGradeLevel, setFilterGradeLevel] = useState<string>('');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // UI states\n  const [showComments, setShowComments] = useState<number | null>(null);\n  const [newComment, setNewComment] = useState<{ [key: number]: string }>({});\n  const [submittingComment, setSubmittingComment] = useState<number | null>(null);\n  const [selectedPinnedPost, setSelectedPinnedPost] = useState<any | null>(null);\n\n  // Data states\n  const [announcements, setAnnouncements] = useState<any[]>([]);\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState<any[]>([]);\n  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | undefined>();\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState<string | undefined>();\n  const [recentStudents, setRecentStudents] = useState<any[]>([]);\n  const [studentLoading, setStudentLoading] = useState(false);\n\n  const { categories } = useCategories();\n\n  // Fetch published announcements with images (admin can see all)\n  const fetchPublishedAnnouncements = async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n\n      const response = await fetch(`${API_BASE_URL}/api/announcements?status=published&page=1&limit=50&sort_by=created_at&sort_order=DESC`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        const announcementsData = data.data.announcements || [];\n\n        // The announcements already include attachments/images from the API\n        // No need to fetch images separately\n        setAnnouncements(announcementsData);\n\n        // Separate pinned announcements\n        const pinned = announcementsData.filter((ann: any) => ann.is_pinned === 1);\n        setPinnedAnnouncements(pinned);\n      } else {\n        setError('Failed to load announcements');\n      }\n    } catch (err: any) {\n      console.error('Error fetching announcements:', err);\n      setError(err.message || 'Failed to load announcements');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch calendar events\n  const fetchCalendarEvents = async () => {\n    try {\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n\n      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(\n          eventsData.map(async (event: any) => {\n            try {\n              const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`, {\n                headers: {\n                  'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n                  'Content-Type': 'application/json'\n                }\n              });\n              const imageData = await imageResponse.json();\n\n              if (imageData.success && imageData.data) {\n                event.images = imageData.data.attachments || [];\n              } else {\n                event.images = [];\n              }\n            } catch (imgErr) {\n              console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n              event.images = [];\n            }\n            return event;\n          })\n        );\n\n        setCalendarEvents(eventsWithImages);\n      } else {\n        setCalendarError('Failed to load calendar events');\n      }\n    } catch (err: any) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to load calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  };\n\n  // Fetch recent student registrations\n  const fetchRecentStudents = async () => {\n    try {\n      setStudentLoading(true);\n      const response = await fetch(`${API_BASE_URL}/api/students?page=1&limit=5&sort_by=created_at&sort_order=DESC`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        setRecentStudents(data.data.students || []);\n      }\n    } catch (err: any) {\n      console.error('Error fetching recent students:', err);\n    } finally {\n      setStudentLoading(false);\n    }\n  };\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchPublishedAnnouncements();\n    fetchCalendarEvents();\n    fetchRecentStudents();\n  }, []);\n\n  // Handle like/unlike functionality (admin perspective)\n  const handleLikeToggle = async (announcement: any) => {\n    try {\n      if (announcement.user_reaction) {\n        await announcementService.removeReaction(announcement.announcement_id);\n      } else {\n        const response = await fetch(`${API_BASE_URL}/api/announcements/${announcement.announcement_id}/reactions`, {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            reaction_type_id: 1,\n            notify_admin: false // Admin doesn't need to notify themselves\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error('Failed to add reaction');\n        }\n      }\n\n      // Refresh data\n      await fetchPublishedAnnouncements();\n    } catch (error) {\n      console.error('Error toggling like:', error);\n    }\n  };\n\n  // Handle comment submission (admin perspective)\n  const handleCommentSubmit = async (announcementId: number, commentText: string) => {\n    if (!commentText.trim()) return;\n\n    try {\n      setSubmittingComment(announcementId);\n\n      const response = await fetch(`${API_BASE_URL}/api/admin/announcements/${announcementId}/comments`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          content: commentText,\n          commenter_name: 'Admin User',\n          commenter_email: '<EMAIL>',\n          notify_admin: false\n        })\n      });\n\n      if (response.ok) {\n        setNewComment(prev => ({ ...prev, [announcementId]: '' }));\n        await fetchPublishedAnnouncements();\n      } else {\n        console.error('Failed to submit comment');\n      }\n    } catch (error) {\n      console.error('Error submitting comment:', error);\n    } finally {\n      setSubmittingComment(null);\n    }\n  };\n\n  // Filter announcements\n  const filteredAnnouncements = announcements.filter(announcement => {\n    const matchesSearch = !searchTerm ||\n      announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      announcement.content.toLowerCase().includes(searchTerm.toLowerCase());\n\n    const matchesCategory = !filterCategory ||\n      announcement.category_id?.toString() === filterCategory;\n\n    const matchesGradeLevel = !filterGradeLevel ||\n      announcement.grade_level?.toString() === filterGradeLevel;\n\n    return matchesSearch && matchesCategory && matchesGradeLevel;\n  });\n\n  // Filter calendar events with date-based filtering\n  const filteredCalendarEvents = calendarEvents.filter(event => {\n    const matchesSearch = !searchTerm ||\n      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      (event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    // Only show events that are published and on or after today's date\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    const eventDate = new Date(event.event_date);\n    eventDate.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    const isEventDateValid = eventDate >= today;\n    const isPublished = (event as any).is_published === 1;\n\n    return matchesSearch && isEventDateValid && isPublished;\n  });\n\n  // Combine and sort all content by date (most recent first)\n  const displayAnnouncements = filteredAnnouncements;\n  const displayEvents = filteredCalendarEvents;\n\n  // Create combined content array for better chronological display\n  const combinedContent = [\n    ...displayAnnouncements.map(item => ({ ...item, type: 'announcement', sortDate: new Date(item.created_at) })),\n    ...displayEvents.map(item => ({ ...item, type: 'event', sortDate: new Date(item.event_date) }))\n  ].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)',\n      position: 'relative'\n    }}>\n      {/* Background Pattern */}\n      <div style={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundImage: `\n          radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.03) 0%, transparent 50%),\n          radial-gradient(circle at 75% 75%, rgba(250, 204, 21, 0.03) 0%, transparent 50%)\n        `,\n        pointerEvents: 'none'\n      }} />\n\n      <div style={{ position: 'relative', zIndex: 1 }}>\n        {/* Modern Admin Header */}\n        <header style={{\n          background: 'white',\n          borderBottom: '1px solid #e5e7eb',\n          position: 'sticky',\n          top: 0,\n          zIndex: 100,\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n        }}>\n          <div style={{\n            padding: '0 2rem',\n            height: '72px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          }}>\n            {/* Left Section: Logo + Page Title */}\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1.5rem',\n              minWidth: '300px'\n            }}>\n              <img\n                src=\"/logo/vcba1.png\"\n                alt=\"VCBA Logo\"\n                style={{\n                  width: '48px',\n                  height: '48px',\n                  objectFit: 'contain'\n                }}\n              />\n              <div>\n                <h1 style={{\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827',\n                  lineHeight: '1.2'\n                }}>\n                  VCBA E-Bulletin Board\n                </h1>\n                <p style={{\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280',\n                  lineHeight: '1.2'\n                }}>\n                  Admin Newsfeed\n                </p>\n              </div>\n            </div>\n\n            {/* Center Section: Search */}\n            <div style={{\n              flex: 1,\n              maxWidth: '500px',\n              margin: '0 2rem'\n            }}>\n              <div style={{ position: 'relative' }}>\n                <Search\n                  size={20}\n                  style={{\n                    position: 'absolute',\n                    left: '1rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: '#9ca3af'\n                  }}\n                />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search announcements and events...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  style={{\n                    width: '100%',\n                    height: '44px',\n                    padding: '0 1rem 0 3rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '12px',\n                    background: '#f9fafb',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onFocus={(e) => {\n                    e.currentTarget.style.borderColor = '#22c55e';\n                    e.currentTarget.style.background = 'white';\n                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n                  }}\n                  onBlur={(e) => {\n                    e.currentTarget.style.borderColor = '#d1d5db';\n                    e.currentTarget.style.background = '#f9fafb';\n                    e.currentTarget.style.boxShadow = 'none';\n                  }}\n                />\n              </div>\n            </div>\n\n            {/* Right Section: Navigation + Filters */}\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              minWidth: '400px',\n              justifyContent: 'flex-end'\n            }}>\n              \n              {/* Filters Group */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.5rem',\n                background: '#f9fafb',\n                borderRadius: '12px',\n                border: '1px solid #e5e7eb'\n              }}>\n                <select\n                  value={filterCategory}\n                  onChange={(e) => setFilterCategory(e.target.value)}\n                  style={{\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '110px'\n                  }}\n                >\n                  <option value=\"\">All Categories</option>\n                  {categories.map(category => (\n                    <option key={category.category_id} value={category.category_id.toString()}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n\n                <select\n                  value={filterGradeLevel}\n                  onChange={(e) => setFilterGradeLevel(e.target.value)}\n                  style={{\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '100px'\n                  }}\n                >\n                  <option value=\"\">All Grades</option>\n                  <option value=\"11\">Grade 11</option>\n                  <option value=\"12\">Grade 12</option>\n                </select>\n\n                {(searchTerm || filterCategory || filterGradeLevel) && (\n                  <button\n                    onClick={() => {\n                      setSearchTerm('');\n                      setFilterCategory('');\n                      setFilterGradeLevel('');\n                    }}\n                    style={{\n                      padding: '0.5rem 0.75rem',\n                      border: 'none',\n                      borderRadius: '8px',\n                      background: '#ef4444',\n                      color: 'white',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.background = '#dc2626';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.background = '#ef4444';\n                    }}\n                  >\n                    Clear\n                  </button>\n                )}\n              </div>\n\n              {/* Back to Dashboard Button */}\n              <button\n                onClick={() => navigate('/admin/dashboard')}\n                style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  padding: '0.75rem 1rem',\n                  background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                  border: 'none',\n                  borderRadius: '12px',\n                  color: 'white',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease',\n                  boxShadow: '0 2px 8px rgba(34, 197, 94, 0.2)'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.2)';\n                }}\n              >\n                <LayoutDashboard size={16} />\n                Dashboard\n              </button>\n            </div>\n          </div>\n        </header>\n\n\n\n        {/* Main Content Layout */}\n        <div style={{\n          padding: '2rem',\n          display: 'flex',\n          gap: '2rem',\n          alignItems: 'flex-start'\n        }}>\n          {/* Left Sidebar: Pinned Posts */}\n          <div style={{\n            width: '320px',\n            flexShrink: 0\n          }}>\n            <div style={{\n              background: 'white',\n              borderRadius: '16px',\n              border: '1px solid #e5e7eb',\n              overflow: 'hidden',\n              position: 'sticky',\n              top: '100px'\n            }}>\n              {/* Pinned Posts Header */}\n              <div style={{\n                padding: '1.5rem 1.5rem 1rem',\n                borderBottom: '1px solid #f3f4f6'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.75rem',\n                  marginBottom: '0.5rem'\n                }}>\n                  <Pin size={20} style={{ color: '#22c55e' }} />\n                  <h3 style={{\n                    margin: 0,\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#111827'\n                  }}>\n                    Pinned Posts\n                  </h3>\n                </div>\n                <p style={{\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                }}>\n                  Important announcements and updates\n                </p>\n              </div>\n\n              {/* Pinned Posts List */}\n              <div style={{ padding: '1rem' }}>\n                {pinnedAnnouncements.length > 0 ? (\n                  <>\n                    {pinnedAnnouncements.slice(0, 3).map((announcement, index) => {\n                      const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                      const categoryStyle = getCategoryStyle(categoryName);\n\n                      return (\n                        <div\n                          key={announcement.announcement_id}\n                          style={{\n                            padding: '1rem',\n                            background: '#f8fafc',\n                            borderRadius: '12px',\n                            border: '1px solid #e2e8f0',\n                            marginBottom: '1rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.2s ease'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.background = '#f1f5f9';\n                            e.currentTarget.style.borderColor = '#22c55e';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.background = '#f8fafc';\n                            e.currentTarget.style.borderColor = '#e2e8f0';\n                          }}\n                          onClick={() => setSelectedPinnedPost(announcement)}\n                        >\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'flex-start',\n                            gap: '0.75rem'\n                          }}>\n                            <div style={{\n                              width: '8px',\n                              height: '8px',\n                              background: categoryStyle.background.includes('#ef4444') ? '#ef4444' : '#22c55e',\n                              borderRadius: '50%',\n                              marginTop: '0.5rem',\n                              flexShrink: 0\n                            }} />\n                            <div style={{ flex: 1 }}>\n                              <h4 style={{\n                                margin: '0 0 0.5rem 0',\n                                fontSize: '0.875rem',\n                                fontWeight: '600',\n                                color: '#111827',\n                                lineHeight: '1.4'\n                              }}>\n                                {announcement.title}\n                              </h4>\n                              <p style={{\n                                margin: '0 0 0.5rem 0',\n                                fontSize: '0.8rem',\n                                color: '#6b7280',\n                                lineHeight: '1.4'\n                              }}>\n                                {announcement.content.length > 80\n                                  ? `${announcement.content.substring(0, 80)}...`\n                                  : announcement.content}\n                              </p>\n                              <div style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                fontSize: '0.75rem',\n                                color: '#9ca3af'\n                              }}>\n                                <Calendar size={12} />\n                                <span>{new Date(announcement.created_at).toLocaleDateString()}</span>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      );\n                    })}\n\n                    {pinnedAnnouncements.length > 3 && (\n                      <button style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #e5e7eb',\n                        borderRadius: '8px',\n                        background: 'white',\n                        color: '#22c55e',\n                        fontSize: '0.875rem',\n                        fontWeight: '500',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.background = '#f0fdf4';\n                        e.currentTarget.style.borderColor = '#22c55e';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.background = 'white';\n                        e.currentTarget.style.borderColor = '#e5e7eb';\n                      }}>\n                        View All {pinnedAnnouncements.length} Pinned Posts\n                      </button>\n                    )}\n                  </>\n                ) : (\n                  <div style={{\n                    padding: '2rem 1rem',\n                    textAlign: 'center',\n                    color: '#6b7280'\n                  }}>\n                    <Pin size={24} style={{ marginBottom: '0.5rem', opacity: 0.5 }} />\n                    <p style={{ margin: 0, fontSize: '0.875rem' }}>\n                      No pinned posts available\n                    </p>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Right Content: Main Feed */}\n          <div style={{ flex: 1, minWidth: 0 }}>\n          {/* Loading State */}\n          {(loading || calendarLoading) && (\n            <div style={{\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              minHeight: '400px'\n            }}>\n              <div style={{\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                gap: '1rem'\n              }}>\n                <div style={{\n                  width: '3rem',\n                  height: '3rem',\n                  border: '4px solid rgba(34, 197, 94, 0.2)',\n                  borderTop: '4px solid #22c55e',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }}></div>\n                <p style={{\n                  color: '#6b7280',\n                  fontSize: '1rem',\n                  fontWeight: '500'\n                }}>\n                  Loading content...\n                </p>\n              </div>\n            </div>\n          )}\n\n          {/* Error State */}\n          {(error || calendarError) && !loading && !calendarLoading && (\n            <div style={{\n              padding: '2rem',\n              background: 'rgba(239, 68, 68, 0.1)',\n              border: '1px solid rgba(239, 68, 68, 0.2)',\n              borderRadius: '16px',\n              textAlign: 'center'\n            }}>\n              <div style={{\n                width: '4rem',\n                height: '4rem',\n                background: 'rgba(239, 68, 68, 0.1)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 1rem'\n              }}>\n                <MessageSquare size={24} color=\"#ef4444\" />\n              </div>\n              <h3 style={{\n                color: '#ef4444',\n                margin: '0 0 0.5rem 0',\n                fontSize: '1.25rem',\n                fontWeight: '600'\n              }}>\n                Error Loading Content\n              </h3>\n              <p style={{\n                color: '#6b7280',\n                margin: '0 0 1.5rem 0',\n                fontSize: '1rem'\n              }}>\n                {error || calendarError}\n              </p>\n              <button\n                onClick={() => {\n                  fetchPublishedAnnouncements();\n                  fetchCalendarEvents();\n                }}\n                style={{\n                  background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '12px',\n                  padding: '0.75rem 1.5rem',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = 'none';\n                }}\n              >\n                Try Again\n              </button>\n            </div>\n          )}\n\n          {/* Empty State */}\n          {!loading && !calendarLoading && !error && !calendarError &&\n           displayAnnouncements.length === 0 && displayEvents.length === 0 && (\n            <div style={{\n              padding: '4rem 2rem',\n              textAlign: 'center'\n            }}>\n              <div style={{\n                width: '5rem',\n                height: '5rem',\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 2rem'\n              }}>\n                <Newspaper size={32} color=\"white\" />\n              </div>\n              <h3 style={{\n                color: '#374151',\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '600'\n              }}>\n                No Content Available\n              </h3>\n              <p style={{\n                color: '#6b7280',\n                margin: '0 0 2rem 0',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                maxWidth: '500px',\n                marginLeft: 'auto',\n                marginRight: 'auto'\n              }}>\n                {searchTerm || filterCategory || filterGradeLevel\n                  ? 'No content matches your current filters. Try adjusting your search criteria.'\n                  : 'There are no published announcements or events at the moment. Check back later for updates.'\n                }\n              </p>\n              {(searchTerm || filterCategory || filterGradeLevel) && (\n                <button\n                  onClick={() => {\n                    setSearchTerm('');\n                    setFilterCategory('');\n                    setFilterGradeLevel('');\n                  }}\n                  style={{\n                    background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '12px',\n                    padding: '0.75rem 1.5rem',\n                    fontSize: '0.875rem',\n                    fontWeight: '600',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'translateY(-1px)';\n                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = 'none';\n                  }}\n                >\n                  Clear Filters\n                </button>\n              )}\n            </div>\n          )}\n\n          {/* Recent Students Section (Admin Only) */}\n          {!studentLoading && recentStudents.length > 0 && (\n            <div style={{\n              background: 'rgba(255, 255, 255, 0.95)',\n              borderRadius: '16px',\n              padding: '1.5rem',\n              border: '1px solid rgba(0, 0, 0, 0.1)',\n              backdropFilter: 'blur(10px)',\n              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n              marginBottom: '1.5rem'\n            }}>\n              <h3 style={{\n                fontSize: '1.25rem',\n                fontWeight: '600',\n                color: '#2d5016',\n                margin: '0 0 1rem 0',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}>\n                👥 Recent Student Registrations\n              </h3>\n              <div style={{\n                display: 'grid',\n                gap: '0.75rem'\n              }}>\n                {recentStudents.slice(0, 3).map((student: any) => (\n                  <div\n                    key={student.student_id}\n                    style={{\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center',\n                      padding: '0.75rem',\n                      backgroundColor: '#f8fdf8',\n                      borderRadius: '8px',\n                      border: '1px solid #e8f5e8'\n                    }}\n                  >\n                    <div>\n                      <div style={{\n                        fontWeight: '500',\n                        color: '#2d5016',\n                        fontSize: '0.875rem'\n                      }}>\n                        {student.profile?.first_name} {student.profile?.last_name}\n                      </div>\n                      <div style={{\n                        fontSize: '0.75rem',\n                        color: '#6b7280'\n                      }}>\n                        Grade {student.profile?.grade_level} • {student.student_number}\n                      </div>\n                    </div>\n                    <div style={{\n                      fontSize: '0.75rem',\n                      color: '#6b7280'\n                    }}>\n                      {new Date(student.created_at).toLocaleDateString()}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* Content Feed */}\n          {!loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && (\n            <div style={{\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem'\n            }}>\n              {/* Calendar Events */}\n              {displayEvents.length > 0 && (\n                <>\n                  {displayEvents.map(event => (\n                    <div\n                      key={`event-${event.calendar_id}`}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Event Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'flex-start',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        <div style={{\n                          width: '48px',\n                          height: '48px',\n                          background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                          borderRadius: '12px',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          flexShrink: 0\n                        }}>\n                          <Calendar size={24} color=\"white\" />\n                        </div>\n\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            marginBottom: '0.5rem'\n                          }}>\n                            {(() => {\n                              const holidayTypeName = event.holiday_type_name || 'School Event';\n                              const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                              const IconComponent = holidayStyle.icon;\n\n                              return (\n                                <span style={{\n                                  background: holidayStyle.background,\n                                  color: 'white',\n                                  fontSize: '0.75rem',\n                                  fontWeight: '600',\n                                  padding: '0.25rem 0.75rem',\n                                  borderRadius: '20px',\n                                  textTransform: 'uppercase',\n                                  letterSpacing: '0.5px',\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: '0.25rem'\n                                }}>\n                                  <IconComponent size={12} color=\"white\" />\n                                  {holidayTypeName}\n                                </span>\n                              );\n                            })()}\n\n                            <div style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem',\n                              color: '#6b7280',\n                              fontSize: '0.875rem'\n                            }}>\n                              <Calendar size={14} />\n                              {new Date(event.event_date).toLocaleDateString('en-US', {\n                                weekday: 'long',\n                                year: 'numeric',\n                                month: 'long',\n                                day: 'numeric'\n                              })}\n                            </div>\n                          </div>\n\n                          <h3 style={{\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '1.25rem',\n                            fontWeight: '700',\n                            color: '#1f2937',\n                            lineHeight: '1.3'\n                          }}>\n                            {event.title}\n                          </h3>\n                        </div>\n\n                        {/* Admin Event Actions */}\n                        <div style={{ display: 'flex', gap: '0.5rem' }}>\n                          <button\n                            onClick={() => navigate(`/admin/calendar?event=${event.calendar_id}`)}\n                            style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem',\n                              padding: '0.5rem 0.75rem',\n                              background: 'rgba(59, 130, 246, 0.1)',\n                              border: '1px solid rgba(59, 130, 246, 0.2)',\n                              borderRadius: '8px',\n                              color: '#3b82f6',\n                              fontSize: '0.75rem',\n                              fontWeight: '500',\n                              cursor: 'pointer',\n                              transition: 'all 0.2s ease'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = 'rgba(59, 130, 246, 0.2)';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'rgba(59, 130, 246, 0.1)';\n                            }}\n                          >\n                            <Edit size={12} />\n                            Edit\n                          </button>\n                        </div>\n                      </div>\n\n                      {/* Event Content */}\n                      {event.description && (\n                        <div style={{\n                          color: '#4b5563',\n                          fontSize: '0.95rem',\n                          lineHeight: '1.6',\n                          marginBottom: '1rem'\n                        }}>\n                          {event.description}\n                        </div>\n                      )}\n\n                      {/* Event Images */}\n                      {(() => {\n                        // Get event images if they exist\n                        const eventImageUrls: string[] = [];\n\n                        if ((event as any).images && (event as any).images.length > 0) {\n                          (event as any).images.forEach((img: any) => {\n                            if (img.file_path) {\n                              // Convert file_path to full URL\n                              const imageUrl = getImageUrl(img.file_path);\n                              if (imageUrl) {\n                                eventImageUrls.push(imageUrl);\n                              }\n                            }\n                          });\n                        }\n\n                        return eventImageUrls.length > 0 ? (\n                          <div style={{ marginBottom: '1rem' }}>\n                            <FacebookImageGallery\n                              images={eventImageUrls.filter(Boolean) as string[]}\n                              altPrefix={event.title}\n                              maxVisible={4}\n                              onImageClick={(index) => {\n                                console.log(`Clicked image ${index + 1} for event: ${event.title}`);\n                              }}\n                            />\n                          </div>\n                        ) : null;\n                      })()}\n\n                      {/* Event Details */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1.5rem',\n                        padding: '1rem',\n                        background: 'rgba(59, 130, 246, 0.05)',\n                        borderRadius: '12px',\n                        fontSize: '0.875rem'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          color: '#6b7280'\n                        }}>\n                          <Calendar size={16} />\n                          <span>\n                            {event.end_date && event.end_date !== event.event_date\n                              ? `${new Date(event.event_date).toLocaleDateString()} - ${new Date(event.end_date).toLocaleDateString()}`\n                              : new Date(event.event_date).toLocaleDateString()\n                            }\n                          </span>\n                        </div>\n\n                        {event.holiday_type_name && (\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            color: '#6b7280'\n                          }}>\n                            <span style={{\n                              padding: '0.25rem 0.5rem',\n                              background: 'rgba(59, 130, 246, 0.1)',\n                              borderRadius: '6px',\n                              fontSize: '0.75rem',\n                              fontWeight: '500'\n                            }}>\n                              {event.holiday_type_name}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </>\n              )}\n\n              {/* Announcements */}\n              {displayAnnouncements.length > 0 && (\n                <>\n                  {displayAnnouncements.map(announcement => (\n                    <div\n                      key={`announcement-${announcement.announcement_id}`}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: announcement.is_pinned\n                          ? '2px solid rgba(250, 204, 21, 0.3)'\n                          : '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: announcement.is_pinned\n                          ? '0 4px 20px rgba(250, 204, 21, 0.15)'\n                          : '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease',\n                        position: 'relative'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = announcement.is_pinned\n                          ? '0 8px 30px rgba(250, 204, 21, 0.25)'\n                          : '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = announcement.is_pinned\n                          ? '0 4px 20px rgba(250, 204, 21, 0.15)'\n                          : '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Pinned Badge */}\n                      {announcement.is_pinned && (\n                        <div style={{\n                          position: 'absolute',\n                          top: '-8px',\n                          right: '1rem',\n                          background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                          color: 'white',\n                          padding: '0.25rem 0.75rem',\n                          borderRadius: '12px',\n                          fontSize: '0.75rem',\n                          fontWeight: '600',\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.25rem',\n                          boxShadow: '0 2px 8px rgba(250, 204, 21, 0.3)'\n                        }}>\n                          <Pin size={12} />\n                          Pinned\n                        </div>\n                      )}\n\n                      {/* Announcement Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'flex-start',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        {(() => {\n                          if (announcement.is_alert) {\n                            return (\n                              <div style={{\n                                width: '48px',\n                                height: '48px',\n                                background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                                borderRadius: '12px',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                flexShrink: 0\n                              }}>\n                                <AlertTriangle size={24} color=\"white\" />\n                              </div>\n                            );\n                          } else {\n                            const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                            const categoryStyle = getCategoryStyle(categoryName);\n                            const IconComponent = categoryStyle.icon;\n\n                            return (\n                              <div style={{\n                                width: '48px',\n                                height: '48px',\n                                background: categoryStyle.background,\n                                borderRadius: '12px',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                flexShrink: 0\n                              }}>\n                                <IconComponent size={24} color=\"white\" />\n                              </div>\n                            );\n                          }\n                        })()}\n\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            marginBottom: '0.5rem',\n                            flexWrap: 'wrap'\n                          }}>\n                            {(() => {\n                              if (announcement.is_alert) {\n                                return (\n                                  <span style={{\n                                    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                                    color: 'white',\n                                    fontSize: '0.75rem',\n                                    fontWeight: '600',\n                                    padding: '0.25rem 0.75rem',\n                                    borderRadius: '20px',\n                                    textTransform: 'uppercase',\n                                    letterSpacing: '0.5px',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.25rem'\n                                  }}>\n                                    <AlertTriangle size={12} color=\"white\" />\n                                    Alert\n                                  </span>\n                                );\n                              } else {\n                                const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                                const categoryStyle = getCategoryStyle(categoryName);\n                                const IconComponent = categoryStyle.icon;\n\n                                return (\n                                  <span style={{\n                                    background: categoryStyle.background,\n                                    color: 'white',\n                                    fontSize: '0.75rem',\n                                    fontWeight: '600',\n                                    padding: '0.25rem 0.75rem',\n                                    borderRadius: '20px',\n                                    textTransform: 'uppercase',\n                                    letterSpacing: '0.5px',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.25rem'\n                                  }}>\n                                    <IconComponent size={12} color=\"white\" />\n                                    {categoryName}\n                                  </span>\n                                );\n                              }\n                            })()}\n\n                            {announcement.grade_level && (\n                              <span style={{\n                                background: 'rgba(59, 130, 246, 0.1)',\n                                color: '#3b82f6',\n                                fontSize: '0.75rem',\n                                fontWeight: '500',\n                                padding: '0.25rem 0.75rem',\n                                borderRadius: '20px'\n                              }}>\n                                Grade {announcement.grade_level}\n                              </span>\n                            )}\n\n                            <div style={{\n                              color: '#6b7280',\n                              fontSize: '0.875rem'\n                            }}>\n                              {new Date(announcement.created_at).toLocaleDateString('en-US', {\n                                weekday: 'short',\n                                year: 'numeric',\n                                month: 'short',\n                                day: 'numeric'\n                              })}\n                            </div>\n                          </div>\n\n                          <h3 style={{\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '1.25rem',\n                            fontWeight: '700',\n                            color: '#1f2937',\n                            lineHeight: '1.3'\n                          }}>\n                            {announcement.title}\n                          </h3>\n                        </div>\n\n                        {/* Admin Announcement Actions */}\n                        <div style={{ display: 'flex', gap: '0.5rem' }}>\n                          <button\n                            onClick={() => navigate(`/admin/posts?edit=${announcement.announcement_id}`)}\n                            style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem',\n                              padding: '0.5rem 0.75rem',\n                              background: 'rgba(34, 197, 94, 0.1)',\n                              border: '1px solid rgba(34, 197, 94, 0.2)',\n                              borderRadius: '8px',\n                              color: '#22c55e',\n                              fontSize: '0.75rem',\n                              fontWeight: '500',\n                              cursor: 'pointer',\n                              transition: 'all 0.2s ease'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = 'rgba(34, 197, 94, 0.2)';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'rgba(34, 197, 94, 0.1)';\n                            }}\n                          >\n                            <Edit size={12} />\n                            Edit\n                          </button>\n\n                          <button\n                            style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem',\n                              padding: '0.5rem 0.75rem',\n                              background: 'rgba(59, 130, 246, 0.1)',\n                              border: '1px solid rgba(59, 130, 246, 0.2)',\n                              borderRadius: '8px',\n                              color: '#3b82f6',\n                              fontSize: '0.75rem',\n                              fontWeight: '500',\n                              cursor: 'pointer',\n                              transition: 'all 0.2s ease'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = 'rgba(59, 130, 246, 0.2)';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'rgba(59, 130, 246, 0.1)';\n                            }}\n                          >\n                            <Eye size={12} />\n                            Analytics\n                          </button>\n                        </div>\n                      </div>\n\n                      {/* Announcement Content */}\n                      <div style={{\n                        color: '#4b5563',\n                        fontSize: '0.95rem',\n                        lineHeight: '1.6',\n                        marginBottom: '1rem'\n                      }}>\n                        {announcement.content}\n                      </div>\n\n                      {/* Images */}\n                      {announcement.attachments && announcement.attachments.length > 0 && (\n                        <ImageGallery\n                          images={announcement.attachments}\n                          altPrefix={announcement.title}\n                          onImageClick={(index) => {\n                            // Could open image viewer modal\n                            console.log('View image:', index);\n                          }}\n                        />\n                      )}\n\n                      {/* Announcement Stats & Actions */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        padding: '1rem',\n                        background: 'rgba(0, 0, 0, 0.02)',\n                        borderRadius: '12px',\n                        marginBottom: '1rem'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1.5rem'\n                        }}>\n                          {/* Like Button */}\n                          <button\n                            onClick={() => handleLikeToggle(announcement)}\n                            style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.5rem',\n                              background: 'none',\n                              border: 'none',\n                              color: announcement.user_reaction ? '#ef4444' : '#6b7280',\n                              cursor: 'pointer',\n                              padding: '0.5rem',\n                              borderRadius: '8px',\n                              transition: 'all 0.2s ease',\n                              fontSize: '0.875rem',\n                              fontWeight: '500'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'none';\n                            }}\n                          >\n                            <Heart\n                              size={18}\n                              fill={announcement.user_reaction ? '#ef4444' : 'none'}\n                            />\n                            <span>{announcement.reaction_count || 0}</span>\n                          </button>\n\n                          {/* Comments Button */}\n                          {announcement.allow_comments && (\n                            <button\n                              onClick={() => setShowComments(\n                                showComments === announcement.announcement_id ? null : announcement.announcement_id\n                              )}\n                              style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                background: 'none',\n                                border: 'none',\n                                color: showComments === announcement.announcement_id ? '#22c55e' : '#6b7280',\n                                cursor: 'pointer',\n                                padding: '0.5rem',\n                                borderRadius: '8px',\n                                transition: 'all 0.2s ease',\n                                fontSize: '0.875rem',\n                                fontWeight: '500'\n                              }}\n                              onMouseEnter={(e) => {\n                                e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                                e.currentTarget.style.color = '#22c55e';\n                              }}\n                              onMouseLeave={(e) => {\n                                e.currentTarget.style.background = 'none';\n                                e.currentTarget.style.color = showComments === announcement.announcement_id ? '#22c55e' : '#6b7280';\n                              }}\n                            >\n                              <MessageSquare size={18} />\n                              <span>{announcement.comment_count || 0}</span>\n                            </button>\n                          )}\n\n                          {/* Views Count */}\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            color: '#6b7280',\n                            fontSize: '0.875rem'\n                          }}>\n                            <Eye size={18} />\n                            <span>{announcement.view_count || 0} views</span>\n                          </div>\n                        </div>\n\n                        {/* Admin Stats */}\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem',\n                          fontSize: '0.75rem',\n                          color: '#6b7280'\n                        }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.25rem'\n                          }}>\n                            <Users size={14} />\n                            <span>Posted by {announcement.posted_by_name || 'Admin'}</span>\n                          </div>\n\n                          <div style={{\n                            padding: '0.25rem 0.5rem',\n                            background: announcement.status === 'published'\n                              ? 'rgba(34, 197, 94, 0.1)'\n                              : 'rgba(107, 114, 128, 0.1)',\n                            color: announcement.status === 'published' ? '#22c55e' : '#6b7280',\n                            borderRadius: '6px',\n                            fontWeight: '500'\n                          }}>\n                            {announcement.status}\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Comments Section */}\n                      {showComments === announcement.announcement_id && announcement.allow_comments && (\n                        <div style={{\n                          marginTop: '1rem',\n                          paddingTop: '1rem',\n                          borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                        }}>\n                          <AdminCommentSection\n                            announcementId={announcement.announcement_id}\n                            allowComments={announcement.allow_comments}\n                            currentUserType=\"admin\"\n                          />\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </>\n              )}\n            </div>\n          )}\n          </div>\n        </div>\n      </div>\n\n      {/* Pinned Post Dialog */}\n      {selectedPinnedPost && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000,\n          padding: '2rem'\n        }}\n        onClick={() => setSelectedPinnedPost(null)}\n        >\n          <div style={{\n            backgroundColor: 'white',\n            borderRadius: '16px',\n            maxWidth: '600px',\n            width: '100%',\n            maxHeight: '80vh',\n            overflow: 'auto',\n            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n          }}\n          onClick={(e) => e.stopPropagation()}\n          >\n            {/* Dialog Header */}\n            <div style={{\n              padding: '1.5rem',\n              borderBottom: '1px solid #e5e7eb',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem'\n              }}>\n                <Pin size={20} style={{ color: '#22c55e' }} />\n                <h3 style={{\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827'\n                }}>\n                  Pinned Post\n                </h3>\n              </div>\n              <button\n                onClick={() => setSelectedPinnedPost(null)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  fontSize: '1.5rem',\n                  color: '#6b7280',\n                  cursor: 'pointer',\n                  padding: '0.25rem',\n                  borderRadius: '4px',\n                  transition: 'color 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.color = '#374151';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.color = '#6b7280';\n                }}\n              >\n                ×\n              </button>\n            </div>\n\n            {/* Dialog Content */}\n            <div style={{ padding: '1.5rem' }}>\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem',\n                marginBottom: '1rem'\n              }}>\n                {(() => {\n                  const categoryName = (selectedPinnedPost.category_name || 'GENERAL').toUpperCase();\n                  const categoryStyle = getCategoryStyle(categoryName);\n                  const IconComponent = categoryStyle.icon;\n\n                  return (\n                    <span style={{\n                      background: categoryStyle.background,\n                      color: 'white',\n                      fontSize: '0.75rem',\n                      fontWeight: '600',\n                      padding: '0.25rem 0.75rem',\n                      borderRadius: '20px',\n                      textTransform: 'uppercase',\n                      letterSpacing: '0.5px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.25rem'\n                    }}>\n                      <IconComponent size={12} color=\"white\" />\n                      {categoryName}\n                    </span>\n                  );\n                })()}\n\n                <span style={{\n                  background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                  color: 'white',\n                  padding: '0.25rem 0.75rem',\n                  borderRadius: '12px',\n                  fontSize: '0.75rem',\n                  fontWeight: '600',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                }}>\n                  <Pin size={12} />\n                  PINNED\n                </span>\n              </div>\n\n              <h2 style={{\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '700',\n                color: '#111827',\n                lineHeight: '1.3'\n              }}>\n                {selectedPinnedPost.title}\n              </h2>\n\n              <div style={{\n                color: '#4b5563',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                marginBottom: '1.5rem'\n              }}>\n                {selectedPinnedPost.content}\n              </div>\n\n              {/* Images */}\n              {selectedPinnedPost.attachments && selectedPinnedPost.attachments.length > 0 && (\n                <div style={{ marginBottom: '1.5rem' }}>\n                  <ImageGallery\n                    images={selectedPinnedPost.attachments}\n                    altPrefix={selectedPinnedPost.title}\n                    onImageClick={(index) => {\n                      console.log('View image:', index);\n                    }}\n                  />\n                </div>\n              )}\n\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem',\n                fontSize: '0.875rem',\n                color: '#6b7280',\n                paddingTop: '1rem',\n                borderTop: '1px solid #e5e7eb'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                }}>\n                  <Calendar size={16} />\n                  <span>Published: {new Date(selectedPinnedPost.created_at).toLocaleDateString()}</span>\n                </div>\n                {selectedPinnedPost.author_name && (\n                  <div>\n                    By: {selectedPinnedPost.author_name}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default AdminNewsfeed;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,OAAOC,mBAAmB,MAAM,4CAA4C;AAC5E,OAAOC,oBAAoB,MAAM,8CAA8C;AAG/E,SAASC,WAAW,EAAEC,YAAY,QAAQ,wBAAwB;AAClE,SACEC,SAAS,EACTC,MAAM,EACNC,GAAG,EACHC,QAAQ,EACRC,aAAa,EACbC,KAAK,EACLC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,eAAe,EACfC,QAAQ,EACRC,WAAW,EACXC,aAAa,EACbC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,aAAa,EACbC,IAAI,EACJC,MAAM,EACNC,KAAK,QACA,cAAc;;AAErB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,YAAyC,GAAGA,CAAC;EACjDC,SAAS;EACTC,GAAG;EACHC,KAAK;EACLC,YAAY;EACZC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAM4C,gBAAgB,GAAGA,CAAA,KAAM;IAC7BH,aAAa,CAAC,IAAI,CAAC;IACnBE,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5BF,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,IAAIH,UAAU,EAAE;IACd,oBACEV,OAAA;MAAKM,KAAK,EAAE;QACV,GAAGA,KAAK;QACRU,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE;MACZ,CAAE;MAAAC,QAAA,EAAC;IAEH;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEV;EAEA,oBACE1B,OAAA;IAAKM,KAAK,EAAE;MAAEqB,QAAQ,EAAE,UAAU;MAAE,GAAGrB;IAAM,CAAE;IAAAgB,QAAA,GAC5CV,YAAY,iBACXZ,OAAA;MAAKM,KAAK,EAAE;QACVqB,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTf,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE;MACT,CAAE;MAAAE,QAAA,EAAC;IAEH;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN,eACD1B,OAAA;MACEgC,GAAG,EAAEvD,WAAW,CAAC2B,SAAS,IAAI,EAAE,CAAC,IAAI,EAAG;MACxCC,GAAG,EAAEA,GAAI;MACTC,KAAK,EAAE;QACL,GAAGA,KAAK;QACR2B,OAAO,EAAErB,YAAY,GAAG,CAAC,GAAG,CAAC;QAC7BsB,UAAU,EAAE;MACd,CAAE;MACFC,OAAO,EAAErB,gBAAiB;MAC1BsB,MAAM,EAAErB,eAAgB;MACxBR,YAAY,EAAEA,YAAa;MAC3BC,YAAY,EAAEA;IAAa;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;;AAED;AAAAjB,EAAA,CAtEMN,YAAyC;AAAAkC,EAAA,GAAzClC,YAAyC;AA6E/C,MAAMmC,YAAyC,GAAGA,CAAC;EAAEC,MAAM;EAAEC,SAAS;EAAEC;AAAa,CAAC,KAAK;EACzF,IAAI,CAACF,MAAM,IAAIA,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAE/C,MAAMC,aAAa,GAAGJ,MAAM,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACxC,MAAMC,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;EAErD,MAAMM,iBAAiB,GAAGA,CAACC,KAAa,EAAEC,KAAa,KAA0B;IAC/E,MAAMC,SAA8B,GAAG;MACrCxB,QAAQ,EAAE,UAAU;MACpByB,QAAQ,EAAE,QAAQ;MAClBC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAEb,YAAY,GAAG,SAAS,GAAG;IACrC,CAAC;IAED,IAAIS,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QAAE,GAAGC,SAAS;QAAEI,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAQ,CAAC;IACzD,CAAC,MAAM,IAAIN,KAAK,KAAK,CAAC,EAAE;MACtB,OAAO;QAAE,GAAGC,SAAS;QAAEI,KAAK,EAAE,KAAK;QAAEC,MAAM,EAAE;MAAQ,CAAC;IACxD,CAAC,MAAM,IAAIN,KAAK,KAAK,CAAC,EAAE;MACtB,IAAID,KAAK,KAAK,CAAC,EAAE;QACf,OAAO;UAAE,GAAGE,SAAS;UAAEI,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAC;MACxD,CAAC,MAAM;QACL,OAAO;UAAE,GAAGL,SAAS;UAAEI,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAC;MACxD;IACF,CAAC,MAAM;MACL,IAAIP,KAAK,KAAK,CAAC,EAAE;QACf,OAAO;UAAE,GAAGE,SAAS;UAAEI,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAC;MACxD,CAAC,MAAM;QACL,OAAO;UAAE,GAAGL,SAAS;UAAEI,KAAK,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAQ,CAAC;MAC3D;IACF;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAACR,KAAa,EAAEC,KAAa,KAA0B;IAC3E,OAAO;MACLK,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdE,SAAS,EAAE,OAAgB;MAC3BxB,UAAU,EAAE;IACd,CAAC;EACH,CAAC;EAED,oBACElC,OAAA;IAAKM,KAAK,EAAE;MACVU,OAAO,EAAE,MAAM;MACf2C,GAAG,EAAE,KAAK;MACVJ,KAAK,EAAE,MAAM;MACbK,YAAY,EAAE;IAChB,CAAE;IAAAtC,QAAA,gBAEAtB,OAAA;MAAKM,KAAK,EAAE0C,iBAAiB,CAAC,CAAC,EAAEL,aAAa,CAACD,MAAM,CAAE;MAAApB,QAAA,gBACrDtB,OAAA,CAACG,YAAY;QACXC,SAAS,EAAEuC,aAAa,CAAC,CAAC,CAAC,CAACkB,SAAU;QACtCxD,GAAG,EAAE,GAAGmC,SAAS,YAAa;QAC9BlC,KAAK,EAAEmD,aAAa,CAAC,CAAC,EAAEd,aAAa,CAACD,MAAM,CAAE;QAC9CnC,YAAY,EAAGuD,CAAC,IAAK;UACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,aAAa;QACjD,CAAE;QACFxD,YAAY,EAAGsD,CAAC,IAAK;UACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,UAAU;QAC9C;MAAE;QAAAzC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACDe,YAAY,iBACXzC,OAAA;QACEM,KAAK,EAAE;UACLqB,QAAQ,EAAE,UAAU;UACpBC,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTuB,MAAM,EAAE;QACV,CAAE;QACFW,OAAO,EAAEA,CAAA,KAAMxB,YAAY,CAAC,CAAC;MAAE;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLiB,aAAa,CAACD,MAAM,GAAG,CAAC,iBACvB1C,OAAA;MAAKM,KAAK,EAAE;QACVU,OAAO,EAAE,MAAM;QACfkD,aAAa,EAAEvB,aAAa,CAACD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,QAAQ;QAC5DiB,GAAG,EAAE,KAAK;QACVJ,KAAK,EAAEZ,aAAa,CAACD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG;MAC9C,CAAE;MAAApB,QAAA,EACCqB,aAAa,CAACC,KAAK,CAAC,CAAC,CAAC,CAACuB,GAAG,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;QAC1C,MAAMC,WAAW,GAAGD,GAAG,GAAG,CAAC;QAC3B,MAAME,MAAM,GAAGD,WAAW,KAAK3B,aAAa,CAACD,MAAM,GAAG,CAAC,IAAIG,cAAc,GAAG,CAAC;QAE7E,oBACE7C,OAAA;UAEEM,KAAK,EAAE;YACL,GAAG0C,iBAAiB,CAACsB,WAAW,EAAE3B,aAAa,CAACD,MAAM,CAAC;YACvDf,QAAQ,EAAE;UACZ,CAAE;UAAAL,QAAA,gBAEFtB,OAAA,CAACG,YAAY;YACXC,SAAS,EAAEgE,KAAK,CAACP,SAAU;YAC3BxD,GAAG,EAAE,GAAGmC,SAAS,YAAY8B,WAAW,GAAG,CAAC,EAAG;YAC/ChE,KAAK,EAAEmD,aAAa,CAACa,WAAW,EAAE3B,aAAa,CAACD,MAAM,CAAE;YACxDnC,YAAY,EAAGuD,CAAC,IAAK;cACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,aAAa;YACjD,CAAE;YACFxD,YAAY,EAAGsD,CAAC,IAAK;cACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,UAAU;YAC9C;UAAE;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGD6C,MAAM,IAAI1B,cAAc,GAAG,CAAC,iBAC3B7C,OAAA;YAAKM,KAAK,EAAE;cACVqB,QAAQ,EAAE,UAAU;cACpBC,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTZ,eAAe,EAAE,oBAAoB;cACrCH,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBE,KAAK,EAAE,OAAO;cACdC,QAAQ,EAAE,QAAQ;cAClBmD,UAAU,EAAE;YACd,CAAE;YAAAlD,QAAA,GAAC,GACA,EAACuB,cAAc;UAAA;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CACN,EAEAe,YAAY,iBACXzC,OAAA;YACEM,KAAK,EAAE;cACLqB,QAAQ,EAAE,UAAU;cACpBC,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTuB,MAAM,EAAE;YACV,CAAE;YACFW,OAAO,EAAEA,CAAA,KAAMxB,YAAY,CAAC6B,WAAW;UAAE;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CACF;QAAA,GAlDI4C,WAAW;UAAA/C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmDb,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAA+C,GAAA,GAvJMnC,YAAyC;AAwJ/C,MAAMoC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACpC,MAAMC,QAAQ,GAAGxG,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMyG,gBAAgB,GAAIC,YAAoB,IAAK;IACjD,MAAMC,MAAM,GAAG;MACb,UAAU,EAAE;QACVC,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE5F;MACR,CAAC;MACD,SAAS,EAAE;QACT2F,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE9F;MACR,CAAC;MACD,QAAQ,EAAE;QACR6F,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE3F;MACR,CAAC;MACD,WAAW,EAAE;QACX0F,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE1F;MACR,CAAC;MACD,QAAQ,EAAE;QACRyF,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAExF;MACR,CAAC;MACD,WAAW,EAAE;QACXuF,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEzF;MACR;IACF,CAAC;IAED,OAAOuF,MAAM,CAACD,YAAY,CAAwB,IAAIC,MAAM,CAAC,SAAS,CAAC;EACzE,CAAC;;EAED;EACA,MAAMG,mBAAmB,GAAIC,eAAuB,IAAK;IACvD,MAAMJ,MAAM,GAAG;MACb,kBAAkB,EAAE;QAClBC,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAErF;MACR,CAAC;MACD,cAAc,EAAE;QACdoF,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEtF;MACR,CAAC;MACD,gBAAgB,EAAE;QAChBqF,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEpF;MACR,CAAC;MACD,cAAc,EAAE;QACdmF,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAExF;MACR,CAAC;MACD,YAAY,EAAE;QACZuF,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEnF;MACR,CAAC;MACD,SAAS,EAAE;QACTkF,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEvF;MACR;IACF,CAAC;IAED,OAAOqF,MAAM,CAACI,eAAe,CAAwB,IAAIJ,MAAM,CAAC,cAAc,CAAC;EACjF,CAAC;;EAED;EACA,MAAM,CAACK,cAAc,EAAEC,iBAAiB,CAAC,GAAGnH,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAACoH,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrH,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAACsH,UAAU,EAAEC,aAAa,CAAC,GAAGvH,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAM,CAACwH,YAAY,EAAEC,eAAe,CAAC,GAAGzH,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAAC0H,UAAU,EAAEC,aAAa,CAAC,GAAG3H,QAAQ,CAA4B,CAAC,CAAC,CAAC;EAC3E,MAAM,CAAC4H,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7H,QAAQ,CAAgB,IAAI,CAAC;EAC/E,MAAM,CAAC8H,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG/H,QAAQ,CAAa,IAAI,CAAC;;EAE9E;EACA,MAAM,CAACgI,aAAa,EAAEC,gBAAgB,CAAC,GAAGjI,QAAQ,CAAQ,EAAE,CAAC;EAC7D,MAAM,CAACkI,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGnI,QAAQ,CAAQ,EAAE,CAAC;EACzE,MAAM,CAACoI,cAAc,EAAEC,iBAAiB,CAAC,GAAGrI,QAAQ,CAAkB,EAAE,CAAC;EACzE,MAAM,CAACsI,OAAO,EAAEC,UAAU,CAAC,GAAGvI,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwI,KAAK,EAAEC,QAAQ,CAAC,GAAGzI,QAAQ,CAAqB,CAAC;EACxD,MAAM,CAAC0I,eAAe,EAAEC,kBAAkB,CAAC,GAAG3I,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC4I,aAAa,EAAEC,gBAAgB,CAAC,GAAG7I,QAAQ,CAAqB,CAAC;EACxE,MAAM,CAAC8I,cAAc,EAAEC,iBAAiB,CAAC,GAAG/I,QAAQ,CAAQ,EAAE,CAAC;EAC/D,MAAM,CAACgJ,cAAc,EAAEC,iBAAiB,CAAC,GAAGjJ,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAM;IAAEkJ;EAAW,CAAC,GAAG9I,aAAa,CAAC,CAAC;;EAEtC;EACA,MAAM+I,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAACW,SAAS,CAAC;MAEnB,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9I,YAAY,wFAAwF,EAAE;QACpI+I,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;UAC/D,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC7B,MAAMG,iBAAiB,GAAGH,IAAI,CAACA,IAAI,CAAC1B,aAAa,IAAI,EAAE;;QAEvD;QACA;QACAC,gBAAgB,CAAC4B,iBAAiB,CAAC;;QAEnC;QACA,MAAMC,MAAM,GAAGD,iBAAiB,CAACE,MAAM,CAAEC,GAAQ,IAAKA,GAAG,CAACC,SAAS,KAAK,CAAC,CAAC;QAC1E9B,sBAAsB,CAAC2B,MAAM,CAAC;MAChC,CAAC,MAAM;QACLrB,QAAQ,CAAC,8BAA8B,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOyB,GAAQ,EAAE;MACjBC,OAAO,CAAC3B,KAAK,CAAC,+BAA+B,EAAE0B,GAAG,CAAC;MACnDzB,QAAQ,CAACyB,GAAG,CAACE,OAAO,IAAI,8BAA8B,CAAC;IACzD,CAAC,SAAS;MACR7B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM8B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF1B,kBAAkB,CAAC,IAAI,CAAC;MACxBE,gBAAgB,CAACO,SAAS,CAAC;MAE3B,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9I,YAAY,0DAA0D,EAAE;QACtG+I,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;UAC/D,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC7B,MAAMY,UAAU,GAAGZ,IAAI,CAACA,IAAI,CAACa,MAAM,IAAIb,IAAI,CAACA,IAAI,IAAI,EAAE;;QAEtD;QACA,MAAMc,gBAAgB,GAAG,MAAMC,OAAO,CAACC,GAAG,CACxCJ,UAAU,CAACrE,GAAG,CAAC,MAAO0E,KAAU,IAAK;UACnC,IAAI;YACF,MAAMC,aAAa,GAAG,MAAMtB,KAAK,CAAC,GAAG9I,YAAY,iBAAiBmK,KAAK,CAACE,WAAW,SAAS,EAAE;cAC5FtB,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;gBAC/D,cAAc,EAAE;cAClB;YACF,CAAC,CAAC;YACF,MAAMqB,SAAS,GAAG,MAAMF,aAAa,CAACjB,IAAI,CAAC,CAAC;YAE5C,IAAImB,SAAS,CAAClB,OAAO,IAAIkB,SAAS,CAACpB,IAAI,EAAE;cACvCiB,KAAK,CAACtG,MAAM,GAAGyG,SAAS,CAACpB,IAAI,CAACqB,WAAW,IAAI,EAAE;YACjD,CAAC,MAAM;cACLJ,KAAK,CAACtG,MAAM,GAAG,EAAE;YACnB;UACF,CAAC,CAAC,OAAO2G,MAAM,EAAE;YACfb,OAAO,CAACc,IAAI,CAAC,oCAAoCN,KAAK,CAACE,WAAW,GAAG,EAAEG,MAAM,CAAC;YAC9EL,KAAK,CAACtG,MAAM,GAAG,EAAE;UACnB;UACA,OAAOsG,KAAK;QACd,CAAC,CACH,CAAC;QAEDtC,iBAAiB,CAACmC,gBAAgB,CAAC;MACrC,CAAC,MAAM;QACL3B,gBAAgB,CAAC,gCAAgC,CAAC;MACpD;IACF,CAAC,CAAC,OAAOqB,GAAQ,EAAE;MACjBC,OAAO,CAAC3B,KAAK,CAAC,iCAAiC,EAAE0B,GAAG,CAAC;MACrDrB,gBAAgB,CAACqB,GAAG,CAACE,OAAO,IAAI,gCAAgC,CAAC;IACnE,CAAC,SAAS;MACRzB,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAMuC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFjC,iBAAiB,CAAC,IAAI,CAAC;MACvB,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9I,YAAY,iEAAiE,EAAE;QAC7G+I,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;UAC/D,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC7BX,iBAAiB,CAACW,IAAI,CAACA,IAAI,CAACyB,QAAQ,IAAI,EAAE,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOjB,GAAQ,EAAE;MACjBC,OAAO,CAAC3B,KAAK,CAAC,iCAAiC,EAAE0B,GAAG,CAAC;IACvD,CAAC,SAAS;MACRjB,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;;EAED;EACAhJ,SAAS,CAAC,MAAM;IACdkJ,2BAA2B,CAAC,CAAC;IAC7BkB,mBAAmB,CAAC,CAAC;IACrBa,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,gBAAgB,GAAG,MAAOC,YAAiB,IAAK;IACpD,IAAI;MACF,IAAIA,YAAY,CAACC,aAAa,EAAE;QAC9B,MAAMnL,mBAAmB,CAACoL,cAAc,CAACF,YAAY,CAACG,eAAe,CAAC;MACxE,CAAC,MAAM;QACL,MAAMnC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9I,YAAY,sBAAsB6K,YAAY,CAACG,eAAe,YAAY,EAAE;UAC1GC,MAAM,EAAE,MAAM;UACdlC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;YAC/D,cAAc,EAAE;UAClB,CAAC;UACDiC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBC,gBAAgB,EAAE,CAAC;YACnBC,YAAY,EAAE,KAAK,CAAC;UACtB,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,CAACzC,QAAQ,CAAC0C,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;QAC3C;MACF;;MAEA;MACA,MAAM7C,2BAA2B,CAAC,CAAC;IACrC,CAAC,CAAC,OAAOX,KAAK,EAAE;MACd2B,OAAO,CAAC3B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,MAAMyD,mBAAmB,GAAG,MAAAA,CAAOC,cAAsB,EAAEC,WAAmB,KAAK;IACjF,IAAI,CAACA,WAAW,CAACC,IAAI,CAAC,CAAC,EAAE;IAEzB,IAAI;MACFvE,oBAAoB,CAACqE,cAAc,CAAC;MAEpC,MAAM7C,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG9I,YAAY,4BAA4B0L,cAAc,WAAW,EAAE;QACjGT,MAAM,EAAE,MAAM;QACdlC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;UAC/D,cAAc,EAAE;QAClB,CAAC;QACDiC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBS,OAAO,EAAEF,WAAW;UACpBG,cAAc,EAAE,YAAY;UAC5BC,eAAe,EAAE,mBAAmB;UACpCT,YAAY,EAAE;QAChB,CAAC;MACH,CAAC,CAAC;MAEF,IAAIzC,QAAQ,CAAC0C,EAAE,EAAE;QACfpE,aAAa,CAAC6E,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACN,cAAc,GAAG;QAAG,CAAC,CAAC,CAAC;QAC1D,MAAM/C,2BAA2B,CAAC,CAAC;MACrC,CAAC,MAAM;QACLgB,OAAO,CAAC3B,KAAK,CAAC,0BAA0B,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd2B,OAAO,CAAC3B,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,SAAS;MACRX,oBAAoB,CAAC,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAM4E,qBAAqB,GAAGzE,aAAa,CAAC+B,MAAM,CAACsB,YAAY,IAAI;IAAA,IAAAqB,qBAAA,EAAAC,qBAAA;IACjE,MAAMC,aAAa,GAAG,CAACtF,UAAU,IAC/B+D,YAAY,CAACwB,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzF,UAAU,CAACwF,WAAW,CAAC,CAAC,CAAC,IACnEzB,YAAY,CAACgB,OAAO,CAACS,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzF,UAAU,CAACwF,WAAW,CAAC,CAAC,CAAC;IAEvE,MAAME,eAAe,GAAG,CAAC9F,cAAc,IACrC,EAAAwF,qBAAA,GAAArB,YAAY,CAAC4B,WAAW,cAAAP,qBAAA,uBAAxBA,qBAAA,CAA0BQ,QAAQ,CAAC,CAAC,MAAKhG,cAAc;IAEzD,MAAMiG,iBAAiB,GAAG,CAAC/F,gBAAgB,IACzC,EAAAuF,qBAAA,GAAAtB,YAAY,CAAC+B,WAAW,cAAAT,qBAAA,uBAAxBA,qBAAA,CAA0BO,QAAQ,CAAC,CAAC,MAAK9F,gBAAgB;IAE3D,OAAOwF,aAAa,IAAII,eAAe,IAAIG,iBAAiB;EAC9D,CAAC,CAAC;;EAEF;EACA,MAAME,sBAAsB,GAAGjF,cAAc,CAAC2B,MAAM,CAACY,KAAK,IAAI;IAC5D,MAAMiC,aAAa,GAAG,CAACtF,UAAU,IAC/BqD,KAAK,CAACkC,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzF,UAAU,CAACwF,WAAW,CAAC,CAAC,CAAC,IAC3DnC,KAAK,CAAC2C,WAAW,IAAI3C,KAAK,CAAC2C,WAAW,CAACR,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACzF,UAAU,CAACwF,WAAW,CAAC,CAAC,CAAE;;IAE3F;IACA,MAAMS,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAE5B,MAAMC,SAAS,GAAG,IAAIF,IAAI,CAAC7C,KAAK,CAACgD,UAAU,CAAC;IAC5CD,SAAS,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhC,MAAMG,gBAAgB,GAAGF,SAAS,IAAIH,KAAK;IAC3C,MAAMM,WAAW,GAAIlD,KAAK,CAASmD,YAAY,KAAK,CAAC;IAErD,OAAOlB,aAAa,IAAIgB,gBAAgB,IAAIC,WAAW;EACzD,CAAC,CAAC;;EAEF;EACA,MAAME,oBAAoB,GAAGtB,qBAAqB;EAClD,MAAMuB,aAAa,GAAGX,sBAAsB;;EAE5C;EACA,MAAMY,eAAe,GAAG,CACtB,GAAGF,oBAAoB,CAAC9H,GAAG,CAACiI,IAAI,KAAK;IAAE,GAAGA,IAAI;IAAEC,IAAI,EAAE,cAAc;IAAEC,QAAQ,EAAE,IAAIZ,IAAI,CAACU,IAAI,CAACG,UAAU;EAAE,CAAC,CAAC,CAAC,EAC7G,GAAGL,aAAa,CAAC/H,GAAG,CAACiI,IAAI,KAAK;IAAE,GAAGA,IAAI;IAAEC,IAAI,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAIZ,IAAI,CAACU,IAAI,CAACP,UAAU;EAAE,CAAC,CAAC,CAAC,CAChG,CAACW,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACJ,QAAQ,CAACK,OAAO,CAAC,CAAC,GAAGF,CAAC,CAACH,QAAQ,CAACK,OAAO,CAAC,CAAC,CAAC;EAE7D,oBACE3M,OAAA;IAAKM,KAAK,EAAE;MACVsM,SAAS,EAAE,OAAO;MAClB5H,UAAU,EAAE,mDAAmD;MAC/DrD,QAAQ,EAAE;IACZ,CAAE;IAAAL,QAAA,gBAEAtB,OAAA;MAAKM,KAAK,EAAE;QACVqB,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACT8K,eAAe,EAAE;AACzB;AACA;AACA,SAAS;QACDC,aAAa,EAAE;MACjB;IAAE;MAAAvL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEL1B,OAAA;MAAKM,KAAK,EAAE;QAAEqB,QAAQ,EAAE,UAAU;QAAEoL,MAAM,EAAE;MAAE,CAAE;MAAAzL,QAAA,gBAE9CtB,OAAA;QAAQM,KAAK,EAAE;UACb0E,UAAU,EAAE,OAAO;UACnBgI,YAAY,EAAE,mBAAmB;UACjCrL,QAAQ,EAAE,QAAQ;UAClBC,GAAG,EAAE,CAAC;UACNmL,MAAM,EAAE,GAAG;UACXE,SAAS,EAAE;QACb,CAAE;QAAA3L,QAAA,eACAtB,OAAA;UAAKM,KAAK,EAAE;YACV4M,OAAO,EAAE,QAAQ;YACjB1J,MAAM,EAAE,MAAM;YACdxC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAI,QAAA,gBAEAtB,OAAA;YAAKM,KAAK,EAAE;cACVU,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpB0C,GAAG,EAAE,QAAQ;cACbwJ,QAAQ,EAAE;YACZ,CAAE;YAAA7L,QAAA,gBACAtB,OAAA;cACEgC,GAAG,EAAC,iBAAiB;cACrB3B,GAAG,EAAC,WAAW;cACfC,KAAK,EAAE;gBACLiD,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdE,SAAS,EAAE;cACb;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF1B,OAAA;cAAAsB,QAAA,gBACEtB,OAAA;gBAAIM,KAAK,EAAE;kBACT8M,MAAM,EAAE,CAAC;kBACT/L,QAAQ,EAAE,SAAS;kBACnBmD,UAAU,EAAE,KAAK;kBACjBpD,KAAK,EAAE,SAAS;kBAChBiM,UAAU,EAAE;gBACd,CAAE;gBAAA/L,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1B,OAAA;gBAAGM,KAAK,EAAE;kBACR8M,MAAM,EAAE,CAAC;kBACT/L,QAAQ,EAAE,UAAU;kBACpBD,KAAK,EAAE,SAAS;kBAChBiM,UAAU,EAAE;gBACd,CAAE;gBAAA/L,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1B,OAAA;YAAKM,KAAK,EAAE;cACVgN,IAAI,EAAE,CAAC;cACPC,QAAQ,EAAE,OAAO;cACjBH,MAAM,EAAE;YACV,CAAE;YAAA9L,QAAA,eACAtB,OAAA;cAAKM,KAAK,EAAE;gBAAEqB,QAAQ,EAAE;cAAW,CAAE;cAAAL,QAAA,gBACnCtB,OAAA,CAACpB,MAAM;gBACL4O,IAAI,EAAE,EAAG;gBACTlN,KAAK,EAAE;kBACLqB,QAAQ,EAAE,UAAU;kBACpBE,IAAI,EAAE,MAAM;kBACZD,GAAG,EAAE,KAAK;kBACVoC,SAAS,EAAE,kBAAkB;kBAC7B5C,KAAK,EAAE;gBACT;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF1B,OAAA;gBACEqM,IAAI,EAAC,MAAM;gBACXoB,WAAW,EAAC,oCAAoC;gBAChDC,KAAK,EAAElI,UAAW;gBAClBmI,QAAQ,EAAG7J,CAAC,IAAK2B,aAAa,CAAC3B,CAAC,CAAC8J,MAAM,CAACF,KAAK,CAAE;gBAC/CpN,KAAK,EAAE;kBACLiD,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACd0J,OAAO,EAAE,eAAe;kBACxBW,MAAM,EAAE,mBAAmB;kBAC3BxK,YAAY,EAAE,MAAM;kBACpB2B,UAAU,EAAE,SAAS;kBACrB5D,KAAK,EAAE,SAAS;kBAChBC,QAAQ,EAAE,UAAU;kBACpByM,OAAO,EAAE,MAAM;kBACf5L,UAAU,EAAE;gBACd,CAAE;gBACF6L,OAAO,EAAGjK,CAAC,IAAK;kBACdA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0N,WAAW,GAAG,SAAS;kBAC7ClK,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,OAAO;kBAC1ClB,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC2M,SAAS,GAAG,kCAAkC;gBACtE,CAAE;gBACFgB,MAAM,EAAGnK,CAAC,IAAK;kBACbA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0N,WAAW,GAAG,SAAS;kBAC7ClK,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,SAAS;kBAC5ClB,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC2M,SAAS,GAAG,MAAM;gBAC1C;cAAE;gBAAA1L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1B,OAAA;YAAKM,KAAK,EAAE;cACVU,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpB0C,GAAG,EAAE,MAAM;cACXwJ,QAAQ,EAAE,OAAO;cACjBjM,cAAc,EAAE;YAClB,CAAE;YAAAI,QAAA,gBAGAtB,OAAA;cAAKM,KAAK,EAAE;gBACVU,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB0C,GAAG,EAAE,QAAQ;gBACbuJ,OAAO,EAAE,QAAQ;gBACjBlI,UAAU,EAAE,SAAS;gBACrB3B,YAAY,EAAE,MAAM;gBACpBwK,MAAM,EAAE;cACV,CAAE;cAAAvM,QAAA,gBACAtB,OAAA;gBACE0N,KAAK,EAAEtI,cAAe;gBACtBuI,QAAQ,EAAG7J,CAAC,IAAKuB,iBAAiB,CAACvB,CAAC,CAAC8J,MAAM,CAACF,KAAK,CAAE;gBACnDpN,KAAK,EAAE;kBACL4M,OAAO,EAAE,gBAAgB;kBACzBW,MAAM,EAAE,MAAM;kBACdxK,YAAY,EAAE,KAAK;kBACnB2B,UAAU,EAAE,OAAO;kBACnB5D,KAAK,EAAE,SAAS;kBAChBC,QAAQ,EAAE,UAAU;kBACpByM,OAAO,EAAE,MAAM;kBACfxK,MAAM,EAAE,SAAS;kBACjB6J,QAAQ,EAAE;gBACZ,CAAE;gBAAA7L,QAAA,gBAEFtB,OAAA;kBAAQ0N,KAAK,EAAC,EAAE;kBAAApM,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACvC0F,UAAU,CAACjD,GAAG,CAAC+J,QAAQ,iBACtBlO,OAAA;kBAAmC0N,KAAK,EAAEQ,QAAQ,CAAC/C,WAAW,CAACC,QAAQ,CAAC,CAAE;kBAAA9J,QAAA,EACvE4M,QAAQ,CAACC;gBAAI,GADHD,QAAQ,CAAC/C,WAAW;kBAAA5J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEzB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAET1B,OAAA;gBACE0N,KAAK,EAAEpI,gBAAiB;gBACxBqI,QAAQ,EAAG7J,CAAC,IAAKyB,mBAAmB,CAACzB,CAAC,CAAC8J,MAAM,CAACF,KAAK,CAAE;gBACrDpN,KAAK,EAAE;kBACL4M,OAAO,EAAE,gBAAgB;kBACzBW,MAAM,EAAE,MAAM;kBACdxK,YAAY,EAAE,KAAK;kBACnB2B,UAAU,EAAE,OAAO;kBACnB5D,KAAK,EAAE,SAAS;kBAChBC,QAAQ,EAAE,UAAU;kBACpByM,OAAO,EAAE,MAAM;kBACfxK,MAAM,EAAE,SAAS;kBACjB6J,QAAQ,EAAE;gBACZ,CAAE;gBAAA7L,QAAA,gBAEFtB,OAAA;kBAAQ0N,KAAK,EAAC,EAAE;kBAAApM,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC1B,OAAA;kBAAQ0N,KAAK,EAAC,IAAI;kBAAApM,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC1B,OAAA;kBAAQ0N,KAAK,EAAC,IAAI;kBAAApM,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,EAER,CAAC8D,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,kBAChDtF,OAAA;gBACEiE,OAAO,EAAEA,CAAA,KAAM;kBACbwB,aAAa,CAAC,EAAE,CAAC;kBACjBJ,iBAAiB,CAAC,EAAE,CAAC;kBACrBE,mBAAmB,CAAC,EAAE,CAAC;gBACzB,CAAE;gBACFjF,KAAK,EAAE;kBACL4M,OAAO,EAAE,gBAAgB;kBACzBW,MAAM,EAAE,MAAM;kBACdxK,YAAY,EAAE,KAAK;kBACnB2B,UAAU,EAAE,SAAS;kBACrB5D,KAAK,EAAE,OAAO;kBACdC,QAAQ,EAAE,UAAU;kBACpBmD,UAAU,EAAE,KAAK;kBACjBlB,MAAM,EAAE,SAAS;kBACjBpB,UAAU,EAAE;gBACd,CAAE;gBACF3B,YAAY,EAAGuD,CAAC,IAAK;kBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,SAAS;gBAC9C,CAAE;gBACFxE,YAAY,EAAGsD,CAAC,IAAK;kBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,SAAS;gBAC9C,CAAE;gBAAA1D,QAAA,EACH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN1B,OAAA;cACEiE,OAAO,EAAEA,CAAA,KAAMW,QAAQ,CAAC,kBAAkB,CAAE;cAC5CtE,KAAK,EAAE;gBACLU,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB0C,GAAG,EAAE,QAAQ;gBACbuJ,OAAO,EAAE,cAAc;gBACvBlI,UAAU,EAAE,mDAAmD;gBAC/D6I,MAAM,EAAE,MAAM;gBACdxK,YAAY,EAAE,MAAM;gBACpBjC,KAAK,EAAE,OAAO;gBACdC,QAAQ,EAAE,UAAU;gBACpBmD,UAAU,EAAE,KAAK;gBACjBlB,MAAM,EAAE,SAAS;gBACjBpB,UAAU,EAAE,eAAe;gBAC3B+K,SAAS,EAAE;cACb,CAAE;cACF1M,YAAY,EAAGuD,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,kBAAkB;gBACpDF,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC2M,SAAS,GAAG,mCAAmC;cACvE,CAAE;cACFzM,YAAY,EAAGsD,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,eAAe;gBACjDF,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC2M,SAAS,GAAG,kCAAkC;cACtE,CAAE;cAAA3L,QAAA,gBAEFtB,OAAA,CAACZ,eAAe;gBAACoO,IAAI,EAAE;cAAG;gBAAAjM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,aAE/B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAKT1B,OAAA;QAAKM,KAAK,EAAE;UACV4M,OAAO,EAAE,MAAM;UACflM,OAAO,EAAE,MAAM;UACf2C,GAAG,EAAE,MAAM;UACX1C,UAAU,EAAE;QACd,CAAE;QAAAK,QAAA,gBAEAtB,OAAA;UAAKM,KAAK,EAAE;YACViD,KAAK,EAAE,OAAO;YACd6K,UAAU,EAAE;UACd,CAAE;UAAA9M,QAAA,eACAtB,OAAA;YAAKM,KAAK,EAAE;cACV0E,UAAU,EAAE,OAAO;cACnB3B,YAAY,EAAE,MAAM;cACpBwK,MAAM,EAAE,mBAAmB;cAC3BzK,QAAQ,EAAE,QAAQ;cAClBzB,QAAQ,EAAE,QAAQ;cAClBC,GAAG,EAAE;YACP,CAAE;YAAAN,QAAA,gBAEAtB,OAAA;cAAKM,KAAK,EAAE;gBACV4M,OAAO,EAAE,oBAAoB;gBAC7BF,YAAY,EAAE;cAChB,CAAE;cAAA1L,QAAA,gBACAtB,OAAA;gBAAKM,KAAK,EAAE;kBACVU,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpB0C,GAAG,EAAE,SAAS;kBACdC,YAAY,EAAE;gBAChB,CAAE;gBAAAtC,QAAA,gBACAtB,OAAA,CAACnB,GAAG;kBAAC2O,IAAI,EAAE,EAAG;kBAAClN,KAAK,EAAE;oBAAEc,KAAK,EAAE;kBAAU;gBAAE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9C1B,OAAA;kBAAIM,KAAK,EAAE;oBACT8M,MAAM,EAAE,CAAC;oBACT/L,QAAQ,EAAE,UAAU;oBACpBmD,UAAU,EAAE,KAAK;oBACjBpD,KAAK,EAAE;kBACT,CAAE;kBAAAE,QAAA,EAAC;gBAEH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACN1B,OAAA;gBAAGM,KAAK,EAAE;kBACR8M,MAAM,EAAE,CAAC;kBACT/L,QAAQ,EAAE,UAAU;kBACpBD,KAAK,EAAE;gBACT,CAAE;gBAAAE,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGN1B,OAAA;cAAKM,KAAK,EAAE;gBAAE4M,OAAO,EAAE;cAAO,CAAE;cAAA5L,QAAA,EAC7B8E,mBAAmB,CAAC1D,MAAM,GAAG,CAAC,gBAC7B1C,OAAA,CAAAE,SAAA;gBAAAoB,QAAA,GACG8E,mBAAmB,CAACxD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACuB,GAAG,CAAC,CAACoF,YAAY,EAAEtG,KAAK,KAAK;kBAC5D,MAAM6B,YAAY,GAAG,CAACyE,YAAY,CAAC8E,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;kBAC5E,MAAMC,aAAa,GAAG1J,gBAAgB,CAACC,YAAY,CAAC;kBAEpD,oBACE9E,OAAA;oBAEEM,KAAK,EAAE;sBACL4M,OAAO,EAAE,MAAM;sBACflI,UAAU,EAAE,SAAS;sBACrB3B,YAAY,EAAE,MAAM;sBACpBwK,MAAM,EAAE,mBAAmB;sBAC3BjK,YAAY,EAAE,MAAM;sBACpBN,MAAM,EAAE,SAAS;sBACjBpB,UAAU,EAAE;oBACd,CAAE;oBACF3B,YAAY,EAAGuD,CAAC,IAAK;sBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,SAAS;sBAC5ClB,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0N,WAAW,GAAG,SAAS;oBAC/C,CAAE;oBACFxN,YAAY,EAAGsD,CAAC,IAAK;sBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,SAAS;sBAC5ClB,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0N,WAAW,GAAG,SAAS;oBAC/C,CAAE;oBACF/J,OAAO,EAAEA,CAAA,KAAMgC,qBAAqB,CAACsD,YAAY,CAAE;oBAAAjI,QAAA,eAEnDtB,OAAA;sBAAKM,KAAK,EAAE;wBACVU,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,YAAY;wBACxB0C,GAAG,EAAE;sBACP,CAAE;sBAAArC,QAAA,gBACAtB,OAAA;wBAAKM,KAAK,EAAE;0BACViD,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,KAAK;0BACbwB,UAAU,EAAEuJ,aAAa,CAACvJ,UAAU,CAACiG,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,SAAS;0BAChF5H,YAAY,EAAE,KAAK;0BACnBmL,SAAS,EAAE,QAAQ;0BACnBJ,UAAU,EAAE;wBACd;sBAAE;wBAAA7M,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACL1B,OAAA;wBAAKM,KAAK,EAAE;0BAAEgN,IAAI,EAAE;wBAAE,CAAE;wBAAAhM,QAAA,gBACtBtB,OAAA;0BAAIM,KAAK,EAAE;4BACT8M,MAAM,EAAE,cAAc;4BACtB/L,QAAQ,EAAE,UAAU;4BACpBmD,UAAU,EAAE,KAAK;4BACjBpD,KAAK,EAAE,SAAS;4BAChBiM,UAAU,EAAE;0BACd,CAAE;0BAAA/L,QAAA,EACCiI,YAAY,CAACwB;wBAAK;0BAAAxJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB,CAAC,eACL1B,OAAA;0BAAGM,KAAK,EAAE;4BACR8M,MAAM,EAAE,cAAc;4BACtB/L,QAAQ,EAAE,QAAQ;4BAClBD,KAAK,EAAE,SAAS;4BAChBiM,UAAU,EAAE;0BACd,CAAE;0BAAA/L,QAAA,EACCiI,YAAY,CAACgB,OAAO,CAAC7H,MAAM,GAAG,EAAE,GAC7B,GAAG6G,YAAY,CAACgB,OAAO,CAACkE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAC7ClF,YAAY,CAACgB;wBAAO;0BAAAhJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvB,CAAC,eACJ1B,OAAA;0BAAKM,KAAK,EAAE;4BACVU,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpB0C,GAAG,EAAE,QAAQ;4BACbtC,QAAQ,EAAE,SAAS;4BACnBD,KAAK,EAAE;0BACT,CAAE;0BAAAE,QAAA,gBACAtB,OAAA,CAAClB,QAAQ;4BAAC0O,IAAI,EAAE;0BAAG;4BAAAjM,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACtB1B,OAAA;4BAAAsB,QAAA,EAAO,IAAIoK,IAAI,CAACnC,YAAY,CAACgD,UAAU,CAAC,CAACmC,kBAAkB,CAAC;0BAAC;4BAAAnN,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GAhED6H,YAAY,CAACG,eAAe;oBAAAnI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAiE9B,CAAC;gBAEV,CAAC,CAAC,EAED0E,mBAAmB,CAAC1D,MAAM,GAAG,CAAC,iBAC7B1C,OAAA;kBAAQM,KAAK,EAAE;oBACbiD,KAAK,EAAE,MAAM;oBACb2J,OAAO,EAAE,SAAS;oBAClBW,MAAM,EAAE,mBAAmB;oBAC3BxK,YAAY,EAAE,KAAK;oBACnB2B,UAAU,EAAE,OAAO;oBACnB5D,KAAK,EAAE,SAAS;oBAChBC,QAAQ,EAAE,UAAU;oBACpBmD,UAAU,EAAE,KAAK;oBACjBlB,MAAM,EAAE,SAAS;oBACjBpB,UAAU,EAAE;kBACd,CAAE;kBACF3B,YAAY,EAAGuD,CAAC,IAAK;oBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,SAAS;oBAC5ClB,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0N,WAAW,GAAG,SAAS;kBAC/C,CAAE;kBACFxN,YAAY,EAAGsD,CAAC,IAAK;oBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,OAAO;oBAC1ClB,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0N,WAAW,GAAG,SAAS;kBAC/C,CAAE;kBAAA1M,QAAA,GAAC,WACQ,EAAC8E,mBAAmB,CAAC1D,MAAM,EAAC,eACvC;gBAAA;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA,eACD,CAAC,gBAEH1B,OAAA;gBAAKM,KAAK,EAAE;kBACV4M,OAAO,EAAE,WAAW;kBACpByB,SAAS,EAAE,QAAQ;kBACnBvN,KAAK,EAAE;gBACT,CAAE;gBAAAE,QAAA,gBACAtB,OAAA,CAACnB,GAAG;kBAAC2O,IAAI,EAAE,EAAG;kBAAClN,KAAK,EAAE;oBAAEsD,YAAY,EAAE,QAAQ;oBAAE3B,OAAO,EAAE;kBAAI;gBAAE;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClE1B,OAAA;kBAAGM,KAAK,EAAE;oBAAE8M,MAAM,EAAE,CAAC;oBAAE/L,QAAQ,EAAE;kBAAW,CAAE;kBAAAC,QAAA,EAAC;gBAE/C;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1B,OAAA;UAAKM,KAAK,EAAE;YAAEgN,IAAI,EAAE,CAAC;YAAEH,QAAQ,EAAE;UAAE,CAAE;UAAA7L,QAAA,GAEpC,CAACkF,OAAO,IAAII,eAAe,kBAC1B5G,OAAA;YAAKM,KAAK,EAAE;cACVU,OAAO,EAAE,MAAM;cACfE,cAAc,EAAE,QAAQ;cACxBD,UAAU,EAAE,QAAQ;cACpB2L,SAAS,EAAE;YACb,CAAE;YAAAtL,QAAA,eACAtB,OAAA;cAAKM,KAAK,EAAE;gBACVU,OAAO,EAAE,MAAM;gBACfkD,aAAa,EAAE,QAAQ;gBACvBjD,UAAU,EAAE,QAAQ;gBACpB0C,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,gBACAtB,OAAA;gBAAKM,KAAK,EAAE;kBACViD,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdqK,MAAM,EAAE,kCAAkC;kBAC1Ce,SAAS,EAAE,mBAAmB;kBAC9BvL,YAAY,EAAE,KAAK;kBACnBwL,SAAS,EAAE;gBACb;cAAE;gBAAAtN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACT1B,OAAA;gBAAGM,KAAK,EAAE;kBACRc,KAAK,EAAE,SAAS;kBAChBC,QAAQ,EAAE,MAAM;kBAChBmD,UAAU,EAAE;gBACd,CAAE;gBAAAlD,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA,CAACgF,KAAK,IAAII,aAAa,KAAK,CAACN,OAAO,IAAI,CAACI,eAAe,iBACvD5G,OAAA;YAAKM,KAAK,EAAE;cACV4M,OAAO,EAAE,MAAM;cACflI,UAAU,EAAE,wBAAwB;cACpC6I,MAAM,EAAE,kCAAkC;cAC1CxK,YAAY,EAAE,MAAM;cACpBsL,SAAS,EAAE;YACb,CAAE;YAAArN,QAAA,gBACAtB,OAAA;cAAKM,KAAK,EAAE;gBACViD,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdwB,UAAU,EAAE,wBAAwB;gBACpC3B,YAAY,EAAE,KAAK;gBACnBrC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBkM,MAAM,EAAE;cACV,CAAE;cAAA9L,QAAA,eACAtB,OAAA,CAACjB,aAAa;gBAACyO,IAAI,EAAE,EAAG;gBAACpM,KAAK,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACN1B,OAAA;cAAIM,KAAK,EAAE;gBACTc,KAAK,EAAE,SAAS;gBAChBgM,MAAM,EAAE,cAAc;gBACtB/L,QAAQ,EAAE,SAAS;gBACnBmD,UAAU,EAAE;cACd,CAAE;cAAAlD,QAAA,EAAC;YAEH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1B,OAAA;cAAGM,KAAK,EAAE;gBACRc,KAAK,EAAE,SAAS;gBAChBgM,MAAM,EAAE,cAAc;gBACtB/L,QAAQ,EAAE;cACZ,CAAE;cAAAC,QAAA,EACCoF,KAAK,IAAII;YAAa;cAAAvF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACJ1B,OAAA;cACEiE,OAAO,EAAEA,CAAA,KAAM;gBACboD,2BAA2B,CAAC,CAAC;gBAC7BkB,mBAAmB,CAAC,CAAC;cACvB,CAAE;cACFjI,KAAK,EAAE;gBACL0E,UAAU,EAAE,mDAAmD;gBAC/D5D,KAAK,EAAE,OAAO;gBACdyM,MAAM,EAAE,MAAM;gBACdxK,YAAY,EAAE,MAAM;gBACpB6J,OAAO,EAAE,gBAAgB;gBACzB7L,QAAQ,EAAE,UAAU;gBACpBmD,UAAU,EAAE,KAAK;gBACjBlB,MAAM,EAAE,SAAS;gBACjBpB,UAAU,EAAE;cACd,CAAE;cACF3B,YAAY,EAAGuD,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,kBAAkB;gBACpDF,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC2M,SAAS,GAAG,mCAAmC;cACvE,CAAE;cACFzM,YAAY,EAAGsD,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,eAAe;gBACjDF,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC2M,SAAS,GAAG,MAAM;cAC1C,CAAE;cAAA3L,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAGA,CAAC8E,OAAO,IAAI,CAACI,eAAe,IAAI,CAACF,KAAK,IAAI,CAACI,aAAa,IACxDmF,oBAAoB,CAACvJ,MAAM,KAAK,CAAC,IAAIwJ,aAAa,CAACxJ,MAAM,KAAK,CAAC,iBAC9D1C,OAAA;YAAKM,KAAK,EAAE;cACV4M,OAAO,EAAE,WAAW;cACpByB,SAAS,EAAE;YACb,CAAE;YAAArN,QAAA,gBACAtB,OAAA;cAAKM,KAAK,EAAE;gBACViD,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdwB,UAAU,EAAE,mDAAmD;gBAC/D3B,YAAY,EAAE,KAAK;gBACnBrC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBkM,MAAM,EAAE;cACV,CAAE;cAAA9L,QAAA,eACAtB,OAAA,CAACrB,SAAS;gBAAC6O,IAAI,EAAE,EAAG;gBAACpM,KAAK,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACN1B,OAAA;cAAIM,KAAK,EAAE;gBACTc,KAAK,EAAE,SAAS;gBAChBgM,MAAM,EAAE,YAAY;gBACpB/L,QAAQ,EAAE,QAAQ;gBAClBmD,UAAU,EAAE;cACd,CAAE;cAAAlD,QAAA,EAAC;YAEH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1B,OAAA;cAAGM,KAAK,EAAE;gBACRc,KAAK,EAAE,SAAS;gBAChBgM,MAAM,EAAE,YAAY;gBACpB/L,QAAQ,EAAE,MAAM;gBAChBgM,UAAU,EAAE,KAAK;gBACjBE,QAAQ,EAAE,OAAO;gBACjBuB,UAAU,EAAE,MAAM;gBAClBC,WAAW,EAAE;cACf,CAAE;cAAAzN,QAAA,EACCkE,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,GAC7C,8EAA8E,GAC9E;YAA6F;cAAA/D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhG,CAAC,EACH,CAAC8D,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,kBAChDtF,OAAA;cACEiE,OAAO,EAAEA,CAAA,KAAM;gBACbwB,aAAa,CAAC,EAAE,CAAC;gBACjBJ,iBAAiB,CAAC,EAAE,CAAC;gBACrBE,mBAAmB,CAAC,EAAE,CAAC;cACzB,CAAE;cACFjF,KAAK,EAAE;gBACL0E,UAAU,EAAE,mDAAmD;gBAC/D5D,KAAK,EAAE,OAAO;gBACdyM,MAAM,EAAE,MAAM;gBACdxK,YAAY,EAAE,MAAM;gBACpB6J,OAAO,EAAE,gBAAgB;gBACzB7L,QAAQ,EAAE,UAAU;gBACpBmD,UAAU,EAAE,KAAK;gBACjBlB,MAAM,EAAE,SAAS;gBACjBpB,UAAU,EAAE;cACd,CAAE;cACF3B,YAAY,EAAGuD,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,kBAAkB;gBACpDF,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC2M,SAAS,GAAG,mCAAmC;cACvE,CAAE;cACFzM,YAAY,EAAGsD,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,eAAe;gBACjDF,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC2M,SAAS,GAAG,MAAM;cAC1C,CAAE;cAAA3L,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAGA,CAACwF,cAAc,IAAIF,cAAc,CAACtE,MAAM,GAAG,CAAC,iBAC3C1C,OAAA;YAAKM,KAAK,EAAE;cACV0E,UAAU,EAAE,2BAA2B;cACvC3B,YAAY,EAAE,MAAM;cACpB6J,OAAO,EAAE,QAAQ;cACjBW,MAAM,EAAE,8BAA8B;cACtCmB,cAAc,EAAE,YAAY;cAC5B/B,SAAS,EAAE,gCAAgC;cAC3CrJ,YAAY,EAAE;YAChB,CAAE;YAAAtC,QAAA,gBACAtB,OAAA;cAAIM,KAAK,EAAE;gBACTe,QAAQ,EAAE,SAAS;gBACnBmD,UAAU,EAAE,KAAK;gBACjBpD,KAAK,EAAE,SAAS;gBAChBgM,MAAM,EAAE,YAAY;gBACpBpM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB0C,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,EAAC;YAEH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1B,OAAA;cAAKM,KAAK,EAAE;gBACVU,OAAO,EAAE,MAAM;gBACf2C,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,EACC0F,cAAc,CAACpE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACuB,GAAG,CAAE8K,OAAY;gBAAA,IAAAC,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA;gBAAA,oBAC3CpP,OAAA;kBAEEM,KAAK,EAAE;oBACLU,OAAO,EAAE,MAAM;oBACfE,cAAc,EAAE,eAAe;oBAC/BD,UAAU,EAAE,QAAQ;oBACpBiM,OAAO,EAAE,SAAS;oBAClB/L,eAAe,EAAE,SAAS;oBAC1BkC,YAAY,EAAE,KAAK;oBACnBwK,MAAM,EAAE;kBACV,CAAE;kBAAAvM,QAAA,gBAEFtB,OAAA;oBAAAsB,QAAA,gBACEtB,OAAA;sBAAKM,KAAK,EAAE;wBACVkE,UAAU,EAAE,KAAK;wBACjBpD,KAAK,EAAE,SAAS;wBAChBC,QAAQ,EAAE;sBACZ,CAAE;sBAAAC,QAAA,IAAA4N,gBAAA,GACCD,OAAO,CAACI,OAAO,cAAAH,gBAAA,uBAAfA,gBAAA,CAAiBI,UAAU,EAAC,GAAC,GAAAH,iBAAA,GAACF,OAAO,CAACI,OAAO,cAAAF,iBAAA,uBAAfA,iBAAA,CAAiBI,SAAS;oBAAA;sBAAAhO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC,eACN1B,OAAA;sBAAKM,KAAK,EAAE;wBACVe,QAAQ,EAAE,SAAS;wBACnBD,KAAK,EAAE;sBACT,CAAE;sBAAAE,QAAA,GAAC,QACK,GAAA8N,iBAAA,GAACH,OAAO,CAACI,OAAO,cAAAD,iBAAA,uBAAfA,iBAAA,CAAiB9D,WAAW,EAAC,UAAG,EAAC2D,OAAO,CAACO,cAAc;oBAAA;sBAAAjO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN1B,OAAA;oBAAKM,KAAK,EAAE;sBACVe,QAAQ,EAAE,SAAS;sBACnBD,KAAK,EAAE;oBACT,CAAE;oBAAAE,QAAA,EACC,IAAIoK,IAAI,CAACuD,OAAO,CAAC1C,UAAU,CAAC,CAACmC,kBAAkB,CAAC;kBAAC;oBAAAnN,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC;gBAAA,GA/BDuN,OAAO,CAACQ,UAAU;kBAAAlO,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgCpB,CAAC;cAAA,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA,CAAC8E,OAAO,IAAI,CAACI,eAAe,KAAKqF,oBAAoB,CAACvJ,MAAM,GAAG,CAAC,IAAIwJ,aAAa,CAACxJ,MAAM,GAAG,CAAC,CAAC,iBAC5F1C,OAAA;YAAKM,KAAK,EAAE;cACVU,OAAO,EAAE,MAAM;cACfkD,aAAa,EAAE,QAAQ;cACvBP,GAAG,EAAE;YACP,CAAE;YAAArC,QAAA,GAEC4K,aAAa,CAACxJ,MAAM,GAAG,CAAC,iBACvB1C,OAAA,CAAAE,SAAA;cAAAoB,QAAA,EACG4K,aAAa,CAAC/H,GAAG,CAAC0E,KAAK,iBACtB7I,OAAA;gBAEEM,KAAK,EAAE;kBACL0E,UAAU,EAAE,2BAA2B;kBACvC3B,YAAY,EAAE,MAAM;kBACpB6J,OAAO,EAAE,QAAQ;kBACjBW,MAAM,EAAE,8BAA8B;kBACtCmB,cAAc,EAAE,YAAY;kBAC5B/B,SAAS,EAAE,gCAAgC;kBAC3C/K,UAAU,EAAE;gBACd,CAAE;gBACF3B,YAAY,EAAGuD,CAAC,IAAK;kBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,kBAAkB;kBACpDF,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC2M,SAAS,GAAG,gCAAgC;gBACpE,CAAE;gBACFzM,YAAY,EAAGsD,CAAC,IAAK;kBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,eAAe;kBACjDF,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC2M,SAAS,GAAG,gCAAgC;gBACpE,CAAE;gBAAA3L,QAAA,gBAGFtB,OAAA;kBAAKM,KAAK,EAAE;oBACVU,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,YAAY;oBACxB0C,GAAG,EAAE,MAAM;oBACXC,YAAY,EAAE;kBAChB,CAAE;kBAAAtC,QAAA,gBACAtB,OAAA;oBAAKM,KAAK,EAAE;sBACViD,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdwB,UAAU,EAAE,mDAAmD;sBAC/D3B,YAAY,EAAE,MAAM;sBACpBrC,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE,QAAQ;sBACxBkN,UAAU,EAAE;oBACd,CAAE;oBAAA9M,QAAA,eACAtB,OAAA,CAAClB,QAAQ;sBAAC0O,IAAI,EAAE,EAAG;sBAACpM,KAAK,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,eAEN1B,OAAA;oBAAKM,KAAK,EAAE;sBAAEgN,IAAI,EAAE;oBAAE,CAAE;oBAAAhM,QAAA,gBACtBtB,OAAA;sBAAKM,KAAK,EAAE;wBACVU,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB0C,GAAG,EAAE,SAAS;wBACdC,YAAY,EAAE;sBAChB,CAAE;sBAAAtC,QAAA,GACC,CAAC,MAAM;wBACN,MAAM6D,eAAe,GAAG0D,KAAK,CAAC6G,iBAAiB,IAAI,cAAc;wBACjE,MAAMC,YAAY,GAAGzK,mBAAmB,CAACC,eAAe,CAAC;wBACzD,MAAMyK,aAAa,GAAGD,YAAY,CAAC1K,IAAI;wBAEvC,oBACEjF,OAAA;0BAAMM,KAAK,EAAE;4BACX0E,UAAU,EAAE2K,YAAY,CAAC3K,UAAU;4BACnC5D,KAAK,EAAE,OAAO;4BACdC,QAAQ,EAAE,SAAS;4BACnBmD,UAAU,EAAE,KAAK;4BACjB0I,OAAO,EAAE,iBAAiB;4BAC1B7J,YAAY,EAAE,MAAM;4BACpBwM,aAAa,EAAE,WAAW;4BAC1BC,aAAa,EAAE,OAAO;4BACtB9O,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpB0C,GAAG,EAAE;0BACP,CAAE;0BAAArC,QAAA,gBACAtB,OAAA,CAAC4P,aAAa;4BAACpC,IAAI,EAAE,EAAG;4BAACpM,KAAK,EAAC;0BAAO;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,EACxCyD,eAAe;wBAAA;0BAAA5D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC;sBAEX,CAAC,EAAE,CAAC,eAEJ1B,OAAA;wBAAKM,KAAK,EAAE;0BACVU,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB0C,GAAG,EAAE,SAAS;0BACdvC,KAAK,EAAE,SAAS;0BAChBC,QAAQ,EAAE;wBACZ,CAAE;wBAAAC,QAAA,gBACAtB,OAAA,CAAClB,QAAQ;0BAAC0O,IAAI,EAAE;wBAAG;0BAAAjM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACrB,IAAIgK,IAAI,CAAC7C,KAAK,CAACgD,UAAU,CAAC,CAAC6C,kBAAkB,CAAC,OAAO,EAAE;0BACtDqB,OAAO,EAAE,MAAM;0BACfC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,MAAM;0BACbC,GAAG,EAAE;wBACP,CAAC,CAAC;sBAAA;wBAAA3O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN1B,OAAA;sBAAIM,KAAK,EAAE;wBACT8M,MAAM,EAAE,cAAc;wBACtB/L,QAAQ,EAAE,SAAS;wBACnBmD,UAAU,EAAE,KAAK;wBACjBpD,KAAK,EAAE,SAAS;wBAChBiM,UAAU,EAAE;sBACd,CAAE;sBAAA/L,QAAA,EACCuH,KAAK,CAACkC;oBAAK;sBAAAxJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAGN1B,OAAA;oBAAKM,KAAK,EAAE;sBAAEU,OAAO,EAAE,MAAM;sBAAE2C,GAAG,EAAE;oBAAS,CAAE;oBAAArC,QAAA,eAC7CtB,OAAA;sBACEiE,OAAO,EAAEA,CAAA,KAAMW,QAAQ,CAAC,yBAAyBiE,KAAK,CAACE,WAAW,EAAE,CAAE;sBACtEzI,KAAK,EAAE;wBACLU,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB0C,GAAG,EAAE,SAAS;wBACduJ,OAAO,EAAE,gBAAgB;wBACzBlI,UAAU,EAAE,yBAAyB;wBACrC6I,MAAM,EAAE,mCAAmC;wBAC3CxK,YAAY,EAAE,KAAK;wBACnBjC,KAAK,EAAE,SAAS;wBAChBC,QAAQ,EAAE,SAAS;wBACnBmD,UAAU,EAAE,KAAK;wBACjBlB,MAAM,EAAE,SAAS;wBACjBpB,UAAU,EAAE;sBACd,CAAE;sBACF3B,YAAY,EAAGuD,CAAC,IAAK;wBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,yBAAyB;sBAC9D,CAAE;sBACFxE,YAAY,EAAGsD,CAAC,IAAK;wBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,yBAAyB;sBAC9D,CAAE;sBAAA1D,QAAA,gBAEFtB,OAAA,CAACd,IAAI;wBAACsO,IAAI,EAAE;sBAAG;wBAAAjM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,QAEpB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGLmH,KAAK,CAAC2C,WAAW,iBAChBxL,OAAA;kBAAKM,KAAK,EAAE;oBACVc,KAAK,EAAE,SAAS;oBAChBC,QAAQ,EAAE,SAAS;oBACnBgM,UAAU,EAAE,KAAK;oBACjBzJ,YAAY,EAAE;kBAChB,CAAE;kBAAAtC,QAAA,EACCuH,KAAK,CAAC2C;gBAAW;kBAAAjK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CACN,EAGA,CAAC,MAAM;kBACN;kBACA,MAAMyO,cAAwB,GAAG,EAAE;kBAEnC,IAAKtH,KAAK,CAAStG,MAAM,IAAKsG,KAAK,CAAStG,MAAM,CAACG,MAAM,GAAG,CAAC,EAAE;oBAC5DmG,KAAK,CAAStG,MAAM,CAAC6N,OAAO,CAAEC,GAAQ,IAAK;sBAC1C,IAAIA,GAAG,CAACxM,SAAS,EAAE;wBACjB;wBACA,MAAMyM,QAAQ,GAAG7R,WAAW,CAAC4R,GAAG,CAACxM,SAAS,CAAC;wBAC3C,IAAIyM,QAAQ,EAAE;0BACZH,cAAc,CAACI,IAAI,CAACD,QAAQ,CAAC;wBAC/B;sBACF;oBACF,CAAC,CAAC;kBACJ;kBAEA,OAAOH,cAAc,CAACzN,MAAM,GAAG,CAAC,gBAC9B1C,OAAA;oBAAKM,KAAK,EAAE;sBAAEsD,YAAY,EAAE;oBAAO,CAAE;oBAAAtC,QAAA,eACnCtB,OAAA,CAACxB,oBAAoB;sBACnB+D,MAAM,EAAE4N,cAAc,CAAClI,MAAM,CAACuI,OAAO,CAAc;sBACnDhO,SAAS,EAAEqG,KAAK,CAACkC,KAAM;sBACvB0F,UAAU,EAAE,CAAE;sBACdhO,YAAY,EAAGQ,KAAK,IAAK;wBACvBoF,OAAO,CAACqI,GAAG,CAAC,iBAAiBzN,KAAK,GAAG,CAAC,eAAe4F,KAAK,CAACkC,KAAK,EAAE,CAAC;sBACrE;oBAAE;sBAAAxJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,GACJ,IAAI;gBACV,CAAC,EAAE,CAAC,eAGJ1B,OAAA;kBAAKM,KAAK,EAAE;oBACVU,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpB0C,GAAG,EAAE,QAAQ;oBACbuJ,OAAO,EAAE,MAAM;oBACflI,UAAU,EAAE,0BAA0B;oBACtC3B,YAAY,EAAE,MAAM;oBACpBhC,QAAQ,EAAE;kBACZ,CAAE;kBAAAC,QAAA,gBACAtB,OAAA;oBAAKM,KAAK,EAAE;sBACVU,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB0C,GAAG,EAAE,QAAQ;sBACbvC,KAAK,EAAE;oBACT,CAAE;oBAAAE,QAAA,gBACAtB,OAAA,CAAClB,QAAQ;sBAAC0O,IAAI,EAAE;oBAAG;sBAAAjM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACtB1B,OAAA;sBAAAsB,QAAA,EACGuH,KAAK,CAAC8H,QAAQ,IAAI9H,KAAK,CAAC8H,QAAQ,KAAK9H,KAAK,CAACgD,UAAU,GAClD,GAAG,IAAIH,IAAI,CAAC7C,KAAK,CAACgD,UAAU,CAAC,CAAC6C,kBAAkB,CAAC,CAAC,MAAM,IAAIhD,IAAI,CAAC7C,KAAK,CAAC8H,QAAQ,CAAC,CAACjC,kBAAkB,CAAC,CAAC,EAAE,GACvG,IAAIhD,IAAI,CAAC7C,KAAK,CAACgD,UAAU,CAAC,CAAC6C,kBAAkB,CAAC;oBAAC;sBAAAnN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAE/C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,EAELmH,KAAK,CAAC6G,iBAAiB,iBACtB1P,OAAA;oBAAKM,KAAK,EAAE;sBACVU,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB0C,GAAG,EAAE,QAAQ;sBACbvC,KAAK,EAAE;oBACT,CAAE;oBAAAE,QAAA,eACAtB,OAAA;sBAAMM,KAAK,EAAE;wBACX4M,OAAO,EAAE,gBAAgB;wBACzBlI,UAAU,EAAE,yBAAyB;wBACrC3B,YAAY,EAAE,KAAK;wBACnBhC,QAAQ,EAAE,SAAS;wBACnBmD,UAAU,EAAE;sBACd,CAAE;sBAAAlD,QAAA,EACCuH,KAAK,CAAC6G;oBAAiB;sBAAAnO,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GAxND,SAASmH,KAAK,CAACE,WAAW,EAAE;gBAAAxH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyN9B,CACN;YAAC,gBACF,CACH,EAGAuK,oBAAoB,CAACvJ,MAAM,GAAG,CAAC,iBAC9B1C,OAAA,CAAAE,SAAA;cAAAoB,QAAA,EACG2K,oBAAoB,CAAC9H,GAAG,CAACoF,YAAY,iBACpCvJ,OAAA;gBAEEM,KAAK,EAAE;kBACL0E,UAAU,EAAE,2BAA2B;kBACvC3B,YAAY,EAAE,MAAM;kBACpB6J,OAAO,EAAE,QAAQ;kBACjBW,MAAM,EAAEtE,YAAY,CAACpB,SAAS,GAC1B,mCAAmC,GACnC,8BAA8B;kBAClC6G,cAAc,EAAE,YAAY;kBAC5B/B,SAAS,EAAE1D,YAAY,CAACpB,SAAS,GAC7B,qCAAqC,GACrC,gCAAgC;kBACpCjG,UAAU,EAAE,2CAA2C;kBACvDP,QAAQ,EAAE;gBACZ,CAAE;gBACFpB,YAAY,EAAGuD,CAAC,IAAK;kBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,kBAAkB;kBACpDF,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC2M,SAAS,GAAG1D,YAAY,CAACpB,SAAS,GACpD,qCAAqC,GACrC,gCAAgC;gBACtC,CAAE;gBACF3H,YAAY,EAAGsD,CAAC,IAAK;kBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,eAAe;kBACjDF,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC2M,SAAS,GAAG1D,YAAY,CAACpB,SAAS,GACpD,qCAAqC,GACrC,gCAAgC;gBACtC,CAAE;gBAAA7G,QAAA,GAGDiI,YAAY,CAACpB,SAAS,iBACrBnI,OAAA;kBAAKM,KAAK,EAAE;oBACVqB,QAAQ,EAAE,UAAU;oBACpBC,GAAG,EAAE,MAAM;oBACXE,KAAK,EAAE,MAAM;oBACbkD,UAAU,EAAE,mDAAmD;oBAC/D5D,KAAK,EAAE,OAAO;oBACd8L,OAAO,EAAE,iBAAiB;oBAC1B7J,YAAY,EAAE,MAAM;oBACpBhC,QAAQ,EAAE,SAAS;oBACnBmD,UAAU,EAAE,KAAK;oBACjBxD,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpB0C,GAAG,EAAE,SAAS;oBACdsJ,SAAS,EAAE;kBACb,CAAE;kBAAA3L,QAAA,gBACAtB,OAAA,CAACnB,GAAG;oBAAC2O,IAAI,EAAE;kBAAG;oBAAAjM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAEnB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN,eAGD1B,OAAA;kBAAKM,KAAK,EAAE;oBACVU,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,YAAY;oBACxB0C,GAAG,EAAE,MAAM;oBACXC,YAAY,EAAE;kBAChB,CAAE;kBAAAtC,QAAA,GACC,CAAC,MAAM;oBACN,IAAIiI,YAAY,CAACqH,QAAQ,EAAE;sBACzB,oBACE5Q,OAAA;wBAAKM,KAAK,EAAE;0BACViD,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdwB,UAAU,EAAE,mDAAmD;0BAC/D3B,YAAY,EAAE,MAAM;0BACpBrC,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpBC,cAAc,EAAE,QAAQ;0BACxBkN,UAAU,EAAE;wBACd,CAAE;wBAAA9M,QAAA,eACAtB,OAAA,CAACT,aAAa;0BAACiO,IAAI,EAAE,EAAG;0BAACpM,KAAK,EAAC;wBAAO;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAEV,CAAC,MAAM;sBACL,MAAMoD,YAAY,GAAG,CAACyE,YAAY,CAAC8E,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;sBAC5E,MAAMC,aAAa,GAAG1J,gBAAgB,CAACC,YAAY,CAAC;sBACpD,MAAM8K,aAAa,GAAGrB,aAAa,CAACtJ,IAAI;sBAExC,oBACEjF,OAAA;wBAAKM,KAAK,EAAE;0BACViD,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdwB,UAAU,EAAEuJ,aAAa,CAACvJ,UAAU;0BACpC3B,YAAY,EAAE,MAAM;0BACpBrC,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpBC,cAAc,EAAE,QAAQ;0BACxBkN,UAAU,EAAE;wBACd,CAAE;wBAAA9M,QAAA,eACAtB,OAAA,CAAC4P,aAAa;0BAACpC,IAAI,EAAE,EAAG;0BAACpM,KAAK,EAAC;wBAAO;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAEV;kBACF,CAAC,EAAE,CAAC,eAEJ1B,OAAA;oBAAKM,KAAK,EAAE;sBAAEgN,IAAI,EAAE;oBAAE,CAAE;oBAAAhM,QAAA,gBACtBtB,OAAA;sBAAKM,KAAK,EAAE;wBACVU,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB0C,GAAG,EAAE,SAAS;wBACdC,YAAY,EAAE,QAAQ;wBACtBiN,QAAQ,EAAE;sBACZ,CAAE;sBAAAvP,QAAA,GACC,CAAC,MAAM;wBACN,IAAIiI,YAAY,CAACqH,QAAQ,EAAE;0BACzB,oBACE5Q,OAAA;4BAAMM,KAAK,EAAE;8BACX0E,UAAU,EAAE,mDAAmD;8BAC/D5D,KAAK,EAAE,OAAO;8BACdC,QAAQ,EAAE,SAAS;8BACnBmD,UAAU,EAAE,KAAK;8BACjB0I,OAAO,EAAE,iBAAiB;8BAC1B7J,YAAY,EAAE,MAAM;8BACpBwM,aAAa,EAAE,WAAW;8BAC1BC,aAAa,EAAE,OAAO;8BACtB9O,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpB0C,GAAG,EAAE;4BACP,CAAE;4BAAArC,QAAA,gBACAtB,OAAA,CAACT,aAAa;8BAACiO,IAAI,EAAE,EAAG;8BAACpM,KAAK,EAAC;4BAAO;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,SAE3C;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAEX,CAAC,MAAM;0BACL,MAAMoD,YAAY,GAAG,CAACyE,YAAY,CAAC8E,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;0BAC5E,MAAMC,aAAa,GAAG1J,gBAAgB,CAACC,YAAY,CAAC;0BACpD,MAAM8K,aAAa,GAAGrB,aAAa,CAACtJ,IAAI;0BAExC,oBACEjF,OAAA;4BAAMM,KAAK,EAAE;8BACX0E,UAAU,EAAEuJ,aAAa,CAACvJ,UAAU;8BACpC5D,KAAK,EAAE,OAAO;8BACdC,QAAQ,EAAE,SAAS;8BACnBmD,UAAU,EAAE,KAAK;8BACjB0I,OAAO,EAAE,iBAAiB;8BAC1B7J,YAAY,EAAE,MAAM;8BACpBwM,aAAa,EAAE,WAAW;8BAC1BC,aAAa,EAAE,OAAO;8BACtB9O,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpB0C,GAAG,EAAE;4BACP,CAAE;4BAAArC,QAAA,gBACAtB,OAAA,CAAC4P,aAAa;8BAACpC,IAAI,EAAE,EAAG;8BAACpM,KAAK,EAAC;4BAAO;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EACxCoD,YAAY;0BAAA;4BAAAvD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACT,CAAC;wBAEX;sBACF,CAAC,EAAE,CAAC,EAEH6H,YAAY,CAAC+B,WAAW,iBACvBtL,OAAA;wBAAMM,KAAK,EAAE;0BACX0E,UAAU,EAAE,yBAAyB;0BACrC5D,KAAK,EAAE,SAAS;0BAChBC,QAAQ,EAAE,SAAS;0BACnBmD,UAAU,EAAE,KAAK;0BACjB0I,OAAO,EAAE,iBAAiB;0BAC1B7J,YAAY,EAAE;wBAChB,CAAE;wBAAA/B,QAAA,GAAC,QACK,EAACiI,YAAY,CAAC+B,WAAW;sBAAA;wBAAA/J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3B,CACP,eAED1B,OAAA;wBAAKM,KAAK,EAAE;0BACVc,KAAK,EAAE,SAAS;0BAChBC,QAAQ,EAAE;wBACZ,CAAE;wBAAAC,QAAA,EACC,IAAIoK,IAAI,CAACnC,YAAY,CAACgD,UAAU,CAAC,CAACmC,kBAAkB,CAAC,OAAO,EAAE;0BAC7DqB,OAAO,EAAE,OAAO;0BAChBC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,OAAO;0BACdC,GAAG,EAAE;wBACP,CAAC;sBAAC;wBAAA3O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN1B,OAAA;sBAAIM,KAAK,EAAE;wBACT8M,MAAM,EAAE,cAAc;wBACtB/L,QAAQ,EAAE,SAAS;wBACnBmD,UAAU,EAAE,KAAK;wBACjBpD,KAAK,EAAE,SAAS;wBAChBiM,UAAU,EAAE;sBACd,CAAE;sBAAA/L,QAAA,EACCiI,YAAY,CAACwB;oBAAK;sBAAAxJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAGN1B,OAAA;oBAAKM,KAAK,EAAE;sBAAEU,OAAO,EAAE,MAAM;sBAAE2C,GAAG,EAAE;oBAAS,CAAE;oBAAArC,QAAA,gBAC7CtB,OAAA;sBACEiE,OAAO,EAAEA,CAAA,KAAMW,QAAQ,CAAC,qBAAqB2E,YAAY,CAACG,eAAe,EAAE,CAAE;sBAC7EpJ,KAAK,EAAE;wBACLU,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB0C,GAAG,EAAE,SAAS;wBACduJ,OAAO,EAAE,gBAAgB;wBACzBlI,UAAU,EAAE,wBAAwB;wBACpC6I,MAAM,EAAE,kCAAkC;wBAC1CxK,YAAY,EAAE,KAAK;wBACnBjC,KAAK,EAAE,SAAS;wBAChBC,QAAQ,EAAE,SAAS;wBACnBmD,UAAU,EAAE,KAAK;wBACjBlB,MAAM,EAAE,SAAS;wBACjBpB,UAAU,EAAE;sBACd,CAAE;sBACF3B,YAAY,EAAGuD,CAAC,IAAK;wBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,wBAAwB;sBAC7D,CAAE;sBACFxE,YAAY,EAAGsD,CAAC,IAAK;wBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,wBAAwB;sBAC7D,CAAE;sBAAA1D,QAAA,gBAEFtB,OAAA,CAACd,IAAI;wBAACsO,IAAI,EAAE;sBAAG;wBAAAjM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,QAEpB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAET1B,OAAA;sBACEM,KAAK,EAAE;wBACLU,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB0C,GAAG,EAAE,SAAS;wBACduJ,OAAO,EAAE,gBAAgB;wBACzBlI,UAAU,EAAE,yBAAyB;wBACrC6I,MAAM,EAAE,mCAAmC;wBAC3CxK,YAAY,EAAE,KAAK;wBACnBjC,KAAK,EAAE,SAAS;wBAChBC,QAAQ,EAAE,SAAS;wBACnBmD,UAAU,EAAE,KAAK;wBACjBlB,MAAM,EAAE,SAAS;wBACjBpB,UAAU,EAAE;sBACd,CAAE;sBACF3B,YAAY,EAAGuD,CAAC,IAAK;wBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,yBAAyB;sBAC9D,CAAE;sBACFxE,YAAY,EAAGsD,CAAC,IAAK;wBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,yBAAyB;sBAC9D,CAAE;sBAAA1D,QAAA,gBAEFtB,OAAA,CAACf,GAAG;wBAACuO,IAAI,EAAE;sBAAG;wBAAAjM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,aAEnB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN1B,OAAA;kBAAKM,KAAK,EAAE;oBACVc,KAAK,EAAE,SAAS;oBAChBC,QAAQ,EAAE,SAAS;oBACnBgM,UAAU,EAAE,KAAK;oBACjBzJ,YAAY,EAAE;kBAChB,CAAE;kBAAAtC,QAAA,EACCiI,YAAY,CAACgB;gBAAO;kBAAAhJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,EAGL6H,YAAY,CAACN,WAAW,IAAIM,YAAY,CAACN,WAAW,CAACvG,MAAM,GAAG,CAAC,iBAC9D1C,OAAA,CAACsC,YAAY;kBACXC,MAAM,EAAEgH,YAAY,CAACN,WAAY;kBACjCzG,SAAS,EAAE+G,YAAY,CAACwB,KAAM;kBAC9BtI,YAAY,EAAGQ,KAAK,IAAK;oBACvB;oBACAoF,OAAO,CAACqI,GAAG,CAAC,aAAa,EAAEzN,KAAK,CAAC;kBACnC;gBAAE;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACF,eAGD1B,OAAA;kBAAKM,KAAK,EAAE;oBACVU,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE,eAAe;oBAC/BgM,OAAO,EAAE,MAAM;oBACflI,UAAU,EAAE,qBAAqB;oBACjC3B,YAAY,EAAE,MAAM;oBACpBO,YAAY,EAAE;kBAChB,CAAE;kBAAAtC,QAAA,gBACAtB,OAAA;oBAAKM,KAAK,EAAE;sBACVU,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB0C,GAAG,EAAE;oBACP,CAAE;oBAAArC,QAAA,gBAEAtB,OAAA;sBACEiE,OAAO,EAAEA,CAAA,KAAMqF,gBAAgB,CAACC,YAAY,CAAE;sBAC9CjJ,KAAK,EAAE;wBACLU,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB0C,GAAG,EAAE,QAAQ;wBACbqB,UAAU,EAAE,MAAM;wBAClB6I,MAAM,EAAE,MAAM;wBACdzM,KAAK,EAAEmI,YAAY,CAACC,aAAa,GAAG,SAAS,GAAG,SAAS;wBACzDlG,MAAM,EAAE,SAAS;wBACjB4J,OAAO,EAAE,QAAQ;wBACjB7J,YAAY,EAAE,KAAK;wBACnBnB,UAAU,EAAE,eAAe;wBAC3Bb,QAAQ,EAAE,UAAU;wBACpBmD,UAAU,EAAE;sBACd,CAAE;sBACFjE,YAAY,EAAGuD,CAAC,IAAK;wBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,qBAAqB;sBAC1D,CAAE;sBACFxE,YAAY,EAAGsD,CAAC,IAAK;wBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,MAAM;sBAC3C,CAAE;sBAAA1D,QAAA,gBAEFtB,OAAA,CAAChB,KAAK;wBACJwO,IAAI,EAAE,EAAG;wBACTsD,IAAI,EAAEvH,YAAY,CAACC,aAAa,GAAG,SAAS,GAAG;sBAAO;wBAAAjI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvD,CAAC,eACF1B,OAAA;wBAAAsB,QAAA,EAAOiI,YAAY,CAACwH,cAAc,IAAI;sBAAC;wBAAAxP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzC,CAAC,EAGR6H,YAAY,CAACyH,cAAc,iBAC1BhR,OAAA;sBACEiE,OAAO,EAAEA,CAAA,KAAM0B,eAAe,CAC5BD,YAAY,KAAK6D,YAAY,CAACG,eAAe,GAAG,IAAI,GAAGH,YAAY,CAACG,eACtE,CAAE;sBACFpJ,KAAK,EAAE;wBACLU,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB0C,GAAG,EAAE,QAAQ;wBACbqB,UAAU,EAAE,MAAM;wBAClB6I,MAAM,EAAE,MAAM;wBACdzM,KAAK,EAAEsE,YAAY,KAAK6D,YAAY,CAACG,eAAe,GAAG,SAAS,GAAG,SAAS;wBAC5EpG,MAAM,EAAE,SAAS;wBACjB4J,OAAO,EAAE,QAAQ;wBACjB7J,YAAY,EAAE,KAAK;wBACnBnB,UAAU,EAAE,eAAe;wBAC3Bb,QAAQ,EAAE,UAAU;wBACpBmD,UAAU,EAAE;sBACd,CAAE;sBACFjE,YAAY,EAAGuD,CAAC,IAAK;wBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,qBAAqB;wBACxDlB,CAAC,CAACC,aAAa,CAACzD,KAAK,CAACc,KAAK,GAAG,SAAS;sBACzC,CAAE;sBACFZ,YAAY,EAAGsD,CAAC,IAAK;wBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,MAAM;wBACzClB,CAAC,CAACC,aAAa,CAACzD,KAAK,CAACc,KAAK,GAAGsE,YAAY,KAAK6D,YAAY,CAACG,eAAe,GAAG,SAAS,GAAG,SAAS;sBACrG,CAAE;sBAAApI,QAAA,gBAEFtB,OAAA,CAACjB,aAAa;wBAACyO,IAAI,EAAE;sBAAG;wBAAAjM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC3B1B,OAAA;wBAAAsB,QAAA,EAAOiI,YAAY,CAAC0H,aAAa,IAAI;sBAAC;wBAAA1P,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CACT,eAGD1B,OAAA;sBAAKM,KAAK,EAAE;wBACVU,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB0C,GAAG,EAAE,QAAQ;wBACbvC,KAAK,EAAE,SAAS;wBAChBC,QAAQ,EAAE;sBACZ,CAAE;sBAAAC,QAAA,gBACAtB,OAAA,CAACf,GAAG;wBAACuO,IAAI,EAAE;sBAAG;wBAAAjM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACjB1B,OAAA;wBAAAsB,QAAA,GAAOiI,YAAY,CAAC2H,UAAU,IAAI,CAAC,EAAC,QAAM;sBAAA;wBAAA3P,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGN1B,OAAA;oBAAKM,KAAK,EAAE;sBACVU,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB0C,GAAG,EAAE,MAAM;sBACXtC,QAAQ,EAAE,SAAS;sBACnBD,KAAK,EAAE;oBACT,CAAE;oBAAAE,QAAA,gBACAtB,OAAA;sBAAKM,KAAK,EAAE;wBACVU,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB0C,GAAG,EAAE;sBACP,CAAE;sBAAArC,QAAA,gBACAtB,OAAA,CAACb,KAAK;wBAACqO,IAAI,EAAE;sBAAG;wBAAAjM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnB1B,OAAA;wBAAAsB,QAAA,GAAM,YAAU,EAACiI,YAAY,CAAC4H,cAAc,IAAI,OAAO;sBAAA;wBAAA5P,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5D,CAAC,eAEN1B,OAAA;sBAAKM,KAAK,EAAE;wBACV4M,OAAO,EAAE,gBAAgB;wBACzBlI,UAAU,EAAEuE,YAAY,CAAC6H,MAAM,KAAK,WAAW,GAC3C,wBAAwB,GACxB,0BAA0B;wBAC9BhQ,KAAK,EAAEmI,YAAY,CAAC6H,MAAM,KAAK,WAAW,GAAG,SAAS,GAAG,SAAS;wBAClE/N,YAAY,EAAE,KAAK;wBACnBmB,UAAU,EAAE;sBACd,CAAE;sBAAAlD,QAAA,EACCiI,YAAY,CAAC6H;oBAAM;sBAAA7P,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGLgE,YAAY,KAAK6D,YAAY,CAACG,eAAe,IAAIH,YAAY,CAACyH,cAAc,iBAC3EhR,OAAA;kBAAKM,KAAK,EAAE;oBACVkO,SAAS,EAAE,MAAM;oBACjB6C,UAAU,EAAE,MAAM;oBAClBzC,SAAS,EAAE;kBACb,CAAE;kBAAAtN,QAAA,eACAtB,OAAA,CAACzB,mBAAmB;oBAClB6L,cAAc,EAAEb,YAAY,CAACG,eAAgB;oBAC7C4H,aAAa,EAAE/H,YAAY,CAACyH,cAAe;oBAC3CO,eAAe,EAAC;kBAAO;oBAAAhQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA,GAlZI,gBAAgB6H,YAAY,CAACG,eAAe,EAAE;gBAAAnI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAmZhD,CACN;YAAC,gBACF,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGLsE,kBAAkB,iBACjBhG,OAAA;MAAKM,KAAK,EAAE;QACVqB,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTZ,eAAe,EAAE,oBAAoB;QACrCH,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxB6L,MAAM,EAAE,IAAI;QACZG,OAAO,EAAE;MACX,CAAE;MACFjJ,OAAO,EAAEA,CAAA,KAAMgC,qBAAqB,CAAC,IAAI,CAAE;MAAA3E,QAAA,eAEzCtB,OAAA;QAAKM,KAAK,EAAE;UACVa,eAAe,EAAE,OAAO;UACxBkC,YAAY,EAAE,MAAM;UACpBkK,QAAQ,EAAE,OAAO;UACjBhK,KAAK,EAAE,MAAM;UACbiO,SAAS,EAAE,MAAM;UACjBpO,QAAQ,EAAE,MAAM;UAChB6J,SAAS,EAAE;QACb,CAAE;QACFhJ,OAAO,EAAGH,CAAC,IAAKA,CAAC,CAAC2N,eAAe,CAAC,CAAE;QAAAnQ,QAAA,gBAGlCtB,OAAA;UAAKM,KAAK,EAAE;YACV4M,OAAO,EAAE,QAAQ;YACjBF,YAAY,EAAE,mBAAmB;YACjChM,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAI,QAAA,gBACAtB,OAAA;YAAKM,KAAK,EAAE;cACVU,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpB0C,GAAG,EAAE;YACP,CAAE;YAAArC,QAAA,gBACAtB,OAAA,CAACnB,GAAG;cAAC2O,IAAI,EAAE,EAAG;cAAClN,KAAK,EAAE;gBAAEc,KAAK,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9C1B,OAAA;cAAIM,KAAK,EAAE;gBACT8M,MAAM,EAAE,CAAC;gBACT/L,QAAQ,EAAE,SAAS;gBACnBmD,UAAU,EAAE,KAAK;gBACjBpD,KAAK,EAAE;cACT,CAAE;cAAAE,QAAA,EAAC;YAEH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACN1B,OAAA;YACEiE,OAAO,EAAEA,CAAA,KAAMgC,qBAAqB,CAAC,IAAI,CAAE;YAC3C3F,KAAK,EAAE;cACL0E,UAAU,EAAE,MAAM;cAClB6I,MAAM,EAAE,MAAM;cACdxM,QAAQ,EAAE,QAAQ;cAClBD,KAAK,EAAE,SAAS;cAChBkC,MAAM,EAAE,SAAS;cACjB4J,OAAO,EAAE,SAAS;cAClB7J,YAAY,EAAE,KAAK;cACnBnB,UAAU,EAAE;YACd,CAAE;YACF3B,YAAY,EAAGuD,CAAC,IAAK;cACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAACc,KAAK,GAAG,SAAS;YACzC,CAAE;YACFZ,YAAY,EAAGsD,CAAC,IAAK;cACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAACc,KAAK,GAAG,SAAS;YACzC,CAAE;YAAAE,QAAA,EACH;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN1B,OAAA;UAAKM,KAAK,EAAE;YAAE4M,OAAO,EAAE;UAAS,CAAE;UAAA5L,QAAA,gBAChCtB,OAAA;YAAKM,KAAK,EAAE;cACVU,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpB0C,GAAG,EAAE,SAAS;cACdC,YAAY,EAAE;YAChB,CAAE;YAAAtC,QAAA,GACC,CAAC,MAAM;cACN,MAAMwD,YAAY,GAAG,CAACkB,kBAAkB,CAACqI,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;cAClF,MAAMC,aAAa,GAAG1J,gBAAgB,CAACC,YAAY,CAAC;cACpD,MAAM8K,aAAa,GAAGrB,aAAa,CAACtJ,IAAI;cAExC,oBACEjF,OAAA;gBAAMM,KAAK,EAAE;kBACX0E,UAAU,EAAEuJ,aAAa,CAACvJ,UAAU;kBACpC5D,KAAK,EAAE,OAAO;kBACdC,QAAQ,EAAE,SAAS;kBACnBmD,UAAU,EAAE,KAAK;kBACjB0I,OAAO,EAAE,iBAAiB;kBAC1B7J,YAAY,EAAE,MAAM;kBACpBwM,aAAa,EAAE,WAAW;kBAC1BC,aAAa,EAAE,OAAO;kBACtB9O,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpB0C,GAAG,EAAE;gBACP,CAAE;gBAAArC,QAAA,gBACAtB,OAAA,CAAC4P,aAAa;kBAACpC,IAAI,EAAE,EAAG;kBAACpM,KAAK,EAAC;gBAAO;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACxCoD,YAAY;cAAA;gBAAAvD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAEX,CAAC,EAAE,CAAC,eAEJ1B,OAAA;cAAMM,KAAK,EAAE;gBACX0E,UAAU,EAAE,mDAAmD;gBAC/D5D,KAAK,EAAE,OAAO;gBACd8L,OAAO,EAAE,iBAAiB;gBAC1B7J,YAAY,EAAE,MAAM;gBACpBhC,QAAQ,EAAE,SAAS;gBACnBmD,UAAU,EAAE,KAAK;gBACjBxD,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB0C,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,gBACAtB,OAAA,CAACnB,GAAG;gBAAC2O,IAAI,EAAE;cAAG;gBAAAjM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEnB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEN1B,OAAA;YAAIM,KAAK,EAAE;cACT8M,MAAM,EAAE,YAAY;cACpB/L,QAAQ,EAAE,QAAQ;cAClBmD,UAAU,EAAE,KAAK;cACjBpD,KAAK,EAAE,SAAS;cAChBiM,UAAU,EAAE;YACd,CAAE;YAAA/L,QAAA,EACC0E,kBAAkB,CAAC+E;UAAK;YAAAxJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eAEL1B,OAAA;YAAKM,KAAK,EAAE;cACVc,KAAK,EAAE,SAAS;cAChBC,QAAQ,EAAE,MAAM;cAChBgM,UAAU,EAAE,KAAK;cACjBzJ,YAAY,EAAE;YAChB,CAAE;YAAAtC,QAAA,EACC0E,kBAAkB,CAACuE;UAAO;YAAAhJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,EAGLsE,kBAAkB,CAACiD,WAAW,IAAIjD,kBAAkB,CAACiD,WAAW,CAACvG,MAAM,GAAG,CAAC,iBAC1E1C,OAAA;YAAKM,KAAK,EAAE;cAAEsD,YAAY,EAAE;YAAS,CAAE;YAAAtC,QAAA,eACrCtB,OAAA,CAACsC,YAAY;cACXC,MAAM,EAAEyD,kBAAkB,CAACiD,WAAY;cACvCzG,SAAS,EAAEwD,kBAAkB,CAAC+E,KAAM;cACpCtI,YAAY,EAAGQ,KAAK,IAAK;gBACvBoF,OAAO,CAACqI,GAAG,CAAC,aAAa,EAAEzN,KAAK,CAAC;cACnC;YAAE;cAAA1B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eAED1B,OAAA;YAAKM,KAAK,EAAE;cACVU,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpB0C,GAAG,EAAE,MAAM;cACXtC,QAAQ,EAAE,UAAU;cACpBD,KAAK,EAAE,SAAS;cAChBiQ,UAAU,EAAE,MAAM;cAClBzC,SAAS,EAAE;YACb,CAAE;YAAAtN,QAAA,gBACAtB,OAAA;cAAKM,KAAK,EAAE;gBACVU,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB0C,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,gBACAtB,OAAA,CAAClB,QAAQ;gBAAC0O,IAAI,EAAE;cAAG;gBAAAjM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtB1B,OAAA;gBAAAsB,QAAA,GAAM,aAAW,EAAC,IAAIoK,IAAI,CAAC1F,kBAAkB,CAACuG,UAAU,CAAC,CAACmC,kBAAkB,CAAC,CAAC;cAAA;gBAAAnN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC,EACLsE,kBAAkB,CAAC0L,WAAW,iBAC7B1R,OAAA;cAAAsB,QAAA,GAAK,MACC,EAAC0E,kBAAkB,CAAC0L,WAAW;YAAA;cAAAnQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACiD,GAAA,CAtxDID,aAAuB;EAAA,QACVtG,WAAW,EAwFLE,aAAa;AAAA;AAAAqT,GAAA,GAzFhCjN,aAAuB;AAwxD7B,eAAeA,aAAa;AAAC,IAAArC,EAAA,EAAAoC,GAAA,EAAAkN,GAAA;AAAAC,YAAA,CAAAvP,EAAA;AAAAuP,YAAA,CAAAnN,GAAA;AAAAmN,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}