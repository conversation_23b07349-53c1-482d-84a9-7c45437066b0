{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M5 12h14\",\n  key: \"1ays0h\"\n}], [\"path\", {\n  d: \"M12 5v14\",\n  key: \"s699le\"\n}]];\nconst Plus = createLucideIcon(\"plus\", __iconNode);\nexport { __iconNode, Plus as default };\n//# sourceMappingURL=plus.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}