{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m12.5 17-.5-1-.5 1h1z\",\n  key: \"3me087\"\n}], [\"path\", {\n  d: \"M15 22a1 1 0 0 0 1-1v-1a2 2 0 0 0 1.56-3.25 8 8 0 1 0-11.12 0A2 2 0 0 0 8 20v1a1 1 0 0 0 1 1z\",\n  key: \"1o5pge\"\n}], [\"circle\", {\n  cx: \"15\",\n  cy: \"12\",\n  r: \"1\",\n  key: \"1tmaij\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"12\",\n  r: \"1\",\n  key: \"1vctgf\"\n}]];\nconst Skull = createLucideIcon(\"skull\", __iconNode);\nexport { __iconNode, Skull as default };\n//# sourceMappingURL=skull.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}