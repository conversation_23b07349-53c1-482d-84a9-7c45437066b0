{"ast": null, "code": "var _jsxFileName = \"D:\\\\capstone-e-bulletin-reactjs-proj\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\components\\\\common\\\\ImageLightbox.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { X, ChevronLeft, ChevronRight, ZoomIn, ZoomOut } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImageLightbox = ({\n  images,\n  initialIndex,\n  isOpen,\n  onClose,\n  altPrefix = 'Image'\n}) => {\n  _s();\n  const [currentIndex, setCurrentIndex] = useState(initialIndex);\n  const [isZoomed, setIsZoomed] = useState(false);\n  const [imageLoaded, setImageLoaded] = useState(false);\n\n  // Reset state when lightbox opens\n  useEffect(() => {\n    if (isOpen) {\n      setCurrentIndex(initialIndex);\n      setIsZoomed(false);\n      setImageLoaded(false);\n      document.body.style.overflow = 'hidden'; // Prevent background scrolling\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, initialIndex]);\n\n  // Keyboard navigation\n  const handleKeyDown = useCallback(e => {\n    if (!isOpen) return;\n    switch (e.key) {\n      case 'Escape':\n        onClose();\n        break;\n      case 'ArrowLeft':\n        goToPrevious();\n        break;\n      case 'ArrowRight':\n        goToNext();\n        break;\n    }\n  }, [isOpen, onClose]);\n  useEffect(() => {\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [handleKeyDown]);\n  const goToNext = () => {\n    setCurrentIndex(prev => (prev + 1) % images.length);\n    setImageLoaded(false);\n    setIsZoomed(false);\n  };\n  const goToPrevious = () => {\n    setCurrentIndex(prev => (prev - 1 + images.length) % images.length);\n    setImageLoaded(false);\n    setIsZoomed(false);\n  };\n  const goToImage = index => {\n    setCurrentIndex(index);\n    setImageLoaded(false);\n    setIsZoomed(false);\n  };\n  const toggleZoom = () => {\n    setIsZoomed(!isZoomed);\n  };\n  if (!isOpen) return null;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: 0,\n      left: 0,\n      right: 0,\n      bottom: 0,\n      backgroundColor: 'rgba(0, 0, 0, 0.9)',\n      zIndex: 9999,\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      padding: '2rem'\n    },\n    onClick: onClose,\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onClose,\n      style: {\n        position: 'absolute',\n        top: '1rem',\n        right: '1rem',\n        background: 'rgba(255, 255, 255, 0.1)',\n        border: 'none',\n        borderRadius: '50%',\n        width: '48px',\n        height: '48px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        color: 'white',\n        cursor: 'pointer',\n        transition: 'all 0.2s ease',\n        zIndex: 10001\n      },\n      onMouseEnter: e => {\n        e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';\n      },\n      onMouseLeave: e => {\n        e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';\n      },\n      children: /*#__PURE__*/_jsxDEV(X, {\n        size: 24\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: '1rem',\n        left: '1rem',\n        background: 'rgba(0, 0, 0, 0.7)',\n        color: 'white',\n        padding: '0.5rem 1rem',\n        borderRadius: '20px',\n        fontSize: '0.875rem',\n        fontWeight: '500',\n        zIndex: 10001\n      },\n      children: [currentIndex + 1, \" / \", images.length]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: e => {\n        e.stopPropagation();\n        toggleZoom();\n      },\n      style: {\n        position: 'absolute',\n        top: '1rem',\n        left: '50%',\n        transform: 'translateX(-50%)',\n        background: 'rgba(255, 255, 255, 0.1)',\n        border: 'none',\n        borderRadius: '50%',\n        width: '48px',\n        height: '48px',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        color: 'white',\n        cursor: 'pointer',\n        transition: 'all 0.2s ease',\n        zIndex: 10001\n      },\n      onMouseEnter: e => {\n        e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';\n      },\n      onMouseLeave: e => {\n        e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';\n      },\n      children: isZoomed ? /*#__PURE__*/_jsxDEV(ZoomOut, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 21\n      }, this) : /*#__PURE__*/_jsxDEV(ZoomIn, {\n        size: 20\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 45\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'relative',\n        maxWidth: '90vw',\n        maxHeight: '80vh',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      onClick: e => e.stopPropagation(),\n      children: [images.length > 1 && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: goToPrevious,\n        style: {\n          position: 'absolute',\n          left: '-4rem',\n          top: '50%',\n          transform: 'translateY(-50%)',\n          background: 'rgba(255, 255, 255, 0.1)',\n          border: 'none',\n          borderRadius: '50%',\n          width: '56px',\n          height: '56px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          cursor: 'pointer',\n          transition: 'all 0.2s ease',\n          zIndex: 10001\n        },\n        onMouseEnter: e => {\n          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';\n          e.currentTarget.style.transform = 'translateY(-50%) scale(1.1)';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';\n          e.currentTarget.style.transform = 'translateY(-50%) scale(1)';\n        },\n        children: /*#__PURE__*/_jsxDEV(ChevronLeft, {\n          size: 28\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 227,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative',\n          overflow: isZoomed ? 'auto' : 'hidden',\n          borderRadius: '12px',\n          maxWidth: '100%',\n          maxHeight: '100%'\n        },\n        children: [!imageLoaded && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            background: 'rgba(0, 0, 0, 0.5)',\n            color: 'white',\n            fontSize: '1.2rem'\n          },\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 242,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n          src: images[currentIndex],\n          alt: `${altPrefix} ${currentIndex + 1}`,\n          style: {\n            maxWidth: isZoomed ? 'none' : '90vw',\n            maxHeight: isZoomed ? 'none' : '80vh',\n            width: isZoomed ? '150%' : 'auto',\n            height: 'auto',\n            borderRadius: '12px',\n            transition: 'all 0.3s ease',\n            cursor: isZoomed ? 'grab' : 'zoom-in',\n            opacity: imageLoaded ? 1 : 0\n          },\n          onLoad: () => setImageLoaded(true),\n          onClick: toggleZoom,\n          draggable: false\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 261,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this), images.length > 1 && /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: goToNext,\n        style: {\n          position: 'absolute',\n          right: '-4rem',\n          top: '50%',\n          transform: 'translateY(-50%)',\n          background: 'rgba(255, 255, 255, 0.1)',\n          border: 'none',\n          borderRadius: '50%',\n          width: '56px',\n          height: '56px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          cursor: 'pointer',\n          transition: 'all 0.2s ease',\n          zIndex: 10001\n        },\n        onMouseEnter: e => {\n          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';\n          e.currentTarget.style.transform = 'translateY(-50%) scale(1.1)';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';\n          e.currentTarget.style.transform = 'translateY(-50%) scale(1)';\n        },\n        children: /*#__PURE__*/_jsxDEV(ChevronRight, {\n          size: 28\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this), images.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        bottom: '2rem',\n        left: '50%',\n        transform: 'translateX(-50%)',\n        display: 'flex',\n        gap: '0.5rem',\n        background: 'rgba(0, 0, 0, 0.7)',\n        padding: '1rem',\n        borderRadius: '12px',\n        maxWidth: '90vw',\n        overflowX: 'auto'\n      },\n      onClick: e => e.stopPropagation(),\n      children: images.map((image, index) => /*#__PURE__*/_jsxDEV(\"img\", {\n        src: image,\n        alt: `${altPrefix} ${index + 1} thumbnail`,\n        style: {\n          width: '60px',\n          height: '60px',\n          objectFit: 'cover',\n          borderRadius: '8px',\n          cursor: 'pointer',\n          opacity: index === currentIndex ? 1 : 0.6,\n          border: index === currentIndex ? '2px solid white' : '2px solid transparent',\n          transition: 'all 0.2s ease'\n        },\n        onClick: () => goToImage(index),\n        onMouseEnter: e => {\n          if (index !== currentIndex) {\n            e.currentTarget.style.opacity = '0.8';\n          }\n        },\n        onMouseLeave: e => {\n          if (index !== currentIndex) {\n            e.currentTarget.style.opacity = '0.6';\n          }\n        }\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 13\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 318,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this);\n};\n_s(ImageLightbox, \"r+YnGvEpx51Jo2FEh2/XXE+LOEU=\");\n_c = ImageLightbox;\nexport default ImageLightbox;\nvar _c;\n$RefreshReg$(_c, \"ImageLightbox\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "X", "ChevronLeft", "ChevronRight", "ZoomIn", "ZoomOut", "jsxDEV", "_jsxDEV", "ImageLightbox", "images", "initialIndex", "isOpen", "onClose", "altPrefix", "_s", "currentIndex", "setCurrentIndex", "isZoomed", "set<PERSON>s<PERSON><PERSON>ed", "imageLoaded", "setImageLoaded", "document", "body", "style", "overflow", "handleKeyDown", "e", "key", "goToPrevious", "goToNext", "addEventListener", "removeEventListener", "prev", "length", "goToImage", "index", "toggleZoom", "position", "top", "left", "right", "bottom", "backgroundColor", "zIndex", "display", "alignItems", "justifyContent", "padding", "onClick", "children", "background", "border", "borderRadius", "width", "height", "color", "cursor", "transition", "onMouseEnter", "currentTarget", "onMouseLeave", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontSize", "fontWeight", "stopPropagation", "transform", "max<PERSON><PERSON><PERSON>", "maxHeight", "src", "alt", "opacity", "onLoad", "draggable", "gap", "overflowX", "map", "image", "objectFit", "_c", "$RefreshReg$"], "sources": ["D:/capstone-e-bulletin-reactjs-proj/FRONT-VCBA-E-BULLETIN-BOARD/src/components/common/ImageLightbox.tsx"], "sourcesContent": ["import React, { useState, useEffect, useCallback } from 'react';\nimport { X, ChevronLeft, ChevronRight, ZoomIn, ZoomOut } from 'lucide-react';\n\ninterface ImageLightboxProps {\n  images: string[];\n  initialIndex: number;\n  isOpen: boolean;\n  onClose: () => void;\n  altPrefix?: string;\n}\n\nconst ImageLightbox: React.FC<ImageLightboxProps> = ({\n  images,\n  initialIndex,\n  isOpen,\n  onClose,\n  altPrefix = 'Image'\n}) => {\n  const [currentIndex, setCurrentIndex] = useState(initialIndex);\n  const [isZoomed, setIsZoomed] = useState(false);\n  const [imageLoaded, setImageLoaded] = useState(false);\n\n  // Reset state when lightbox opens\n  useEffect(() => {\n    if (isOpen) {\n      setCurrentIndex(initialIndex);\n      setIsZoomed(false);\n      setImageLoaded(false);\n      document.body.style.overflow = 'hidden'; // Prevent background scrolling\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen, initialIndex]);\n\n  // Keyboard navigation\n  const handleKeyDown = useCallback((e: KeyboardEvent) => {\n    if (!isOpen) return;\n\n    switch (e.key) {\n      case 'Escape':\n        onClose();\n        break;\n      case 'ArrowLeft':\n        goToPrevious();\n        break;\n      case 'ArrowRight':\n        goToNext();\n        break;\n    }\n  }, [isOpen, onClose]);\n\n  useEffect(() => {\n    document.addEventListener('keydown', handleKeyDown);\n    return () => document.removeEventListener('keydown', handleKeyDown);\n  }, [handleKeyDown]);\n\n  const goToNext = () => {\n    setCurrentIndex((prev) => (prev + 1) % images.length);\n    setImageLoaded(false);\n    setIsZoomed(false);\n  };\n\n  const goToPrevious = () => {\n    setCurrentIndex((prev) => (prev - 1 + images.length) % images.length);\n    setImageLoaded(false);\n    setIsZoomed(false);\n  };\n\n  const goToImage = (index: number) => {\n    setCurrentIndex(index);\n    setImageLoaded(false);\n    setIsZoomed(false);\n  };\n\n  const toggleZoom = () => {\n    setIsZoomed(!isZoomed);\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <div\n      style={{\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: 'rgba(0, 0, 0, 0.9)',\n        zIndex: 9999,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        padding: '2rem'\n      }}\n      onClick={onClose}\n    >\n      {/* Close Button */}\n      <button\n        onClick={onClose}\n        style={{\n          position: 'absolute',\n          top: '1rem',\n          right: '1rem',\n          background: 'rgba(255, 255, 255, 0.1)',\n          border: 'none',\n          borderRadius: '50%',\n          width: '48px',\n          height: '48px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          cursor: 'pointer',\n          transition: 'all 0.2s ease',\n          zIndex: 10001\n        }}\n        onMouseEnter={(e) => {\n          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';\n        }}\n        onMouseLeave={(e) => {\n          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';\n        }}\n      >\n        <X size={24} />\n      </button>\n\n      {/* Image Counter */}\n      <div\n        style={{\n          position: 'absolute',\n          top: '1rem',\n          left: '1rem',\n          background: 'rgba(0, 0, 0, 0.7)',\n          color: 'white',\n          padding: '0.5rem 1rem',\n          borderRadius: '20px',\n          fontSize: '0.875rem',\n          fontWeight: '500',\n          zIndex: 10001\n        }}\n      >\n        {currentIndex + 1} / {images.length}\n      </div>\n\n      {/* Zoom Button */}\n      <button\n        onClick={(e) => {\n          e.stopPropagation();\n          toggleZoom();\n        }}\n        style={{\n          position: 'absolute',\n          top: '1rem',\n          left: '50%',\n          transform: 'translateX(-50%)',\n          background: 'rgba(255, 255, 255, 0.1)',\n          border: 'none',\n          borderRadius: '50%',\n          width: '48px',\n          height: '48px',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          cursor: 'pointer',\n          transition: 'all 0.2s ease',\n          zIndex: 10001\n        }}\n        onMouseEnter={(e) => {\n          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';\n        }}\n        onMouseLeave={(e) => {\n          e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';\n        }}\n      >\n        {isZoomed ? <ZoomOut size={20} /> : <ZoomIn size={20} />}\n      </button>\n\n      {/* Main Image Container */}\n      <div\n        style={{\n          position: 'relative',\n          maxWidth: '90vw',\n          maxHeight: '80vh',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center'\n        }}\n        onClick={(e) => e.stopPropagation()}\n      >\n        {/* Previous Button */}\n        {images.length > 1 && (\n          <button\n            onClick={goToPrevious}\n            style={{\n              position: 'absolute',\n              left: '-4rem',\n              top: '50%',\n              transform: 'translateY(-50%)',\n              background: 'rgba(255, 255, 255, 0.1)',\n              border: 'none',\n              borderRadius: '50%',\n              width: '56px',\n              height: '56px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              cursor: 'pointer',\n              transition: 'all 0.2s ease',\n              zIndex: 10001\n            }}\n            onMouseEnter={(e) => {\n              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';\n              e.currentTarget.style.transform = 'translateY(-50%) scale(1.1)';\n            }}\n            onMouseLeave={(e) => {\n              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';\n              e.currentTarget.style.transform = 'translateY(-50%) scale(1)';\n            }}\n          >\n            <ChevronLeft size={28} />\n          </button>\n        )}\n\n        {/* Main Image */}\n        <div\n          style={{\n            position: 'relative',\n            overflow: isZoomed ? 'auto' : 'hidden',\n            borderRadius: '12px',\n            maxWidth: '100%',\n            maxHeight: '100%'\n          }}\n        >\n          {!imageLoaded && (\n            <div\n              style={{\n                position: 'absolute',\n                top: 0,\n                left: 0,\n                right: 0,\n                bottom: 0,\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                background: 'rgba(0, 0, 0, 0.5)',\n                color: 'white',\n                fontSize: '1.2rem'\n              }}\n            >\n              Loading...\n            </div>\n          )}\n          \n          <img\n            src={images[currentIndex]}\n            alt={`${altPrefix} ${currentIndex + 1}`}\n            style={{\n              maxWidth: isZoomed ? 'none' : '90vw',\n              maxHeight: isZoomed ? 'none' : '80vh',\n              width: isZoomed ? '150%' : 'auto',\n              height: 'auto',\n              borderRadius: '12px',\n              transition: 'all 0.3s ease',\n              cursor: isZoomed ? 'grab' : 'zoom-in',\n              opacity: imageLoaded ? 1 : 0\n            }}\n            onLoad={() => setImageLoaded(true)}\n            onClick={toggleZoom}\n            draggable={false}\n          />\n        </div>\n\n        {/* Next Button */}\n        {images.length > 1 && (\n          <button\n            onClick={goToNext}\n            style={{\n              position: 'absolute',\n              right: '-4rem',\n              top: '50%',\n              transform: 'translateY(-50%)',\n              background: 'rgba(255, 255, 255, 0.1)',\n              border: 'none',\n              borderRadius: '50%',\n              width: '56px',\n              height: '56px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              cursor: 'pointer',\n              transition: 'all 0.2s ease',\n              zIndex: 10001\n            }}\n            onMouseEnter={(e) => {\n              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)';\n              e.currentTarget.style.transform = 'translateY(-50%) scale(1.1)';\n            }}\n            onMouseLeave={(e) => {\n              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)';\n              e.currentTarget.style.transform = 'translateY(-50%) scale(1)';\n            }}\n          >\n            <ChevronRight size={28} />\n          </button>\n        )}\n      </div>\n\n      {/* Thumbnail Navigation */}\n      {images.length > 1 && (\n        <div\n          style={{\n            position: 'absolute',\n            bottom: '2rem',\n            left: '50%',\n            transform: 'translateX(-50%)',\n            display: 'flex',\n            gap: '0.5rem',\n            background: 'rgba(0, 0, 0, 0.7)',\n            padding: '1rem',\n            borderRadius: '12px',\n            maxWidth: '90vw',\n            overflowX: 'auto'\n          }}\n          onClick={(e) => e.stopPropagation()}\n        >\n          {images.map((image, index) => (\n            <img\n              key={index}\n              src={image}\n              alt={`${altPrefix} ${index + 1} thumbnail`}\n              style={{\n                width: '60px',\n                height: '60px',\n                objectFit: 'cover',\n                borderRadius: '8px',\n                cursor: 'pointer',\n                opacity: index === currentIndex ? 1 : 0.6,\n                border: index === currentIndex ? '2px solid white' : '2px solid transparent',\n                transition: 'all 0.2s ease'\n              }}\n              onClick={() => goToImage(index)}\n              onMouseEnter={(e) => {\n                if (index !== currentIndex) {\n                  e.currentTarget.style.opacity = '0.8';\n                }\n              }}\n              onMouseLeave={(e) => {\n                if (index !== currentIndex) {\n                  e.currentTarget.style.opacity = '0.6';\n                }\n              }}\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ImageLightbox;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,CAAC,EAAEC,WAAW,EAAEC,YAAY,EAAEC,MAAM,EAAEC,OAAO,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAU7E,MAAMC,aAA2C,GAAGA,CAAC;EACnDC,MAAM;EACNC,YAAY;EACZC,MAAM;EACNC,OAAO;EACPC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGlB,QAAQ,CAACY,YAAY,CAAC;EAC9D,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIY,MAAM,EAAE;MACVK,eAAe,CAACN,YAAY,CAAC;MAC7BQ,WAAW,CAAC,KAAK,CAAC;MAClBE,cAAc,CAAC,KAAK,CAAC;MACrBC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,QAAQ,CAAC,CAAC;IAC3C,CAAC,MAAM;MACLH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC;IAEA,OAAO,MAAM;MACXH,QAAQ,CAACC,IAAI,CAACC,KAAK,CAACC,QAAQ,GAAG,OAAO;IACxC,CAAC;EACH,CAAC,EAAE,CAACb,MAAM,EAAED,YAAY,CAAC,CAAC;;EAE1B;EACA,MAAMe,aAAa,GAAGzB,WAAW,CAAE0B,CAAgB,IAAK;IACtD,IAAI,CAACf,MAAM,EAAE;IAEb,QAAQe,CAAC,CAACC,GAAG;MACX,KAAK,QAAQ;QACXf,OAAO,CAAC,CAAC;QACT;MACF,KAAK,WAAW;QACdgB,YAAY,CAAC,CAAC;QACd;MACF,KAAK,YAAY;QACfC,QAAQ,CAAC,CAAC;QACV;IACJ;EACF,CAAC,EAAE,CAAClB,MAAM,EAAEC,OAAO,CAAC,CAAC;EAErBb,SAAS,CAAC,MAAM;IACdsB,QAAQ,CAACS,gBAAgB,CAAC,SAAS,EAAEL,aAAa,CAAC;IACnD,OAAO,MAAMJ,QAAQ,CAACU,mBAAmB,CAAC,SAAS,EAAEN,aAAa,CAAC;EACrE,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAEnB,MAAMI,QAAQ,GAAGA,CAAA,KAAM;IACrBb,eAAe,CAAEgB,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIvB,MAAM,CAACwB,MAAM,CAAC;IACrDb,cAAc,CAAC,KAAK,CAAC;IACrBF,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMU,YAAY,GAAGA,CAAA,KAAM;IACzBZ,eAAe,CAAEgB,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,GAAGvB,MAAM,CAACwB,MAAM,IAAIxB,MAAM,CAACwB,MAAM,CAAC;IACrEb,cAAc,CAAC,KAAK,CAAC;IACrBF,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMgB,SAAS,GAAIC,KAAa,IAAK;IACnCnB,eAAe,CAACmB,KAAK,CAAC;IACtBf,cAAc,CAAC,KAAK,CAAC;IACrBF,WAAW,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,MAAMkB,UAAU,GAAGA,CAAA,KAAM;IACvBlB,WAAW,CAAC,CAACD,QAAQ,CAAC;EACxB,CAAC;EAED,IAAI,CAACN,MAAM,EAAE,OAAO,IAAI;EAExB,oBACEJ,OAAA;IACEgB,KAAK,EAAE;MACLc,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,CAAC;MACNC,IAAI,EAAE,CAAC;MACPC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,eAAe,EAAE,oBAAoB;MACrCC,MAAM,EAAE,IAAI;MACZC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,OAAO,EAAE;IACX,CAAE;IACFC,OAAO,EAAEpC,OAAQ;IAAAqC,QAAA,gBAGjB1C,OAAA;MACEyC,OAAO,EAAEpC,OAAQ;MACjBW,KAAK,EAAE;QACLc,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,MAAM;QACXE,KAAK,EAAE,MAAM;QACbU,UAAU,EAAE,0BAA0B;QACtCC,MAAM,EAAE,MAAM;QACdC,YAAY,EAAE,KAAK;QACnBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdV,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBS,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,SAAS;QACjBC,UAAU,EAAE,eAAe;QAC3Bd,MAAM,EAAE;MACV,CAAE;MACFe,YAAY,EAAGhC,CAAC,IAAK;QACnBA,CAAC,CAACiC,aAAa,CAACpC,KAAK,CAAC2B,UAAU,GAAG,0BAA0B;MAC/D,CAAE;MACFU,YAAY,EAAGlC,CAAC,IAAK;QACnBA,CAAC,CAACiC,aAAa,CAACpC,KAAK,CAAC2B,UAAU,GAAG,0BAA0B;MAC/D,CAAE;MAAAD,QAAA,eAEF1C,OAAA,CAACN,CAAC;QAAC4D,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACT,CAAC,eAGT1D,OAAA;MACEgB,KAAK,EAAE;QACLc,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,MAAM;QACXC,IAAI,EAAE,MAAM;QACZW,UAAU,EAAE,oBAAoB;QAChCK,KAAK,EAAE,OAAO;QACdR,OAAO,EAAE,aAAa;QACtBK,YAAY,EAAE,MAAM;QACpBc,QAAQ,EAAE,UAAU;QACpBC,UAAU,EAAE,KAAK;QACjBxB,MAAM,EAAE;MACV,CAAE;MAAAM,QAAA,GAEDlC,YAAY,GAAG,CAAC,EAAC,KAAG,EAACN,MAAM,CAACwB,MAAM;IAAA;MAAA6B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChC,CAAC,eAGN1D,OAAA;MACEyC,OAAO,EAAGtB,CAAC,IAAK;QACdA,CAAC,CAAC0C,eAAe,CAAC,CAAC;QACnBhC,UAAU,CAAC,CAAC;MACd,CAAE;MACFb,KAAK,EAAE;QACLc,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,MAAM;QACXC,IAAI,EAAE,KAAK;QACX8B,SAAS,EAAE,kBAAkB;QAC7BnB,UAAU,EAAE,0BAA0B;QACtCC,MAAM,EAAE,MAAM;QACdC,YAAY,EAAE,KAAK;QACnBC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdV,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBS,KAAK,EAAE,OAAO;QACdC,MAAM,EAAE,SAAS;QACjBC,UAAU,EAAE,eAAe;QAC3Bd,MAAM,EAAE;MACV,CAAE;MACFe,YAAY,EAAGhC,CAAC,IAAK;QACnBA,CAAC,CAACiC,aAAa,CAACpC,KAAK,CAAC2B,UAAU,GAAG,0BAA0B;MAC/D,CAAE;MACFU,YAAY,EAAGlC,CAAC,IAAK;QACnBA,CAAC,CAACiC,aAAa,CAACpC,KAAK,CAAC2B,UAAU,GAAG,0BAA0B;MAC/D,CAAE;MAAAD,QAAA,EAEDhC,QAAQ,gBAAGV,OAAA,CAACF,OAAO;QAACwD,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,gBAAG1D,OAAA,CAACH,MAAM;QAACyD,IAAI,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,eAGT1D,OAAA;MACEgB,KAAK,EAAE;QACLc,QAAQ,EAAE,UAAU;QACpBiC,QAAQ,EAAE,MAAM;QAChBC,SAAS,EAAE,MAAM;QACjB3B,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE;MAClB,CAAE;MACFE,OAAO,EAAGtB,CAAC,IAAKA,CAAC,CAAC0C,eAAe,CAAC,CAAE;MAAAnB,QAAA,GAGnCxC,MAAM,CAACwB,MAAM,GAAG,CAAC,iBAChB1B,OAAA;QACEyC,OAAO,EAAEpB,YAAa;QACtBL,KAAK,EAAE;UACLc,QAAQ,EAAE,UAAU;UACpBE,IAAI,EAAE,OAAO;UACbD,GAAG,EAAE,KAAK;UACV+B,SAAS,EAAE,kBAAkB;UAC7BnB,UAAU,EAAE,0BAA0B;UACtCC,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdV,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBS,KAAK,EAAE,OAAO;UACdC,MAAM,EAAE,SAAS;UACjBC,UAAU,EAAE,eAAe;UAC3Bd,MAAM,EAAE;QACV,CAAE;QACFe,YAAY,EAAGhC,CAAC,IAAK;UACnBA,CAAC,CAACiC,aAAa,CAACpC,KAAK,CAAC2B,UAAU,GAAG,0BAA0B;UAC7DxB,CAAC,CAACiC,aAAa,CAACpC,KAAK,CAAC8C,SAAS,GAAG,6BAA6B;QACjE,CAAE;QACFT,YAAY,EAAGlC,CAAC,IAAK;UACnBA,CAAC,CAACiC,aAAa,CAACpC,KAAK,CAAC2B,UAAU,GAAG,0BAA0B;UAC7DxB,CAAC,CAACiC,aAAa,CAACpC,KAAK,CAAC8C,SAAS,GAAG,2BAA2B;QAC/D,CAAE;QAAApB,QAAA,eAEF1C,OAAA,CAACL,WAAW;UAAC2D,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB,CACT,eAGD1D,OAAA;QACEgB,KAAK,EAAE;UACLc,QAAQ,EAAE,UAAU;UACpBb,QAAQ,EAAEP,QAAQ,GAAG,MAAM,GAAG,QAAQ;UACtCmC,YAAY,EAAE,MAAM;UACpBkB,QAAQ,EAAE,MAAM;UAChBC,SAAS,EAAE;QACb,CAAE;QAAAtB,QAAA,GAED,CAAC9B,WAAW,iBACXZ,OAAA;UACEgB,KAAK,EAAE;YACLc,QAAQ,EAAE,UAAU;YACpBC,GAAG,EAAE,CAAC;YACNC,IAAI,EAAE,CAAC;YACPC,KAAK,EAAE,CAAC;YACRC,MAAM,EAAE,CAAC;YACTG,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE,QAAQ;YACxBI,UAAU,EAAE,oBAAoB;YAChCK,KAAK,EAAE,OAAO;YACdW,QAAQ,EAAE;UACZ,CAAE;UAAAjB,QAAA,EACH;QAED;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,eAED1D,OAAA;UACEiE,GAAG,EAAE/D,MAAM,CAACM,YAAY,CAAE;UAC1B0D,GAAG,EAAE,GAAG5D,SAAS,IAAIE,YAAY,GAAG,CAAC,EAAG;UACxCQ,KAAK,EAAE;YACL+C,QAAQ,EAAErD,QAAQ,GAAG,MAAM,GAAG,MAAM;YACpCsD,SAAS,EAAEtD,QAAQ,GAAG,MAAM,GAAG,MAAM;YACrCoC,KAAK,EAAEpC,QAAQ,GAAG,MAAM,GAAG,MAAM;YACjCqC,MAAM,EAAE,MAAM;YACdF,YAAY,EAAE,MAAM;YACpBK,UAAU,EAAE,eAAe;YAC3BD,MAAM,EAAEvC,QAAQ,GAAG,MAAM,GAAG,SAAS;YACrCyD,OAAO,EAAEvD,WAAW,GAAG,CAAC,GAAG;UAC7B,CAAE;UACFwD,MAAM,EAAEA,CAAA,KAAMvD,cAAc,CAAC,IAAI,CAAE;UACnC4B,OAAO,EAAEZ,UAAW;UACpBwC,SAAS,EAAE;QAAM;UAAAd,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,EAGLxD,MAAM,CAACwB,MAAM,GAAG,CAAC,iBAChB1B,OAAA;QACEyC,OAAO,EAAEnB,QAAS;QAClBN,KAAK,EAAE;UACLc,QAAQ,EAAE,UAAU;UACpBG,KAAK,EAAE,OAAO;UACdF,GAAG,EAAE,KAAK;UACV+B,SAAS,EAAE,kBAAkB;UAC7BnB,UAAU,EAAE,0BAA0B;UACtCC,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,KAAK;UACnBC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdV,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBS,KAAK,EAAE,OAAO;UACdC,MAAM,EAAE,SAAS;UACjBC,UAAU,EAAE,eAAe;UAC3Bd,MAAM,EAAE;QACV,CAAE;QACFe,YAAY,EAAGhC,CAAC,IAAK;UACnBA,CAAC,CAACiC,aAAa,CAACpC,KAAK,CAAC2B,UAAU,GAAG,0BAA0B;UAC7DxB,CAAC,CAACiC,aAAa,CAACpC,KAAK,CAAC8C,SAAS,GAAG,6BAA6B;QACjE,CAAE;QACFT,YAAY,EAAGlC,CAAC,IAAK;UACnBA,CAAC,CAACiC,aAAa,CAACpC,KAAK,CAAC2B,UAAU,GAAG,0BAA0B;UAC7DxB,CAAC,CAACiC,aAAa,CAACpC,KAAK,CAAC8C,SAAS,GAAG,2BAA2B;QAC/D,CAAE;QAAApB,QAAA,eAEF1C,OAAA,CAACJ,YAAY;UAAC0D,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLxD,MAAM,CAACwB,MAAM,GAAG,CAAC,iBAChB1B,OAAA;MACEgB,KAAK,EAAE;QACLc,QAAQ,EAAE,UAAU;QACpBI,MAAM,EAAE,MAAM;QACdF,IAAI,EAAE,KAAK;QACX8B,SAAS,EAAE,kBAAkB;QAC7BzB,OAAO,EAAE,MAAM;QACfiC,GAAG,EAAE,QAAQ;QACb3B,UAAU,EAAE,oBAAoB;QAChCH,OAAO,EAAE,MAAM;QACfK,YAAY,EAAE,MAAM;QACpBkB,QAAQ,EAAE,MAAM;QAChBQ,SAAS,EAAE;MACb,CAAE;MACF9B,OAAO,EAAGtB,CAAC,IAAKA,CAAC,CAAC0C,eAAe,CAAC,CAAE;MAAAnB,QAAA,EAEnCxC,MAAM,CAACsE,GAAG,CAAC,CAACC,KAAK,EAAE7C,KAAK,kBACvB5B,OAAA;QAEEiE,GAAG,EAAEQ,KAAM;QACXP,GAAG,EAAE,GAAG5D,SAAS,IAAIsB,KAAK,GAAG,CAAC,YAAa;QAC3CZ,KAAK,EAAE;UACL8B,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACd2B,SAAS,EAAE,OAAO;UAClB7B,YAAY,EAAE,KAAK;UACnBI,MAAM,EAAE,SAAS;UACjBkB,OAAO,EAAEvC,KAAK,KAAKpB,YAAY,GAAG,CAAC,GAAG,GAAG;UACzCoC,MAAM,EAAEhB,KAAK,KAAKpB,YAAY,GAAG,iBAAiB,GAAG,uBAAuB;UAC5E0C,UAAU,EAAE;QACd,CAAE;QACFT,OAAO,EAAEA,CAAA,KAAMd,SAAS,CAACC,KAAK,CAAE;QAChCuB,YAAY,EAAGhC,CAAC,IAAK;UACnB,IAAIS,KAAK,KAAKpB,YAAY,EAAE;YAC1BW,CAAC,CAACiC,aAAa,CAACpC,KAAK,CAACmD,OAAO,GAAG,KAAK;UACvC;QACF,CAAE;QACFd,YAAY,EAAGlC,CAAC,IAAK;UACnB,IAAIS,KAAK,KAAKpB,YAAY,EAAE;YAC1BW,CAAC,CAACiC,aAAa,CAACpC,KAAK,CAACmD,OAAO,GAAG,KAAK;UACvC;QACF;MAAE,GAvBGvC,KAAK;QAAA2B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAwBX,CACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACnD,EAAA,CAlWIN,aAA2C;AAAA0E,EAAA,GAA3C1E,aAA2C;AAoWjD,eAAeA,aAAa;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}