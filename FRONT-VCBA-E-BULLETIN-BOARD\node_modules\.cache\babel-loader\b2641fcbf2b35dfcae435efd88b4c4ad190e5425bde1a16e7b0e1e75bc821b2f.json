{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10.513 4.856 13.12 2.17a.5.5 0 0 1 .86.46l-1.377 4.317\",\n  key: \"193nxd\"\n}], [\"path\", {\n  d: \"M15.656 10H20a1 1 0 0 1 .78 1.63l-1.72 1.773\",\n  key: \"27a7lr\"\n}], [\"path\", {\n  d: \"M16.273 16.273 10.88 21.83a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14H4a1 1 0 0 1-.78-1.63l4.507-4.643\",\n  key: \"1e0qe9\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}]];\nconst ZapOff = createLucideIcon(\"zap-off\", __iconNode);\nexport { __iconNode, ZapOff as default };\n//# sourceMappingURL=zap-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}