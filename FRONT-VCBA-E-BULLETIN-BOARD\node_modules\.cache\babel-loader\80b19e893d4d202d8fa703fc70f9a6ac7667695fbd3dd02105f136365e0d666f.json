{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M14 17H5\",\n  key: \"gfn3mx\"\n}], [\"path\", {\n  d: \"M19 7h-9\",\n  key: \"6i9tg\"\n}], [\"circle\", {\n  cx: \"17\",\n  cy: \"17\",\n  r: \"3\",\n  key: \"18b49y\"\n}], [\"circle\", {\n  cx: \"7\",\n  cy: \"7\",\n  r: \"3\",\n  key: \"dfmy0x\"\n}]];\nconst Settings2 = createLucideIcon(\"settings-2\", __iconNode);\nexport { __iconNode, Settings2 as default };\n//# sourceMappingURL=settings-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}