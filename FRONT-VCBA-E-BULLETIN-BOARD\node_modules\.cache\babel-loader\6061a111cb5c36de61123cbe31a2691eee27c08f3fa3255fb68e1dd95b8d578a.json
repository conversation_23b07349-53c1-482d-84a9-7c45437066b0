{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12.034 12.681a.498.498 0 0 1 .647-.647l9 3.5a.5.5 0 0 1-.033.943l-3.444 1.068a1 1 0 0 0-.66.66l-1.067 3.443a.5.5 0 0 1-.943.033z\",\n  key: \"xwnzip\"\n}], [\"path\", {\n  d: \"M5 17A12 12 0 0 1 17 5\",\n  key: \"1okkup\"\n}], [\"circle\", {\n  cx: \"19\",\n  cy: \"5\",\n  r: \"2\",\n  key: \"mhkx31\"\n}], [\"circle\", {\n  cx: \"5\",\n  cy: \"19\",\n  r: \"2\",\n  key: \"v8kfzx\"\n}]];\nconst SplinePointer = createLucideIcon(\"spline-pointer\", __iconNode);\nexport { __iconNode, SplinePointer as default };\n//# sourceMappingURL=spline-pointer.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}