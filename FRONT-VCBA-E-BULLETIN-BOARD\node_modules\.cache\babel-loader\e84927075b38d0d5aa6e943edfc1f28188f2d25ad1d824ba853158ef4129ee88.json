{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M5 7 3 5\",\n  key: \"1yys58\"\n}], [\"path\", {\n  d: \"M9 6V3\",\n  key: \"1ptz9u\"\n}], [\"path\", {\n  d: \"m13 7 2-2\",\n  key: \"1w3vmq\"\n}], [\"circle\", {\n  cx: \"9\",\n  cy: \"13\",\n  r: \"3\",\n  key: \"1mma13\"\n}], [\"path\", {\n  d: \"M11.83 12H20a2 2 0 0 1 2 2v4a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-4a2 2 0 0 1 2-2h2.17\",\n  key: \"2frwzc\"\n}], [\"path\", {\n  d: \"M16 16h2\",\n  key: \"dnq2od\"\n}]];\nconst Projector = createLucideIcon(\"projector\", __iconNode);\nexport { __iconNode, Projector as default };\n//# sourceMappingURL=projector.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}