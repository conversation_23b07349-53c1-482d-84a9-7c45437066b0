{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 20h.01\",\n  key: \"zekei9\"\n}], [\"path\", {\n  d: \"M2 8.82a15 15 0 0 1 20 0\",\n  key: \"dnpr2z\"\n}], [\"path\", {\n  d: \"M5 12.859a10 10 0 0 1 14 0\",\n  key: \"1x1e6c\"\n}], [\"path\", {\n  d: \"M8.5 16.429a5 5 0 0 1 7 0\",\n  key: \"1bycff\"\n}]];\nconst Wifi = createLucideIcon(\"wifi\", __iconNode);\nexport { __iconNode, Wifi as default };\n//# sourceMappingURL=wifi.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}