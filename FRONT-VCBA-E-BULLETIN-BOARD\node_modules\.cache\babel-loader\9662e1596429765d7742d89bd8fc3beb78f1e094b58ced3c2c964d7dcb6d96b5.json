{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M17 12v4a1 1 0 0 1-1 1h-4\",\n  key: \"uk4fdo\"\n}], [\"path\", {\n  d: \"M17 3h2a2 2 0 0 1 2 2v2\",\n  key: \"4qcy5o\"\n}], [\"path\", {\n  d: \"M17 8V7\",\n  key: \"q2g9wo\"\n}], [\"path\", {\n  d: \"M21 17v2a2 2 0 0 1-2 2h-2\",\n  key: \"6vwrx8\"\n}], [\"path\", {\n  d: \"M3 7V5a2 2 0 0 1 2-2h2\",\n  key: \"aa7l1z\"\n}], [\"path\", {\n  d: \"M7 17h.01\",\n  key: \"19xn7k\"\n}], [\"path\", {\n  d: \"M7 21H5a2 2 0 0 1-2-2v-2\",\n  key: \"ioqczr\"\n}], [\"rect\", {\n  x: \"7\",\n  y: \"7\",\n  width: \"5\",\n  height: \"5\",\n  rx: \"1\",\n  key: \"m9kyts\"\n}]];\nconst ScanQrCode = createLucideIcon(\"scan-qr-code\", __iconNode);\nexport { __iconNode, ScanQrCode as default };\n//# sourceMappingURL=scan-qr-code.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}