{"ast": null, "code": "var _jsxFileName = \"D:\\\\capstone-e-bulletin-reactjs-proj\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\AdminNewsfeed.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { announcementService } from '../../services';\nimport { useCategories } from '../../hooks/useAnnouncements';\nimport AdminCommentSection from '../../components/admin/AdminCommentSection';\nimport FacebookImageGallery from '../../components/common/FacebookImageGallery';\nimport ImageLightbox from '../../components/common/ImageLightbox';\nimport { getImageUrl, API_BASE_URL } from '../../config/constants';\nimport { Newspaper, Search, Pin, Calendar, MessageSquare, Heart, Eye, Edit, Users, LayoutDashboard, BookOpen, PartyPopper, AlertTriangle, Clock, Trophy, Briefcase, GraduationCap, Flag, Coffee, Plane } from 'lucide-react';\n\n// Custom hook for CORS-safe image loading\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst useImageLoader = imagePath => {\n  _s();\n  const [imageUrl, setImageUrl] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n        console.log('🔄 Fetching image via CORS-safe method:', fullUrl);\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n            'Origin': window.location.origin\n          },\n          mode: 'cors'\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n        setImageUrl(objectUrl);\n        console.log('✅ Image loaded successfully via fetch');\n      } catch (err) {\n        console.error('❌ Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadImage();\n\n    // Cleanup object URL on unmount\n    return () => {\n      if (imageUrl && imageUrl.startsWith('blob:')) {\n        URL.revokeObjectURL(imageUrl);\n      }\n    };\n  }, [imagePath]);\n  return {\n    imageUrl,\n    loading,\n    error\n  };\n};\n\n// CORS-safe image component\n_s(useImageLoader, \"RmtsSrtaIxi3XNLTaMW1wv0GUMw=\");\nconst ImageDisplay = ({\n  imagePath,\n  alt,\n  style,\n  className,\n  onLoad,\n  onMouseEnter,\n  onMouseLeave\n}) => {\n  _s2();\n  const {\n    imageUrl,\n    loading,\n    error\n  } = useImageLoader(imagePath);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b'\n      },\n      className: className,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.5rem',\n            fontSize: '1.5rem'\n          },\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem'\n          },\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !imageUrl) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b',\n        border: '2px dashed #cbd5e1'\n      },\n      className: className,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.5rem',\n            fontSize: '1.5rem'\n          },\n          children: \"\\uD83D\\uDDBC\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem',\n            fontWeight: '500'\n          },\n          children: \"Image unavailable\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.75rem',\n            marginTop: '0.25rem',\n            color: '#9ca3af'\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 150,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"img\", {\n    src: imageUrl,\n    alt: alt,\n    style: style,\n    className: className,\n    onLoad: e => {\n      console.log('✅ Image rendered successfully via CORS-safe method');\n      onLoad === null || onLoad === void 0 ? void 0 : onLoad(e);\n    },\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 164,\n    columnNumber: 5\n  }, this);\n};\n\n// Image Gallery Component\n_s2(ImageDisplay, \"PvWnh8aQTIWDcUxVyGaJg2nxP24=\", false, function () {\n  return [useImageLoader];\n});\n_c = ImageDisplay;\nconst ImageGallery = ({\n  images,\n  altPrefix,\n  onImageClick\n}) => {\n  if (!images || images.length === 0) return null;\n  const visibleImages = images.slice(0, 4);\n  const remainingCount = Math.max(0, images.length - 4);\n  const getContainerStyle = (index, total) => {\n    const baseStyle = {\n      position: 'relative',\n      overflow: 'hidden',\n      borderRadius: '12px',\n      cursor: onImageClick ? 'pointer' : 'default'\n    };\n    if (total === 1) {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '300px'\n      };\n    } else if (total === 2) {\n      return {\n        ...baseStyle,\n        width: '50%',\n        height: '250px'\n      };\n    } else if (total === 3) {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '50%',\n          height: '250px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '50%',\n          height: '120px'\n        };\n      }\n    } else {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '50%',\n          height: '250px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '33.33%',\n          height: '120px'\n        };\n      }\n    }\n  };\n  const getImageStyle = (index, total) => {\n    return {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover',\n      transition: 'transform 0.3s ease'\n    };\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: getContainerStyle(0, visibleImages.length),\n      children: [/*#__PURE__*/_jsxDEV(ImageDisplay, {\n        imagePath: visibleImages[0].file_path,\n        alt: `${altPrefix} - Image 1`,\n        style: getImageStyle(0, visibleImages.length),\n        onMouseEnter: e => {\n          e.currentTarget.style.transform = 'scale(1.02)';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.transform = 'scale(1)';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          cursor: 'pointer'\n        },\n        onClick: () => onImageClick(0)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 249,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 236,\n      columnNumber: 7\n    }, this), visibleImages.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: visibleImages.length === 2 ? 'row' : 'column',\n        gap: '4px',\n        width: visibleImages.length === 2 ? '50%' : '50%'\n      },\n      children: visibleImages.slice(1).map((image, idx) => {\n        const actualIndex = idx + 1;\n        const isLast = actualIndex === visibleImages.length - 1 && remainingCount > 0;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...getContainerStyle(actualIndex, visibleImages.length),\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ImageDisplay, {\n            imagePath: image.file_path,\n            alt: `${altPrefix} - Image ${actualIndex + 1}`,\n            style: getImageStyle(actualIndex, visibleImages.length),\n            onMouseEnter: e => {\n              e.currentTarget.style.transform = 'scale(1.02)';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.transform = 'scale(1)';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 17\n          }, this), isLast && remainingCount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              backgroundColor: 'rgba(0, 0, 0, 0.6)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              fontSize: '1.5rem',\n              fontWeight: '600'\n            },\n            children: [\"+\", remainingCount]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 19\n          }, this), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            },\n            onClick: () => onImageClick(actualIndex)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 19\n          }, this)]\n        }, actualIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 229,\n    columnNumber: 5\n  }, this);\n};\n\n// Main AdminNewsfeed Component\n_c2 = ImageGallery;\nconst AdminNewsfeed = () => {\n  _s3();\n  const navigate = useNavigate();\n\n  // Open lightbox function for announcements\n  const openLightbox = (images, initialIndex) => {\n    const imageUrls = images.map(img => getImageUrl(img.file_path)).filter(Boolean);\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Open lightbox function for image URLs (calendar events)\n  const openLightboxWithUrls = (imageUrls, initialIndex) => {\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Category styling function\n  const getCategoryStyle = categoryName => {\n    const styles = {\n      'ACADEMIC': {\n        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n        icon: BookOpen\n      },\n      'GENERAL': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Users\n      },\n      'EVENTS': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: PartyPopper\n      },\n      'EMERGENCY': {\n        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n        icon: AlertTriangle\n      },\n      'SPORTS': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Trophy\n      },\n      'DEADLINES': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Clock\n      }\n    };\n    return styles[categoryName] || styles['GENERAL'];\n  };\n\n  // Holiday type styling function\n  const getHolidayTypeStyle = holidayTypeName => {\n    const styles = {\n      'National Holiday': {\n        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n        icon: Flag\n      },\n      'School Event': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: GraduationCap\n      },\n      'Academic Break': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Coffee\n      },\n      'Sports Event': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Trophy\n      },\n      'Field Trip': {\n        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n        icon: Plane\n      },\n      'Meeting': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Briefcase\n      }\n    };\n    return styles[holidayTypeName] || styles['School Event'];\n  };\n\n  // Filter states\n  const [filterCategory, setFilterCategory] = useState('');\n  const [filterGradeLevel, setFilterGradeLevel] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // UI states\n  const [showComments, setShowComments] = useState(null);\n  const [newComment, setNewComment] = useState({});\n  const [submittingComment, setSubmittingComment] = useState(null);\n  const [selectedPinnedPost, setSelectedPinnedPost] = useState(null);\n\n  // Lightbox states\n  const [lightboxOpen, setLightboxOpen] = useState(false);\n  const [lightboxImages, setLightboxImages] = useState([]);\n  const [lightboxInitialIndex, setLightboxInitialIndex] = useState(0);\n\n  // Data states\n  const [announcements, setAnnouncements] = useState([]);\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState([]);\n  const [calendarEvents, setCalendarEvents] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState();\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState();\n  const [recentStudents, setRecentStudents] = useState([]);\n  const [studentLoading, setStudentLoading] = useState(false);\n  const {\n    categories\n  } = useCategories();\n\n  // Fetch published announcements with images (admin can see all)\n  const fetchPublishedAnnouncements = async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const response = await fetch(`${API_BASE_URL}/api/announcements?status=published&page=1&limit=50&sort_by=created_at&sort_order=DESC`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (data.success && data.data) {\n        const announcementsData = data.data.announcements || [];\n\n        // The announcements already include attachments/images from the API\n        // No need to fetch images separately\n        setAnnouncements(announcementsData);\n\n        // Separate pinned announcements\n        const pinned = announcementsData.filter(ann => ann.is_pinned === 1);\n        setPinnedAnnouncements(pinned);\n      } else {\n        setError('Failed to load announcements');\n      }\n    } catch (err) {\n      console.error('Error fetching announcements:', err);\n      setError(err.message || 'Failed to load announcements');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch calendar events\n  const fetchCalendarEvents = async () => {\n    try {\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(eventsData.map(async event => {\n          try {\n            const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`, {\n              headers: {\n                'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n                'Content-Type': 'application/json'\n              }\n            });\n            const imageData = await imageResponse.json();\n            if (imageData.success && imageData.data) {\n              event.images = imageData.data.attachments || [];\n            } else {\n              event.images = [];\n            }\n          } catch (imgErr) {\n            console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n            event.images = [];\n          }\n          return event;\n        }));\n        setCalendarEvents(eventsWithImages);\n      } else {\n        setCalendarError('Failed to load calendar events');\n      }\n    } catch (err) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to load calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  };\n\n  // Fetch recent student registrations\n  const fetchRecentStudents = async () => {\n    try {\n      setStudentLoading(true);\n      const response = await fetch(`${API_BASE_URL}/api/students?page=1&limit=5&sort_by=created_at&sort_order=DESC`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (data.success && data.data) {\n        setRecentStudents(data.data.students || []);\n      }\n    } catch (err) {\n      console.error('Error fetching recent students:', err);\n    } finally {\n      setStudentLoading(false);\n    }\n  };\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchPublishedAnnouncements();\n    fetchCalendarEvents();\n    // fetchRecentStudents(); // Commented out - endpoint doesn't exist yet\n  }, []);\n\n  // Handle like/unlike functionality (admin perspective)\n  const handleLikeToggle = async announcement => {\n    try {\n      if (announcement.user_reaction) {\n        await announcementService.removeReaction(announcement.announcement_id);\n      } else {\n        const response = await fetch(`${API_BASE_URL}/api/announcements/${announcement.announcement_id}/reactions`, {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            reaction_type_id: 1,\n            notify_admin: false // Admin doesn't need to notify themselves\n          })\n        });\n        if (!response.ok) {\n          throw new Error('Failed to add reaction');\n        }\n      }\n\n      // Refresh data\n      await fetchPublishedAnnouncements();\n    } catch (error) {\n      console.error('Error toggling like:', error);\n    }\n  };\n\n  // Handle comment submission (admin perspective)\n  const handleCommentSubmit = async (announcementId, commentText) => {\n    if (!commentText.trim()) return;\n    try {\n      setSubmittingComment(announcementId);\n      const response = await fetch(`${API_BASE_URL}/api/admin/announcements/${announcementId}/comments`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          content: commentText,\n          commenter_name: 'Admin User',\n          commenter_email: '<EMAIL>',\n          notify_admin: false\n        })\n      });\n      if (response.ok) {\n        setNewComment(prev => ({\n          ...prev,\n          [announcementId]: ''\n        }));\n        await fetchPublishedAnnouncements();\n      } else {\n        console.error('Failed to submit comment');\n      }\n    } catch (error) {\n      console.error('Error submitting comment:', error);\n    } finally {\n      setSubmittingComment(null);\n    }\n  };\n\n  // Filter announcements\n  const filteredAnnouncements = announcements.filter(announcement => {\n    var _announcement$categor, _announcement$grade_l;\n    const matchesSearch = !searchTerm || announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) || announcement.content.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = !filterCategory || ((_announcement$categor = announcement.category_id) === null || _announcement$categor === void 0 ? void 0 : _announcement$categor.toString()) === filterCategory;\n    const matchesGradeLevel = !filterGradeLevel || ((_announcement$grade_l = announcement.grade_level) === null || _announcement$grade_l === void 0 ? void 0 : _announcement$grade_l.toString()) === filterGradeLevel;\n    return matchesSearch && matchesCategory && matchesGradeLevel;\n  });\n\n  // Filter calendar events with date-based filtering\n  const filteredCalendarEvents = calendarEvents.filter(event => {\n    const matchesSearch = !searchTerm || event.title.toLowerCase().includes(searchTerm.toLowerCase()) || event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase());\n\n    // Only show events that are published and on or after today's date\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    const eventDate = new Date(event.event_date);\n    eventDate.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    const isEventDateValid = eventDate >= today;\n    const isPublished = event.is_published === 1;\n    return matchesSearch && isEventDateValid && isPublished;\n  });\n\n  // Combine and sort all content by date (most recent first)\n  const displayAnnouncements = filteredAnnouncements;\n  const displayEvents = filteredCalendarEvents;\n\n  // Create combined content array for better chronological display\n  const combinedContent = [...displayAnnouncements.map(item => ({\n    ...item,\n    type: 'announcement',\n    sortDate: new Date(item.created_at)\n  })), ...displayEvents.map(item => ({\n    ...item,\n    type: 'event',\n    sortDate: new Date(item.event_date)\n  }))].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)',\n      position: 'relative'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundImage: `\n          radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.03) 0%, transparent 50%),\n          radial-gradient(circle at 75% 75%, rgba(250, 204, 21, 0.03) 0%, transparent 50%)\n        `,\n        pointerEvents: 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 682,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'relative',\n        zIndex: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        style: {\n          background: 'white',\n          borderBottom: '1px solid #e5e7eb',\n          position: 'sticky',\n          top: 0,\n          zIndex: 100,\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '0 2rem',\n            height: '72px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1.5rem',\n              minWidth: '300px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/logo/vcba1.png\",\n              alt: \"VCBA Logo\",\n              style: {\n                width: '48px',\n                height: '48px',\n                objectFit: 'contain'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 719,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                style: {\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827',\n                  lineHeight: '1.2'\n                },\n                children: \"VCBA E-Bulletin Board\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 729,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280',\n                  lineHeight: '1.2'\n                },\n                children: \"Admin Newsfeed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 738,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 728,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 713,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1,\n              maxWidth: '500px',\n              margin: '0 2rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Search, {\n                size: 20,\n                style: {\n                  position: 'absolute',\n                  left: '1rem',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  color: '#9ca3af'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 756,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search post\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                style: {\n                  width: '100%',\n                  height: '44px',\n                  padding: '0 1rem 0 3rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '12px',\n                  background: '#f9fafb',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  transition: 'all 0.2s ease'\n                },\n                onFocus: e => {\n                  e.currentTarget.style.borderColor = '#22c55e';\n                  e.currentTarget.style.background = 'white';\n                  e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n                },\n                onBlur: e => {\n                  e.currentTarget.style.borderColor = '#d1d5db';\n                  e.currentTarget.style.background = '#f9fafb';\n                  e.currentTarget.style.boxShadow = 'none';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 766,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 755,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 750,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              minWidth: '400px',\n              justifyContent: 'flex-end'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.5rem',\n                background: '#f9fafb',\n                borderRadius: '12px',\n                border: '1px solid #e5e7eb'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                value: filterCategory,\n                onChange: e => setFilterCategory(e.target.value),\n                style: {\n                  padding: '0.5rem 0.75rem',\n                  border: 'none',\n                  borderRadius: '8px',\n                  background: 'white',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  cursor: 'pointer',\n                  minWidth: '110px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"All Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 831,\n                  columnNumber: 19\n                }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: category.category_id.toString(),\n                  children: category.name\n                }, category.category_id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 833,\n                  columnNumber: 21\n                }, this))]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 816,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filterGradeLevel,\n                onChange: e => setFilterGradeLevel(e.target.value),\n                style: {\n                  padding: '0.5rem 0.75rem',\n                  border: 'none',\n                  borderRadius: '8px',\n                  background: 'white',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  cursor: 'pointer',\n                  minWidth: '100px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"All Grades\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 854,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"11\",\n                  children: \"Grade 11\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 855,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"12\",\n                  children: \"Grade 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 856,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 839,\n                columnNumber: 17\n              }, this), (searchTerm || filterCategory || filterGradeLevel) && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setSearchTerm('');\n                  setFilterCategory('');\n                  setFilterGradeLevel('');\n                },\n                style: {\n                  padding: '0.5rem 0.75rem',\n                  border: 'none',\n                  borderRadius: '8px',\n                  background: '#ef4444',\n                  color: 'white',\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.background = '#dc2626';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.background = '#ef4444';\n                },\n                children: \"Clear\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 860,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 807,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate('/admin/dashboard'),\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.75rem 1rem',\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                border: 'none',\n                borderRadius: '12px',\n                color: 'white',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease',\n                boxShadow: '0 2px 8px rgba(34, 197, 94, 0.2)'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.2)';\n              },\n              children: [/*#__PURE__*/_jsxDEV(LayoutDashboard, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 916,\n                columnNumber: 17\n              }, this), \"Dashboard\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 890,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 798,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 705,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 697,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '2rem',\n          display: 'flex',\n          gap: '2rem',\n          alignItems: 'flex-start'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '320px',\n            flexShrink: 0\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              borderRadius: '16px',\n              border: '1px solid #e5e7eb',\n              overflow: 'hidden',\n              position: 'sticky',\n              top: '100px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '1.5rem 1.5rem 1rem',\n                borderBottom: '1px solid #f3f4f6'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.75rem',\n                  marginBottom: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Pin, {\n                  size: 20,\n                  style: {\n                    color: '#22c55e'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 956,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#111827'\n                  },\n                  children: \"Pinned Posts\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 957,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 950,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                },\n                children: \"Important announcements and updates\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 966,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 946,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '1rem'\n              },\n              children: pinnedAnnouncements.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [pinnedAnnouncements.slice(0, 3).map((announcement, index) => {\n                  // Handle alert announcements with special styling\n                  const isAlert = announcement.is_alert;\n                  const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                  const categoryStyle = getCategoryStyle(categoryName);\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      padding: '1rem',\n                      background: isAlert ? '#fef2f2' : '#f8fafc',\n                      borderRadius: '12px',\n                      border: isAlert ? '1px solid #fecaca' : '1px solid #e2e8f0',\n                      marginBottom: '1rem',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.background = isAlert ? '#fee2e2' : '#f1f5f9';\n                      e.currentTarget.style.borderColor = isAlert ? '#ef4444' : '#22c55e';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.background = isAlert ? '#fef2f2' : '#f8fafc';\n                      e.currentTarget.style.borderColor = isAlert ? '#fecaca' : '#e2e8f0';\n                    },\n                    onClick: () => setSelectedPinnedPost(announcement),\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'flex-start',\n                        gap: '0.75rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '8px',\n                          height: '8px',\n                          background: isAlert ? '#ef4444' : categoryStyle.background.includes('#ef4444') ? '#ef4444' : '#22c55e',\n                          borderRadius: '50%',\n                          marginTop: '0.5rem',\n                          flexShrink: 0\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1012,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          flex: 1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          style: {\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '0.875rem',\n                            fontWeight: '600',\n                            color: '#111827',\n                            lineHeight: '1.4'\n                          },\n                          children: announcement.title\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1021,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          style: {\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '0.8rem',\n                            color: '#6b7280',\n                            lineHeight: '1.4'\n                          },\n                          children: announcement.content.length > 80 ? `${announcement.content.substring(0, 80)}...` : announcement.content\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1030,\n                          columnNumber: 31\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            fontSize: '0.75rem',\n                            color: '#9ca3af'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                            size: 12\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1047,\n                            columnNumber: 33\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: new Date(announcement.created_at).toLocaleDateString()\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1048,\n                            columnNumber: 33\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1040,\n                          columnNumber: 31\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1020,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1007,\n                      columnNumber: 27\n                    }, this)\n                  }, announcement.announcement_id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 986,\n                    columnNumber: 25\n                  }, this);\n                }), pinnedAnnouncements.length > 3 && /*#__PURE__*/_jsxDEV(\"button\", {\n                  style: {\n                    width: '100%',\n                    padding: '0.75rem',\n                    border: '1px solid #e5e7eb',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#22c55e',\n                    fontSize: '0.875rem',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.background = '#f0fdf4';\n                    e.currentTarget.style.borderColor = '#22c55e';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.background = 'white';\n                    e.currentTarget.style.borderColor = '#e5e7eb';\n                  },\n                  children: [\"View All \", pinnedAnnouncements.length, \" Pinned Posts\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1057,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '2rem 1rem',\n                  textAlign: 'center',\n                  color: '#6b7280'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Pin, {\n                  size: 24,\n                  style: {\n                    marginBottom: '0.5rem',\n                    opacity: 0.5\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1087,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '0.875rem'\n                  },\n                  children: \"No pinned posts available\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1088,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1082,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 976,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 937,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 933,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            minWidth: 0\n          },\n          children: [(loading || calendarLoading) && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              minHeight: '400px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                gap: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '3rem',\n                  height: '3rem',\n                  border: '4px solid rgba(34, 197, 94, 0.2)',\n                  borderTop: '4px solid #22c55e',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1113,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#6b7280',\n                  fontSize: '1rem',\n                  fontWeight: '500'\n                },\n                children: \"Loading content...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1121,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1107,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1101,\n            columnNumber: 13\n          }, this), (error || calendarError) && !loading && !calendarLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '2rem',\n              background: 'rgba(239, 68, 68, 0.1)',\n              border: '1px solid rgba(239, 68, 68, 0.2)',\n              borderRadius: '16px',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '4rem',\n                height: '4rem',\n                background: 'rgba(239, 68, 68, 0.1)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 1rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(MessageSquare, {\n                size: 24,\n                color: \"#ef4444\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1151,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                color: '#ef4444',\n                margin: '0 0 0.5rem 0',\n                fontSize: '1.25rem',\n                fontWeight: '600'\n              },\n              children: \"Error Loading Content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#6b7280',\n                margin: '0 0 1.5rem 0',\n                fontSize: '1rem'\n              },\n              children: error || calendarError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1161,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                fetchPublishedAnnouncements();\n                fetchCalendarEvents();\n              },\n              style: {\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '12px',\n                padding: '0.75rem 1.5rem',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = 'none';\n              },\n              children: \"Try Again\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1168,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1134,\n            columnNumber: 13\n          }, this), !loading && !calendarLoading && !error && !calendarError && displayAnnouncements.length === 0 && displayEvents.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '4rem 2rem',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '5rem',\n                height: '5rem',\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 2rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(Newspaper, {\n                size: 32,\n                color: \"white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1215,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1205,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                color: '#374151',\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '600'\n              },\n              children: \"No Content Available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1217,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#6b7280',\n                margin: '0 0 2rem 0',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                maxWidth: '500px',\n                marginLeft: 'auto',\n                marginRight: 'auto'\n              },\n              children: searchTerm || filterCategory || filterGradeLevel ? 'No content matches your current filters. Try adjusting your search criteria.' : 'There are no published announcements or events at the moment. Check back later for updates.'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1225,\n              columnNumber: 15\n            }, this), (searchTerm || filterCategory || filterGradeLevel) && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setSearchTerm('');\n                setFilterCategory('');\n                setFilterGradeLevel('');\n              },\n              style: {\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '12px',\n                padding: '0.75rem 1.5rem',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = 'none';\n              },\n              children: \"Clear Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1240,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1201,\n            columnNumber: 13\n          }, this), !studentLoading && recentStudents.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'rgba(255, 255, 255, 0.95)',\n              borderRadius: '16px',\n              padding: '1.5rem',\n              border: '1px solid rgba(0, 0, 0, 0.1)',\n              backdropFilter: 'blur(10px)',\n              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n              marginBottom: '1.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                fontSize: '1.25rem',\n                fontWeight: '600',\n                color: '#2d5016',\n                margin: '0 0 1rem 0',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              },\n              children: \"\\uD83D\\uDC65 Recent Student Registrations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1283,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gap: '0.75rem'\n              },\n              children: recentStudents.slice(0, 3).map(student => {\n                var _student$profile, _student$profile2, _student$profile3;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    padding: '0.75rem',\n                    backgroundColor: '#f8fdf8',\n                    borderRadius: '8px',\n                    border: '1px solid #e8f5e8'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontWeight: '500',\n                        color: '#2d5016',\n                        fontSize: '0.875rem'\n                      },\n                      children: [(_student$profile = student.profile) === null || _student$profile === void 0 ? void 0 : _student$profile.first_name, \" \", (_student$profile2 = student.profile) === null || _student$profile2 === void 0 ? void 0 : _student$profile2.last_name]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1312,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.75rem',\n                        color: '#6b7280'\n                      },\n                      children: [\"Grade \", (_student$profile3 = student.profile) === null || _student$profile3 === void 0 ? void 0 : _student$profile3.grade_level, \" \\u2022 \", student.student_number]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1319,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1311,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.75rem',\n                      color: '#6b7280'\n                    },\n                    children: new Date(student.created_at).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1326,\n                    columnNumber: 21\n                  }, this)]\n                }, student.student_id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1299,\n                  columnNumber: 19\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1294,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1274,\n            columnNumber: 13\n          }, this), !loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem'\n            },\n            children: [displayEvents.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: displayEvents.map(event => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'rgba(255, 255, 255, 0.95)',\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  border: '1px solid rgba(0, 0, 0, 0.1)',\n                  backdropFilter: 'blur(10px)',\n                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                  transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-2px)';\n                  e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    gap: '1rem',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '48px',\n                      height: '48px',\n                      background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                      borderRadius: '12px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      flexShrink: 0\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Calendar, {\n                      size: 24,\n                      color: \"white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1386,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1376,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        marginBottom: '0.5rem'\n                      },\n                      children: [(() => {\n                        const holidayTypeName = event.holiday_type_name || 'School Event';\n                        const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                        const IconComponent = holidayStyle.icon;\n                        return /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            background: holidayStyle.background,\n                            color: 'white',\n                            fontSize: '0.75rem',\n                            fontWeight: '600',\n                            padding: '0.25rem 0.75rem',\n                            borderRadius: '20px',\n                            textTransform: 'uppercase',\n                            letterSpacing: '0.5px',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.25rem'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                            size: 12,\n                            color: \"white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1415,\n                            columnNumber: 35\n                          }, this), holidayTypeName]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1402,\n                          columnNumber: 33\n                        }, this);\n                      })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.25rem',\n                          color: '#6b7280',\n                          fontSize: '0.875rem'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                          size: 14\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1428,\n                          columnNumber: 31\n                        }, this), new Date(event.event_date).toLocaleDateString('en-US', {\n                          weekday: 'long',\n                          year: 'numeric',\n                          month: 'long',\n                          day: 'numeric'\n                        })]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1421,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1390,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      style: {\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        color: '#1f2937',\n                        lineHeight: '1.3'\n                      },\n                      children: event.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1438,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1389,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      gap: '0.5rem'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => navigate(`/admin/calendar?event=${event.calendar_id}`),\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem',\n                        padding: '0.5rem 0.75rem',\n                        background: 'rgba(59, 130, 246, 0.1)',\n                        border: '1px solid rgba(59, 130, 246, 0.2)',\n                        borderRadius: '8px',\n                        color: '#3b82f6',\n                        fontSize: '0.75rem',\n                        fontWeight: '500',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = 'rgba(59, 130, 246, 0.2)';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'rgba(59, 130, 246, 0.1)';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Edit, {\n                        size: 12\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1474,\n                        columnNumber: 29\n                      }, this), \"Edit\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1451,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1450,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1370,\n                  columnNumber: 23\n                }, this), event.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#4b5563',\n                    fontSize: '0.95rem',\n                    lineHeight: '1.6',\n                    marginBottom: '1rem'\n                  },\n                  children: event.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1482,\n                  columnNumber: 25\n                }, this), (() => {\n                  // Get event images if they exist\n                  const eventImageUrls = [];\n                  if (event.images && event.images.length > 0) {\n                    event.images.forEach(img => {\n                      if (img.file_path) {\n                        // Convert file_path to full URL\n                        const imageUrl = getImageUrl(img.file_path);\n                        if (imageUrl) {\n                          eventImageUrls.push(imageUrl);\n                        }\n                      }\n                    });\n                  }\n                  return eventImageUrls.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginBottom: '1rem'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(FacebookImageGallery, {\n                      images: eventImageUrls.filter(Boolean),\n                      altPrefix: event.title,\n                      maxVisible: 4,\n                      onImageClick: index => {\n                        const filteredImages = eventImageUrls.filter(Boolean);\n                        openLightboxWithUrls(filteredImages, index);\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1511,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1510,\n                    columnNumber: 27\n                  }, this) : null;\n                })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1.5rem',\n                    padding: '1rem',\n                    background: 'rgba(59, 130, 246, 0.05)',\n                    borderRadius: '12px',\n                    fontSize: '0.875rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      color: '#6b7280'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1540,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: event.end_date && event.end_date !== event.event_date ? `${new Date(event.event_date).toLocaleDateString()} - ${new Date(event.end_date).toLocaleDateString()}` : new Date(event.event_date).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1541,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1534,\n                    columnNumber: 25\n                  }, this), event.holiday_type_name && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      color: '#6b7280'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        padding: '0.25rem 0.5rem',\n                        background: 'rgba(59, 130, 246, 0.1)',\n                        borderRadius: '6px',\n                        fontSize: '0.75rem',\n                        fontWeight: '500'\n                      },\n                      children: event.holiday_type_name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1556,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1550,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1525,\n                  columnNumber: 23\n                }, this)]\n              }, `event-${event.calendar_id}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1349,\n                columnNumber: 21\n              }, this))\n            }, void 0, false), displayAnnouncements.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: displayAnnouncements.map(announcement => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'rgba(255, 255, 255, 0.95)',\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  border: announcement.is_pinned ? '2px solid rgba(250, 204, 21, 0.3)' : '1px solid rgba(0, 0, 0, 0.1)',\n                  backdropFilter: 'blur(10px)',\n                  boxShadow: announcement.is_pinned ? '0 4px 20px rgba(250, 204, 21, 0.15)' : '0 4px 20px rgba(0, 0, 0, 0.08)',\n                  transition: 'transform 0.2s ease, box-shadow 0.2s ease',\n                  position: 'relative'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-2px)';\n                  e.currentTarget.style.boxShadow = announcement.is_pinned ? '0 8px 30px rgba(250, 204, 21, 0.25)' : '0 8px 30px rgba(0, 0, 0, 0.12)';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = announcement.is_pinned ? '0 4px 20px rgba(250, 204, 21, 0.15)' : '0 4px 20px rgba(0, 0, 0, 0.08)';\n                },\n                children: [announcement.is_pinned && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    top: '-8px',\n                    right: '1rem',\n                    background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                    color: 'white',\n                    padding: '0.25rem 0.75rem',\n                    borderRadius: '12px',\n                    fontSize: '0.75rem',\n                    fontWeight: '600',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem',\n                    boxShadow: '0 2px 8px rgba(250, 204, 21, 0.3)'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Pin, {\n                    size: 12\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1623,\n                    columnNumber: 27\n                  }, this), \"Pinned\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1608,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    gap: '1rem',\n                    marginBottom: '1rem'\n                  },\n                  children: [(() => {\n                    if (announcement.is_alert) {\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '48px',\n                          height: '48px',\n                          background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                          borderRadius: '12px',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          flexShrink: 0\n                        },\n                        children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                          size: 24,\n                          color: \"white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1648,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1638,\n                        columnNumber: 31\n                      }, this);\n                    } else {\n                      const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                      const categoryStyle = getCategoryStyle(categoryName);\n                      const IconComponent = categoryStyle.icon;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '48px',\n                          height: '48px',\n                          background: categoryStyle.background,\n                          borderRadius: '12px',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          flexShrink: 0\n                        },\n                        children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                          size: 24,\n                          color: \"white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1667,\n                          columnNumber: 33\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1657,\n                        columnNumber: 31\n                      }, this);\n                    }\n                  })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        marginBottom: '0.5rem',\n                        flexWrap: 'wrap'\n                      },\n                      children: [(() => {\n                        if (announcement.is_alert) {\n                          return /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                              color: 'white',\n                              fontSize: '0.75rem',\n                              fontWeight: '600',\n                              padding: '0.25rem 0.75rem',\n                              borderRadius: '20px',\n                              textTransform: 'uppercase',\n                              letterSpacing: '0.5px',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                              size: 12,\n                              color: \"white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1697,\n                              columnNumber: 37\n                            }, this), \"Alert\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1684,\n                            columnNumber: 35\n                          }, this);\n                        } else {\n                          const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                          const categoryStyle = getCategoryStyle(categoryName);\n                          const IconComponent = categoryStyle.icon;\n                          return /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              background: categoryStyle.background,\n                              color: 'white',\n                              fontSize: '0.75rem',\n                              fontWeight: '600',\n                              padding: '0.25rem 0.75rem',\n                              borderRadius: '20px',\n                              textTransform: 'uppercase',\n                              letterSpacing: '0.5px',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                              size: 12,\n                              color: \"white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1720,\n                              columnNumber: 37\n                            }, this), categoryName]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1707,\n                            columnNumber: 35\n                          }, this);\n                        }\n                      })(), announcement.grade_level && /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          background: 'rgba(59, 130, 246, 0.1)',\n                          color: '#3b82f6',\n                          fontSize: '0.75rem',\n                          fontWeight: '500',\n                          padding: '0.25rem 0.75rem',\n                          borderRadius: '20px'\n                        },\n                        children: [\"Grade \", announcement.grade_level]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1728,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          color: '#6b7280',\n                          fontSize: '0.875rem'\n                        },\n                        children: new Date(announcement.created_at).toLocaleDateString('en-US', {\n                          weekday: 'short',\n                          year: 'numeric',\n                          month: 'short',\n                          day: 'numeric'\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1740,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1674,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      style: {\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        color: '#1f2937',\n                        lineHeight: '1.3'\n                      },\n                      children: announcement.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1753,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1673,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      gap: '0.5rem'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => navigate(`/admin/posts?edit=${announcement.announcement_id}`),\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem',\n                        padding: '0.5rem 0.75rem',\n                        background: 'rgba(34, 197, 94, 0.1)',\n                        border: '1px solid rgba(34, 197, 94, 0.2)',\n                        borderRadius: '8px',\n                        color: '#22c55e',\n                        fontSize: '0.75rem',\n                        fontWeight: '500',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = 'rgba(34, 197, 94, 0.2)';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'rgba(34, 197, 94, 0.1)';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Edit, {\n                        size: 12\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1789,\n                        columnNumber: 29\n                      }, this), \"Edit\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1766,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1765,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1629,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#4b5563',\n                    fontSize: '0.95rem',\n                    lineHeight: '1.6',\n                    marginBottom: '1rem'\n                  },\n                  children: announcement.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1796,\n                  columnNumber: 23\n                }, this), announcement.attachments && announcement.attachments.length > 0 && /*#__PURE__*/_jsxDEV(ImageGallery, {\n                  images: announcement.attachments,\n                  altPrefix: announcement.title,\n                  onImageClick: index => {\n                    openLightbox(announcement.attachments, index);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1807,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    padding: '1rem',\n                    background: 'rgba(0, 0, 0, 0.02)',\n                    borderRadius: '12px',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1.5rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleLikeToggle(announcement),\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        background: 'none',\n                        border: 'none',\n                        color: announcement.user_reaction ? '#ef4444' : '#6b7280',\n                        cursor: 'pointer',\n                        padding: '0.5rem',\n                        borderRadius: '8px',\n                        transition: 'all 0.2s ease',\n                        fontSize: '0.875rem',\n                        fontWeight: '500'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'none';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Heart, {\n                        size: 18,\n                        fill: announcement.user_reaction ? '#ef4444' : 'none'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1855,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: announcement.reaction_count || 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1859,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1832,\n                      columnNumber: 27\n                    }, this), announcement.allow_comments && /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setShowComments(showComments === announcement.announcement_id ? null : announcement.announcement_id),\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        background: 'none',\n                        border: 'none',\n                        color: showComments === announcement.announcement_id ? '#22c55e' : '#6b7280',\n                        cursor: 'pointer',\n                        padding: '0.5rem',\n                        borderRadius: '8px',\n                        transition: 'all 0.2s ease',\n                        fontSize: '0.875rem',\n                        fontWeight: '500'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                        e.currentTarget.style.color = '#22c55e';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'none';\n                        e.currentTarget.style.color = showComments === announcement.announcement_id ? '#22c55e' : '#6b7280';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n                        size: 18\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1891,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: announcement.comment_count || 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1892,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1864,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        color: '#6b7280',\n                        fontSize: '0.875rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Eye, {\n                        size: 18\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1904,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [announcement.view_count || 0, \" views\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1905,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1897,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1826,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1rem',\n                      fontSize: '0.75rem',\n                      color: '#6b7280'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Users, {\n                        size: 14\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1922,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [\"Posted by \", announcement.posted_by_name || 'Admin']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1923,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1917,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        padding: '0.25rem 0.5rem',\n                        background: announcement.status === 'published' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(107, 114, 128, 0.1)',\n                        color: announcement.status === 'published' ? '#22c55e' : '#6b7280',\n                        borderRadius: '6px',\n                        fontWeight: '500'\n                      },\n                      children: announcement.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1926,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1910,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1817,\n                  columnNumber: 23\n                }, this), showComments === announcement.announcement_id && announcement.allow_comments && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '1rem',\n                    paddingTop: '1rem',\n                    borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(AdminCommentSection, {\n                    announcementId: announcement.announcement_id,\n                    allowComments: announcement.allow_comments,\n                    currentUserType: \"admin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1947,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1942,\n                  columnNumber: 25\n                }, this)]\n              }, `announcement-${announcement.announcement_id}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1577,\n                columnNumber: 21\n              }, this))\n            }, void 0, false)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1340,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1098,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 926,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 695,\n      columnNumber: 7\n    }, this), selectedPinnedPost && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundColor: 'rgba(0, 0, 0, 0.5)',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 1000,\n        padding: '2rem'\n      },\n      onClick: () => setSelectedPinnedPost(null),\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          backgroundColor: 'white',\n          borderRadius: '16px',\n          maxWidth: '600px',\n          width: '100%',\n          maxHeight: '80vh',\n          overflow: 'auto',\n          boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n        },\n        onClick: e => e.stopPropagation(),\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1.5rem',\n            borderBottom: '1px solid #e5e7eb',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.75rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Pin, {\n              size: 20,\n              style: {\n                color: '#22c55e'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2005,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                margin: 0,\n                fontSize: '1.25rem',\n                fontWeight: '600',\n                color: '#111827'\n              },\n              children: \"Pinned Post\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2006,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2000,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setSelectedPinnedPost(null),\n            style: {\n              background: 'none',\n              border: 'none',\n              fontSize: '1.5rem',\n              color: '#6b7280',\n              cursor: 'pointer',\n              padding: '0.25rem',\n              borderRadius: '4px',\n              transition: 'color 0.2s ease'\n            },\n            onMouseEnter: e => {\n              e.currentTarget.style.color = '#374151';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.color = '#6b7280';\n            },\n            children: \"\\xD7\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2015,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1993,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '1.5rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '0.75rem',\n              marginBottom: '1rem'\n            },\n            children: [(() => {\n              const categoryName = (selectedPinnedPost.category_name || 'GENERAL').toUpperCase();\n              const categoryStyle = getCategoryStyle(categoryName);\n              const IconComponent = categoryStyle.icon;\n              return /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  background: categoryStyle.background,\n                  color: 'white',\n                  fontSize: '0.75rem',\n                  fontWeight: '600',\n                  padding: '0.25rem 0.75rem',\n                  borderRadius: '20px',\n                  textTransform: 'uppercase',\n                  letterSpacing: '0.5px',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                  size: 12,\n                  color: \"white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2065,\n                  columnNumber: 23\n                }, this), categoryName]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2052,\n                columnNumber: 21\n              }, this);\n            })(), /*#__PURE__*/_jsxDEV(\"span\", {\n              style: {\n                background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                color: 'white',\n                padding: '0.25rem 0.75rem',\n                borderRadius: '12px',\n                fontSize: '0.75rem',\n                fontWeight: '600',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.25rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Pin, {\n                size: 12\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2082,\n                columnNumber: 19\n              }, this), \"PINNED\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2071,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2040,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            style: {\n              margin: '0 0 1rem 0',\n              fontSize: '1.5rem',\n              fontWeight: '700',\n              color: '#111827',\n              lineHeight: '1.3'\n            },\n            children: selectedPinnedPost.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2087,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              color: '#4b5563',\n              fontSize: '1rem',\n              lineHeight: '1.6',\n              marginBottom: '1.5rem'\n            },\n            children: selectedPinnedPost.content\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2097,\n            columnNumber: 15\n          }, this), selectedPinnedPost.attachments && selectedPinnedPost.attachments.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '1.5rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(ImageGallery, {\n              images: selectedPinnedPost.attachments,\n              altPrefix: selectedPinnedPost.title,\n              onImageClick: index => {\n                openLightbox(selectedPinnedPost.attachments, index);\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 2109,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 2108,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              fontSize: '0.875rem',\n              color: '#6b7280',\n              paddingTop: '1rem',\n              borderTop: '1px solid #e5e7eb'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 2133,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: [\"Published: \", new Date(selectedPinnedPost.created_at).toLocaleDateString()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2134,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2128,\n              columnNumber: 17\n            }, this), selectedPinnedPost.author_name && /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"By: \", selectedPinnedPost.author_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 2137,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 2119,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 2039,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1981,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1966,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(ImageLightbox, {\n      images: lightboxImages,\n      initialIndex: lightboxInitialIndex,\n      isOpen: lightboxOpen,\n      onClose: () => setLightboxOpen(false),\n      altPrefix: \"Announcement Image\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 2148,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 676,\n    columnNumber: 5\n  }, this);\n};\n_s3(AdminNewsfeed, \"/ZFdixGu6weWE6Bc3aIoCt0qVGw=\", false, function () {\n  return [useNavigate, useCategories];\n});\n_c3 = AdminNewsfeed;\nexport default AdminNewsfeed;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ImageDisplay\");\n$RefreshReg$(_c2, \"ImageGallery\");\n$RefreshReg$(_c3, \"AdminNewsfeed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "announcementService", "useCategories", "AdminCommentSection", "FacebookImageGallery", "ImageLightbox", "getImageUrl", "API_BASE_URL", "Newspaper", "Search", "<PERSON>n", "Calendar", "MessageSquare", "Heart", "Eye", "Edit", "Users", "LayoutDashboard", "BookOpen", "PartyPopper", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clock", "Trophy", "Briefcase", "GraduationCap", "Flag", "Coffee", "Plane", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "useImageLoader", "imagePath", "_s", "imageUrl", "setImageUrl", "loading", "setLoading", "error", "setError", "loadImage", "fullUrl", "Error", "console", "log", "response", "fetch", "method", "headers", "localStorage", "getItem", "window", "location", "origin", "mode", "ok", "status", "statusText", "blob", "objectUrl", "URL", "createObjectURL", "err", "message", "startsWith", "revokeObjectURL", "ImageDisplay", "alt", "style", "className", "onLoad", "onMouseEnter", "onMouseLeave", "_s2", "display", "alignItems", "justifyContent", "backgroundColor", "color", "children", "textAlign", "marginBottom", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "border", "fontWeight", "marginTop", "src", "e", "_c", "ImageGallery", "images", "altPrefix", "onImageClick", "length", "visibleImages", "slice", "remainingCount", "Math", "max", "getContainerStyle", "index", "total", "baseStyle", "position", "overflow", "borderRadius", "cursor", "width", "height", "getImageStyle", "objectFit", "transition", "gap", "file_path", "currentTarget", "transform", "top", "left", "right", "bottom", "onClick", "flexDirection", "map", "image", "idx", "actualIndex", "isLast", "_c2", "AdminNewsfeed", "_s3", "navigate", "openLightbox", "initialIndex", "imageUrls", "img", "filter", "Boolean", "setLightboxImages", "setLightboxInitialIndex", "setLightboxOpen", "openLightboxWithUrls", "getCategoryStyle", "categoryName", "styles", "background", "icon", "getHolidayTypeStyle", "holidayTypeName", "filterCategory", "setFilterCategory", "filterGradeLevel", "setFilterGradeLevel", "searchTerm", "setSearchTerm", "showComments", "setShowComments", "newComment", "setNewComment", "submittingComment", "setSubmittingComment", "selectedPinnedPost", "setSelectedPinnedPost", "lightboxOpen", "lightboxImages", "lightboxInitialIndex", "announcements", "setAnnouncements", "pinnedAnnouncements", "setPinnedAnnouncements", "calendarEvents", "setCalendarEvents", "calendarLoading", "setCalendarLoading", "calendarError", "setCalendarError", "recentStudents", "setRecentStudents", "studentLoading", "setStudentLoading", "categories", "fetchPublishedAnnouncements", "undefined", "data", "json", "success", "announcementsData", "pinned", "ann", "is_pinned", "fetchCalendarEvents", "eventsData", "events", "eventsWithImages", "Promise", "all", "event", "imageResponse", "calendar_id", "imageData", "attachments", "imgErr", "warn", "fetchRecentStudents", "students", "handleLikeToggle", "announcement", "user_reaction", "removeReaction", "announcement_id", "body", "JSON", "stringify", "reaction_type_id", "notify_admin", "handleCommentSubmit", "announcementId", "commentText", "trim", "content", "commenter_name", "commenter_email", "prev", "filteredAnnouncements", "_announcement$categor", "_announcement$grade_l", "matchesSearch", "title", "toLowerCase", "includes", "matchesCategory", "category_id", "toString", "matchesGradeLevel", "grade_level", "filteredCalendarEvents", "description", "today", "Date", "setHours", "eventDate", "event_date", "isEventDateValid", "isPublished", "is_published", "displayAnnouncements", "displayEvents", "combinedContent", "item", "type", "sortDate", "created_at", "sort", "a", "b", "getTime", "minHeight", "backgroundImage", "pointerEvents", "zIndex", "borderBottom", "boxShadow", "padding", "min<PERSON><PERSON><PERSON>", "margin", "lineHeight", "flex", "max<PERSON><PERSON><PERSON>", "size", "placeholder", "value", "onChange", "target", "outline", "onFocus", "borderColor", "onBlur", "category", "name", "flexShrink", "<PERSON><PERSON><PERSON><PERSON>", "is_alert", "category_name", "toUpperCase", "categoryStyle", "substring", "toLocaleDateString", "opacity", "borderTop", "animation", "marginLeft", "marginRight", "<PERSON><PERSON>ilter", "student", "_student$profile", "_student$profile2", "_student$profile3", "profile", "first_name", "last_name", "student_number", "student_id", "holiday_type_name", "holidayStyle", "IconComponent", "textTransform", "letterSpacing", "weekday", "year", "month", "day", "eventImageUrls", "for<PERSON>ach", "push", "maxVisible", "filteredImages", "end_date", "flexWrap", "fill", "reaction_count", "allow_comments", "comment_count", "view_count", "posted_by_name", "paddingTop", "allowComments", "currentUserType", "maxHeight", "stopPropagation", "author_name", "isOpen", "onClose", "_c3", "$RefreshReg$"], "sources": ["D:/capstone-e-bulletin-reactjs-proj/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/AdminNewsfeed.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { announcementService } from '../../services';\nimport { useCategories } from '../../hooks/useAnnouncements';\nimport AdminCommentSection from '../../components/admin/AdminCommentSection';\nimport FacebookImageGallery from '../../components/common/FacebookImageGallery';\nimport ImageLightbox from '../../components/common/ImageLightbox';\nimport type { AnnouncementAttachment } from '../../services/announcementService';\nimport type { CalendarEvent } from '../../types/calendar.types';\nimport { getImageUrl, API_BASE_URL } from '../../config/constants';\nimport {\n  Newspaper,\n  Search,\n  Pin,\n  Calendar,\n  MessageSquare,\n  Heart,\n  Eye,\n  Edit,\n  Users,\n  LayoutDashboard,\n  BookOpen,\n  PartyPopper,\n  AlertTriangle,\n  Clock,\n  Trophy,\n  Briefcase,\n  GraduationCap,\n  Flag,\n  Coffee,\n  Plane\n} from 'lucide-react';\n\n// Custom hook for CORS-safe image loading\nconst useImageLoader = (imagePath: string | null) => {\n  const [imageUrl, setImageUrl] = useState<string | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n\n        console.log('🔄 Fetching image via CORS-safe method:', fullUrl);\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n            'Origin': window.location.origin,\n          },\n          mode: 'cors',\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n        setImageUrl(objectUrl);\n\n        console.log('✅ Image loaded successfully via fetch');\n\n      } catch (err) {\n        console.error('❌ Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadImage();\n\n    // Cleanup object URL on unmount\n    return () => {\n      if (imageUrl && imageUrl.startsWith('blob:')) {\n        URL.revokeObjectURL(imageUrl);\n      }\n    };\n  }, [imagePath]);\n\n  return { imageUrl, loading, error };\n};\n\n// CORS-safe image component\ninterface ImageDisplayProps {\n  imagePath: string | null;\n  alt: string;\n  style?: React.CSSProperties;\n  className?: string;\n  onLoad?: (e: React.SyntheticEvent<HTMLImageElement>) => void;\n  onMouseEnter?: (e: React.MouseEvent<HTMLImageElement>) => void;\n  onMouseLeave?: (e: React.MouseEvent<HTMLImageElement>) => void;\n}\n\nconst ImageDisplay: React.FC<ImageDisplayProps> = ({\n  imagePath,\n  alt,\n  style,\n  className,\n  onLoad,\n  onMouseEnter,\n  onMouseLeave\n}) => {\n  const { imageUrl, loading, error } = useImageLoader(imagePath);\n\n  if (loading) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b'\n      }} className={className}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>⏳</div>\n          <div style={{ fontSize: '0.875rem' }}>Loading...</div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !imageUrl) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b',\n        border: '2px dashed #cbd5e1'\n      }} className={className}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>🖼️</div>\n          <div style={{ fontSize: '0.875rem', fontWeight: '500' }}>Image unavailable</div>\n          {error && (\n            <div style={{ fontSize: '0.75rem', marginTop: '0.25rem', color: '#9ca3af' }}>\n              {error}\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <img\n      src={imageUrl}\n      alt={alt}\n      style={style}\n      className={className}\n      onLoad={(e) => {\n        console.log('✅ Image rendered successfully via CORS-safe method');\n        onLoad?.(e);\n      }}\n      onMouseEnter={onMouseEnter}\n      onMouseLeave={onMouseLeave}\n    />\n  );\n};\n\n// Image Gallery Component\ninterface ImageGalleryProps {\n  images: AnnouncementAttachment[];\n  altPrefix: string;\n  onImageClick?: (index: number) => void;\n}\n\nconst ImageGallery: React.FC<ImageGalleryProps> = ({ images, altPrefix, onImageClick }) => {\n  if (!images || images.length === 0) return null;\n\n  const visibleImages = images.slice(0, 4);\n  const remainingCount = Math.max(0, images.length - 4);\n\n  const getContainerStyle = (index: number, total: number): React.CSSProperties => {\n    const baseStyle: React.CSSProperties = {\n      position: 'relative',\n      overflow: 'hidden',\n      borderRadius: '12px',\n      cursor: onImageClick ? 'pointer' : 'default'\n    };\n\n    if (total === 1) {\n      return { ...baseStyle, width: '100%', height: '300px' };\n    } else if (total === 2) {\n      return { ...baseStyle, width: '50%', height: '250px' };\n    } else if (total === 3) {\n      if (index === 0) {\n        return { ...baseStyle, width: '50%', height: '250px' };\n      } else {\n        return { ...baseStyle, width: '50%', height: '120px' };\n      }\n    } else {\n      if (index === 0) {\n        return { ...baseStyle, width: '50%', height: '250px' };\n      } else {\n        return { ...baseStyle, width: '33.33%', height: '120px' };\n      }\n    }\n  };\n\n  const getImageStyle = (index: number, total: number): React.CSSProperties => {\n    return {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover' as const,\n      transition: 'transform 0.3s ease'\n    };\n  };\n\n  return (\n    <div style={{\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1rem'\n    }}>\n      {/* Main image or left side */}\n      <div style={getContainerStyle(0, visibleImages.length)}>\n        <ImageDisplay\n          imagePath={visibleImages[0].file_path}\n          alt={`${altPrefix} - Image 1`}\n          style={getImageStyle(0, visibleImages.length)}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.transform = 'scale(1.02)';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.transform = 'scale(1)';\n          }}\n        />\n        {onImageClick && (\n          <div\n            style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            }}\n            onClick={() => onImageClick(0)}\n          />\n        )}\n      </div>\n\n      {/* Right side images */}\n      {visibleImages.length > 1 && (\n        <div style={{\n          display: 'flex',\n          flexDirection: visibleImages.length === 2 ? 'row' : 'column',\n          gap: '4px',\n          width: visibleImages.length === 2 ? '50%' : '50%'\n        }}>\n          {visibleImages.slice(1).map((image, idx) => {\n            const actualIndex = idx + 1;\n            const isLast = actualIndex === visibleImages.length - 1 && remainingCount > 0;\n            \n            return (\n              <div\n                key={actualIndex}\n                style={{\n                  ...getContainerStyle(actualIndex, visibleImages.length),\n                  position: 'relative'\n                }}\n              >\n                <ImageDisplay\n                  imagePath={image.file_path}\n                  alt={`${altPrefix} - Image ${actualIndex + 1}`}\n                  style={getImageStyle(actualIndex, visibleImages.length)}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'scale(1.02)';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'scale(1)';\n                  }}\n                />\n                \n                {/* Overlay for remaining images count */}\n                {isLast && remainingCount > 0 && (\n                  <div style={{\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: 'white',\n                    fontSize: '1.5rem',\n                    fontWeight: '600'\n                  }}>\n                    +{remainingCount}\n                  </div>\n                )}\n                \n                {onImageClick && (\n                  <div\n                    style={{\n                      position: 'absolute',\n                      top: 0,\n                      left: 0,\n                      right: 0,\n                      bottom: 0,\n                      cursor: 'pointer'\n                    }}\n                    onClick={() => onImageClick(actualIndex)}\n                  />\n                )}\n              </div>\n            );\n          })}\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Main AdminNewsfeed Component\nconst AdminNewsfeed: React.FC = () => {\n  const navigate = useNavigate();\n\n  // Open lightbox function for announcements\n  const openLightbox = (images: AnnouncementAttachment[], initialIndex: number) => {\n    const imageUrls = images.map(img => getImageUrl(img.file_path)).filter(Boolean) as string[];\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Open lightbox function for image URLs (calendar events)\n  const openLightboxWithUrls = (imageUrls: string[], initialIndex: number) => {\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Category styling function\n  const getCategoryStyle = (categoryName: string) => {\n    const styles = {\n      'ACADEMIC': {\n        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n        icon: BookOpen\n      },\n      'GENERAL': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Users\n      },\n      'EVENTS': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: PartyPopper\n      },\n      'EMERGENCY': {\n        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n        icon: AlertTriangle\n      },\n      'SPORTS': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Trophy\n      },\n      'DEADLINES': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Clock\n      }\n    };\n\n    return styles[categoryName as keyof typeof styles] || styles['GENERAL'];\n  };\n\n  // Holiday type styling function\n  const getHolidayTypeStyle = (holidayTypeName: string) => {\n    const styles = {\n      'National Holiday': {\n        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n        icon: Flag\n      },\n      'School Event': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: GraduationCap\n      },\n      'Academic Break': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Coffee\n      },\n      'Sports Event': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Trophy\n      },\n      'Field Trip': {\n        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n        icon: Plane\n      },\n      'Meeting': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Briefcase\n      }\n    };\n\n    return styles[holidayTypeName as keyof typeof styles] || styles['School Event'];\n  };\n\n  // Filter states\n  const [filterCategory, setFilterCategory] = useState<string>('');\n  const [filterGradeLevel, setFilterGradeLevel] = useState<string>('');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // UI states\n  const [showComments, setShowComments] = useState<number | null>(null);\n  const [newComment, setNewComment] = useState<{ [key: number]: string }>({});\n  const [submittingComment, setSubmittingComment] = useState<number | null>(null);\n  const [selectedPinnedPost, setSelectedPinnedPost] = useState<any | null>(null);\n\n  // Lightbox states\n  const [lightboxOpen, setLightboxOpen] = useState(false);\n  const [lightboxImages, setLightboxImages] = useState<string[]>([]);\n  const [lightboxInitialIndex, setLightboxInitialIndex] = useState(0);\n\n  // Data states\n  const [announcements, setAnnouncements] = useState<any[]>([]);\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState<any[]>([]);\n  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | undefined>();\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState<string | undefined>();\n  const [recentStudents, setRecentStudents] = useState<any[]>([]);\n  const [studentLoading, setStudentLoading] = useState(false);\n\n  const { categories } = useCategories();\n\n  // Fetch published announcements with images (admin can see all)\n  const fetchPublishedAnnouncements = async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n\n      const response = await fetch(`${API_BASE_URL}/api/announcements?status=published&page=1&limit=50&sort_by=created_at&sort_order=DESC`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        const announcementsData = data.data.announcements || [];\n\n        // The announcements already include attachments/images from the API\n        // No need to fetch images separately\n        setAnnouncements(announcementsData);\n\n        // Separate pinned announcements\n        const pinned = announcementsData.filter((ann: any) => ann.is_pinned === 1);\n        setPinnedAnnouncements(pinned);\n      } else {\n        setError('Failed to load announcements');\n      }\n    } catch (err: any) {\n      console.error('Error fetching announcements:', err);\n      setError(err.message || 'Failed to load announcements');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch calendar events\n  const fetchCalendarEvents = async () => {\n    try {\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n\n      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(\n          eventsData.map(async (event: any) => {\n            try {\n              const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`, {\n                headers: {\n                  'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n                  'Content-Type': 'application/json'\n                }\n              });\n              const imageData = await imageResponse.json();\n\n              if (imageData.success && imageData.data) {\n                event.images = imageData.data.attachments || [];\n              } else {\n                event.images = [];\n              }\n            } catch (imgErr) {\n              console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n              event.images = [];\n            }\n            return event;\n          })\n        );\n\n        setCalendarEvents(eventsWithImages);\n      } else {\n        setCalendarError('Failed to load calendar events');\n      }\n    } catch (err: any) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to load calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  };\n\n  // Fetch recent student registrations\n  const fetchRecentStudents = async () => {\n    try {\n      setStudentLoading(true);\n      const response = await fetch(`${API_BASE_URL}/api/students?page=1&limit=5&sort_by=created_at&sort_order=DESC`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        setRecentStudents(data.data.students || []);\n      }\n    } catch (err: any) {\n      console.error('Error fetching recent students:', err);\n    } finally {\n      setStudentLoading(false);\n    }\n  };\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchPublishedAnnouncements();\n    fetchCalendarEvents();\n    // fetchRecentStudents(); // Commented out - endpoint doesn't exist yet\n  }, []);\n\n  // Handle like/unlike functionality (admin perspective)\n  const handleLikeToggle = async (announcement: any) => {\n    try {\n      if (announcement.user_reaction) {\n        await announcementService.removeReaction(announcement.announcement_id);\n      } else {\n        const response = await fetch(`${API_BASE_URL}/api/announcements/${announcement.announcement_id}/reactions`, {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            reaction_type_id: 1,\n            notify_admin: false // Admin doesn't need to notify themselves\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error('Failed to add reaction');\n        }\n      }\n\n      // Refresh data\n      await fetchPublishedAnnouncements();\n    } catch (error) {\n      console.error('Error toggling like:', error);\n    }\n  };\n\n  // Handle comment submission (admin perspective)\n  const handleCommentSubmit = async (announcementId: number, commentText: string) => {\n    if (!commentText.trim()) return;\n\n    try {\n      setSubmittingComment(announcementId);\n\n      const response = await fetch(`${API_BASE_URL}/api/admin/announcements/${announcementId}/comments`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          content: commentText,\n          commenter_name: 'Admin User',\n          commenter_email: '<EMAIL>',\n          notify_admin: false\n        })\n      });\n\n      if (response.ok) {\n        setNewComment(prev => ({ ...prev, [announcementId]: '' }));\n        await fetchPublishedAnnouncements();\n      } else {\n        console.error('Failed to submit comment');\n      }\n    } catch (error) {\n      console.error('Error submitting comment:', error);\n    } finally {\n      setSubmittingComment(null);\n    }\n  };\n\n  // Filter announcements\n  const filteredAnnouncements = announcements.filter(announcement => {\n    const matchesSearch = !searchTerm ||\n      announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      announcement.content.toLowerCase().includes(searchTerm.toLowerCase());\n\n    const matchesCategory = !filterCategory ||\n      announcement.category_id?.toString() === filterCategory;\n\n    const matchesGradeLevel = !filterGradeLevel ||\n      announcement.grade_level?.toString() === filterGradeLevel;\n\n    return matchesSearch && matchesCategory && matchesGradeLevel;\n  });\n\n  // Filter calendar events with date-based filtering\n  const filteredCalendarEvents = calendarEvents.filter(event => {\n    const matchesSearch = !searchTerm ||\n      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      (event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    // Only show events that are published and on or after today's date\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    const eventDate = new Date(event.event_date);\n    eventDate.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    const isEventDateValid = eventDate >= today;\n    const isPublished = (event as any).is_published === 1;\n\n    return matchesSearch && isEventDateValid && isPublished;\n  });\n\n  // Combine and sort all content by date (most recent first)\n  const displayAnnouncements = filteredAnnouncements;\n  const displayEvents = filteredCalendarEvents;\n\n  // Create combined content array for better chronological display\n  const combinedContent = [\n    ...displayAnnouncements.map(item => ({ ...item, type: 'announcement', sortDate: new Date(item.created_at) })),\n    ...displayEvents.map(item => ({ ...item, type: 'event', sortDate: new Date(item.event_date) }))\n  ].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)',\n      position: 'relative'\n    }}>\n      {/* Background Pattern */}\n      <div style={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundImage: `\n          radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.03) 0%, transparent 50%),\n          radial-gradient(circle at 75% 75%, rgba(250, 204, 21, 0.03) 0%, transparent 50%)\n        `,\n        pointerEvents: 'none'\n      }} />\n\n      <div style={{ position: 'relative', zIndex: 1 }}>\n        {/* Modern Admin Header */}\n        <header style={{\n          background: 'white',\n          borderBottom: '1px solid #e5e7eb',\n          position: 'sticky',\n          top: 0,\n          zIndex: 100,\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n        }}>\n          <div style={{\n            padding: '0 2rem',\n            height: '72px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          }}>\n            {/* Left Section: Logo + Page Title */}\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1.5rem',\n              minWidth: '300px'\n            }}>\n              <img\n                src=\"/logo/vcba1.png\"\n                alt=\"VCBA Logo\"\n                style={{\n                  width: '48px',\n                  height: '48px',\n                  objectFit: 'contain'\n                }}\n              />\n              <div>\n                <h1 style={{\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827',\n                  lineHeight: '1.2'\n                }}>\n                  VCBA E-Bulletin Board\n                </h1>\n                <p style={{\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280',\n                  lineHeight: '1.2'\n                }}>\n                  Admin Newsfeed\n                </p>\n              </div>\n            </div>\n\n            {/* Center Section: Search */}\n            <div style={{\n              flex: 1,\n              maxWidth: '500px',\n              margin: '0 2rem'\n            }}>\n              <div style={{ position: 'relative' }}>\n                <Search\n                  size={20}\n                  style={{\n                    position: 'absolute',\n                    left: '1rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: '#9ca3af'\n                  }}\n                />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search post\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  style={{\n                    width: '100%',\n                    height: '44px',\n                    padding: '0 1rem 0 3rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '12px',\n                    background: '#f9fafb',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onFocus={(e) => {\n                    e.currentTarget.style.borderColor = '#22c55e';\n                    e.currentTarget.style.background = 'white';\n                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n                  }}\n                  onBlur={(e) => {\n                    e.currentTarget.style.borderColor = '#d1d5db';\n                    e.currentTarget.style.background = '#f9fafb';\n                    e.currentTarget.style.boxShadow = 'none';\n                  }}\n                />\n              </div>\n            </div>\n\n            {/* Right Section: Navigation + Filters */}\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              minWidth: '400px',\n              justifyContent: 'flex-end'\n            }}>\n              \n              {/* Filters Group */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.5rem',\n                background: '#f9fafb',\n                borderRadius: '12px',\n                border: '1px solid #e5e7eb'\n              }}>\n                <select\n                  value={filterCategory}\n                  onChange={(e) => setFilterCategory(e.target.value)}\n                  style={{\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '110px'\n                  }}\n                >\n                  <option value=\"\">All Categories</option>\n                  {categories.map(category => (\n                    <option key={category.category_id} value={category.category_id.toString()}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n\n                <select\n                  value={filterGradeLevel}\n                  onChange={(e) => setFilterGradeLevel(e.target.value)}\n                  style={{\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '100px'\n                  }}\n                >\n                  <option value=\"\">All Grades</option>\n                  <option value=\"11\">Grade 11</option>\n                  <option value=\"12\">Grade 12</option>\n                </select>\n\n                {(searchTerm || filterCategory || filterGradeLevel) && (\n                  <button\n                    onClick={() => {\n                      setSearchTerm('');\n                      setFilterCategory('');\n                      setFilterGradeLevel('');\n                    }}\n                    style={{\n                      padding: '0.5rem 0.75rem',\n                      border: 'none',\n                      borderRadius: '8px',\n                      background: '#ef4444',\n                      color: 'white',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.background = '#dc2626';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.background = '#ef4444';\n                    }}\n                  >\n                    Clear\n                  </button>\n                )}\n              </div>\n\n              {/* Back to Dashboard Button */}\n              <button\n                onClick={() => navigate('/admin/dashboard')}\n                style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  padding: '0.75rem 1rem',\n                  background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                  border: 'none',\n                  borderRadius: '12px',\n                  color: 'white',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease',\n                  boxShadow: '0 2px 8px rgba(34, 197, 94, 0.2)'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.2)';\n                }}\n              >\n                <LayoutDashboard size={16} />\n                Dashboard\n              </button>\n            </div>\n          </div>\n        </header>\n\n\n\n        {/* Main Content Layout */}\n        <div style={{\n          padding: '2rem',\n          display: 'flex',\n          gap: '2rem',\n          alignItems: 'flex-start'\n        }}>\n          {/* Left Sidebar: Pinned Posts */}\n          <div style={{\n            width: '320px',\n            flexShrink: 0\n          }}>\n            <div style={{\n              background: 'white',\n              borderRadius: '16px',\n              border: '1px solid #e5e7eb',\n              overflow: 'hidden',\n              position: 'sticky',\n              top: '100px'\n            }}>\n              {/* Pinned Posts Header */}\n              <div style={{\n                padding: '1.5rem 1.5rem 1rem',\n                borderBottom: '1px solid #f3f4f6'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.75rem',\n                  marginBottom: '0.5rem'\n                }}>\n                  <Pin size={20} style={{ color: '#22c55e' }} />\n                  <h3 style={{\n                    margin: 0,\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#111827'\n                  }}>\n                    Pinned Posts\n                  </h3>\n                </div>\n                <p style={{\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                }}>\n                  Important announcements and updates\n                </p>\n              </div>\n\n              {/* Pinned Posts List */}\n              <div style={{ padding: '1rem' }}>\n                {pinnedAnnouncements.length > 0 ? (\n                  <>\n                    {pinnedAnnouncements.slice(0, 3).map((announcement, index) => {\n                      // Handle alert announcements with special styling\n                      const isAlert = announcement.is_alert;\n                      const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                      const categoryStyle = getCategoryStyle(categoryName);\n\n                      return (\n                        <div\n                          key={announcement.announcement_id}\n                          style={{\n                            padding: '1rem',\n                            background: isAlert ? '#fef2f2' : '#f8fafc',\n                            borderRadius: '12px',\n                            border: isAlert ? '1px solid #fecaca' : '1px solid #e2e8f0',\n                            marginBottom: '1rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.2s ease'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.background = isAlert ? '#fee2e2' : '#f1f5f9';\n                            e.currentTarget.style.borderColor = isAlert ? '#ef4444' : '#22c55e';\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.background = isAlert ? '#fef2f2' : '#f8fafc';\n                            e.currentTarget.style.borderColor = isAlert ? '#fecaca' : '#e2e8f0';\n                          }}\n                          onClick={() => setSelectedPinnedPost(announcement)}\n                        >\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'flex-start',\n                            gap: '0.75rem'\n                          }}>\n                            <div style={{\n                              width: '8px',\n                              height: '8px',\n                              background: isAlert ? '#ef4444' : (categoryStyle.background.includes('#ef4444') ? '#ef4444' : '#22c55e'),\n                              borderRadius: '50%',\n                              marginTop: '0.5rem',\n                              flexShrink: 0\n                            }} />\n                            <div style={{ flex: 1 }}>\n                              <h4 style={{\n                                margin: '0 0 0.5rem 0',\n                                fontSize: '0.875rem',\n                                fontWeight: '600',\n                                color: '#111827',\n                                lineHeight: '1.4'\n                              }}>\n                                {announcement.title}\n                              </h4>\n                              <p style={{\n                                margin: '0 0 0.5rem 0',\n                                fontSize: '0.8rem',\n                                color: '#6b7280',\n                                lineHeight: '1.4'\n                              }}>\n                                {announcement.content.length > 80\n                                  ? `${announcement.content.substring(0, 80)}...`\n                                  : announcement.content}\n                              </p>\n                              <div style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                fontSize: '0.75rem',\n                                color: '#9ca3af'\n                              }}>\n                                <Calendar size={12} />\n                                <span>{new Date(announcement.created_at).toLocaleDateString()}</span>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      );\n                    })}\n\n                    {pinnedAnnouncements.length > 3 && (\n                      <button style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #e5e7eb',\n                        borderRadius: '8px',\n                        background: 'white',\n                        color: '#22c55e',\n                        fontSize: '0.875rem',\n                        fontWeight: '500',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.background = '#f0fdf4';\n                        e.currentTarget.style.borderColor = '#22c55e';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.background = 'white';\n                        e.currentTarget.style.borderColor = '#e5e7eb';\n                      }}>\n                        View All {pinnedAnnouncements.length} Pinned Posts\n                      </button>\n                    )}\n                  </>\n                ) : (\n                  <div style={{\n                    padding: '2rem 1rem',\n                    textAlign: 'center',\n                    color: '#6b7280'\n                  }}>\n                    <Pin size={24} style={{ marginBottom: '0.5rem', opacity: 0.5 }} />\n                    <p style={{ margin: 0, fontSize: '0.875rem' }}>\n                      No pinned posts available\n                    </p>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Right Content: Main Feed */}\n          <div style={{ flex: 1, minWidth: 0 }}>\n          {/* Loading State */}\n          {(loading || calendarLoading) && (\n            <div style={{\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              minHeight: '400px'\n            }}>\n              <div style={{\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                gap: '1rem'\n              }}>\n                <div style={{\n                  width: '3rem',\n                  height: '3rem',\n                  border: '4px solid rgba(34, 197, 94, 0.2)',\n                  borderTop: '4px solid #22c55e',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }}></div>\n                <p style={{\n                  color: '#6b7280',\n                  fontSize: '1rem',\n                  fontWeight: '500'\n                }}>\n                  Loading content...\n                </p>\n              </div>\n            </div>\n          )}\n\n          {/* Error State */}\n          {(error || calendarError) && !loading && !calendarLoading && (\n            <div style={{\n              padding: '2rem',\n              background: 'rgba(239, 68, 68, 0.1)',\n              border: '1px solid rgba(239, 68, 68, 0.2)',\n              borderRadius: '16px',\n              textAlign: 'center'\n            }}>\n              <div style={{\n                width: '4rem',\n                height: '4rem',\n                background: 'rgba(239, 68, 68, 0.1)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 1rem'\n              }}>\n                <MessageSquare size={24} color=\"#ef4444\" />\n              </div>\n              <h3 style={{\n                color: '#ef4444',\n                margin: '0 0 0.5rem 0',\n                fontSize: '1.25rem',\n                fontWeight: '600'\n              }}>\n                Error Loading Content\n              </h3>\n              <p style={{\n                color: '#6b7280',\n                margin: '0 0 1.5rem 0',\n                fontSize: '1rem'\n              }}>\n                {error || calendarError}\n              </p>\n              <button\n                onClick={() => {\n                  fetchPublishedAnnouncements();\n                  fetchCalendarEvents();\n                }}\n                style={{\n                  background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '12px',\n                  padding: '0.75rem 1.5rem',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = 'none';\n                }}\n              >\n                Try Again\n              </button>\n            </div>\n          )}\n\n          {/* Empty State */}\n          {!loading && !calendarLoading && !error && !calendarError &&\n           displayAnnouncements.length === 0 && displayEvents.length === 0 && (\n            <div style={{\n              padding: '4rem 2rem',\n              textAlign: 'center'\n            }}>\n              <div style={{\n                width: '5rem',\n                height: '5rem',\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 2rem'\n              }}>\n                <Newspaper size={32} color=\"white\" />\n              </div>\n              <h3 style={{\n                color: '#374151',\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '600'\n              }}>\n                No Content Available\n              </h3>\n              <p style={{\n                color: '#6b7280',\n                margin: '0 0 2rem 0',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                maxWidth: '500px',\n                marginLeft: 'auto',\n                marginRight: 'auto'\n              }}>\n                {searchTerm || filterCategory || filterGradeLevel\n                  ? 'No content matches your current filters. Try adjusting your search criteria.'\n                  : 'There are no published announcements or events at the moment. Check back later for updates.'\n                }\n              </p>\n              {(searchTerm || filterCategory || filterGradeLevel) && (\n                <button\n                  onClick={() => {\n                    setSearchTerm('');\n                    setFilterCategory('');\n                    setFilterGradeLevel('');\n                  }}\n                  style={{\n                    background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '12px',\n                    padding: '0.75rem 1.5rem',\n                    fontSize: '0.875rem',\n                    fontWeight: '600',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'translateY(-1px)';\n                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = 'none';\n                  }}\n                >\n                  Clear Filters\n                </button>\n              )}\n            </div>\n          )}\n\n          {/* Recent Students Section (Admin Only) */}\n          {!studentLoading && recentStudents.length > 0 && (\n            <div style={{\n              background: 'rgba(255, 255, 255, 0.95)',\n              borderRadius: '16px',\n              padding: '1.5rem',\n              border: '1px solid rgba(0, 0, 0, 0.1)',\n              backdropFilter: 'blur(10px)',\n              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n              marginBottom: '1.5rem'\n            }}>\n              <h3 style={{\n                fontSize: '1.25rem',\n                fontWeight: '600',\n                color: '#2d5016',\n                margin: '0 0 1rem 0',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}>\n                👥 Recent Student Registrations\n              </h3>\n              <div style={{\n                display: 'grid',\n                gap: '0.75rem'\n              }}>\n                {recentStudents.slice(0, 3).map((student: any) => (\n                  <div\n                    key={student.student_id}\n                    style={{\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center',\n                      padding: '0.75rem',\n                      backgroundColor: '#f8fdf8',\n                      borderRadius: '8px',\n                      border: '1px solid #e8f5e8'\n                    }}\n                  >\n                    <div>\n                      <div style={{\n                        fontWeight: '500',\n                        color: '#2d5016',\n                        fontSize: '0.875rem'\n                      }}>\n                        {student.profile?.first_name} {student.profile?.last_name}\n                      </div>\n                      <div style={{\n                        fontSize: '0.75rem',\n                        color: '#6b7280'\n                      }}>\n                        Grade {student.profile?.grade_level} • {student.student_number}\n                      </div>\n                    </div>\n                    <div style={{\n                      fontSize: '0.75rem',\n                      color: '#6b7280'\n                    }}>\n                      {new Date(student.created_at).toLocaleDateString()}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* Content Feed */}\n          {!loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && (\n            <div style={{\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem'\n            }}>\n              {/* Calendar Events */}\n              {displayEvents.length > 0 && (\n                <>\n                  {displayEvents.map(event => (\n                    <div\n                      key={`event-${event.calendar_id}`}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Event Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'flex-start',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        <div style={{\n                          width: '48px',\n                          height: '48px',\n                          background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                          borderRadius: '12px',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          flexShrink: 0\n                        }}>\n                          <Calendar size={24} color=\"white\" />\n                        </div>\n\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            marginBottom: '0.5rem'\n                          }}>\n                            {(() => {\n                              const holidayTypeName = event.holiday_type_name || 'School Event';\n                              const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                              const IconComponent = holidayStyle.icon;\n\n                              return (\n                                <span style={{\n                                  background: holidayStyle.background,\n                                  color: 'white',\n                                  fontSize: '0.75rem',\n                                  fontWeight: '600',\n                                  padding: '0.25rem 0.75rem',\n                                  borderRadius: '20px',\n                                  textTransform: 'uppercase',\n                                  letterSpacing: '0.5px',\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: '0.25rem'\n                                }}>\n                                  <IconComponent size={12} color=\"white\" />\n                                  {holidayTypeName}\n                                </span>\n                              );\n                            })()}\n\n                            <div style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem',\n                              color: '#6b7280',\n                              fontSize: '0.875rem'\n                            }}>\n                              <Calendar size={14} />\n                              {new Date(event.event_date).toLocaleDateString('en-US', {\n                                weekday: 'long',\n                                year: 'numeric',\n                                month: 'long',\n                                day: 'numeric'\n                              })}\n                            </div>\n                          </div>\n\n                          <h3 style={{\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '1.25rem',\n                            fontWeight: '700',\n                            color: '#1f2937',\n                            lineHeight: '1.3'\n                          }}>\n                            {event.title}\n                          </h3>\n                        </div>\n\n                        {/* Admin Event Actions */}\n                        <div style={{ display: 'flex', gap: '0.5rem' }}>\n                          <button\n                            onClick={() => navigate(`/admin/calendar?event=${event.calendar_id}`)}\n                            style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem',\n                              padding: '0.5rem 0.75rem',\n                              background: 'rgba(59, 130, 246, 0.1)',\n                              border: '1px solid rgba(59, 130, 246, 0.2)',\n                              borderRadius: '8px',\n                              color: '#3b82f6',\n                              fontSize: '0.75rem',\n                              fontWeight: '500',\n                              cursor: 'pointer',\n                              transition: 'all 0.2s ease'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = 'rgba(59, 130, 246, 0.2)';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'rgba(59, 130, 246, 0.1)';\n                            }}\n                          >\n                            <Edit size={12} />\n                            Edit\n                          </button>\n                        </div>\n                      </div>\n\n                      {/* Event Content */}\n                      {event.description && (\n                        <div style={{\n                          color: '#4b5563',\n                          fontSize: '0.95rem',\n                          lineHeight: '1.6',\n                          marginBottom: '1rem'\n                        }}>\n                          {event.description}\n                        </div>\n                      )}\n\n                      {/* Event Images */}\n                      {(() => {\n                        // Get event images if they exist\n                        const eventImageUrls: string[] = [];\n\n                        if ((event as any).images && (event as any).images.length > 0) {\n                          (event as any).images.forEach((img: any) => {\n                            if (img.file_path) {\n                              // Convert file_path to full URL\n                              const imageUrl = getImageUrl(img.file_path);\n                              if (imageUrl) {\n                                eventImageUrls.push(imageUrl);\n                              }\n                            }\n                          });\n                        }\n\n                        return eventImageUrls.length > 0 ? (\n                          <div style={{ marginBottom: '1rem' }}>\n                            <FacebookImageGallery\n                              images={eventImageUrls.filter(Boolean) as string[]}\n                              altPrefix={event.title}\n                              maxVisible={4}\n                              onImageClick={(index) => {\n                                const filteredImages = eventImageUrls.filter(Boolean) as string[];\n                                openLightboxWithUrls(filteredImages, index);\n                              }}\n                            />\n                          </div>\n                        ) : null;\n                      })()}\n\n                      {/* Event Details */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1.5rem',\n                        padding: '1rem',\n                        background: 'rgba(59, 130, 246, 0.05)',\n                        borderRadius: '12px',\n                        fontSize: '0.875rem'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          color: '#6b7280'\n                        }}>\n                          <Calendar size={16} />\n                          <span>\n                            {event.end_date && event.end_date !== event.event_date\n                              ? `${new Date(event.event_date).toLocaleDateString()} - ${new Date(event.end_date).toLocaleDateString()}`\n                              : new Date(event.event_date).toLocaleDateString()\n                            }\n                          </span>\n                        </div>\n\n                        {event.holiday_type_name && (\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            color: '#6b7280'\n                          }}>\n                            <span style={{\n                              padding: '0.25rem 0.5rem',\n                              background: 'rgba(59, 130, 246, 0.1)',\n                              borderRadius: '6px',\n                              fontSize: '0.75rem',\n                              fontWeight: '500'\n                            }}>\n                              {event.holiday_type_name}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </>\n              )}\n\n              {/* Announcements */}\n              {displayAnnouncements.length > 0 && (\n                <>\n                  {displayAnnouncements.map(announcement => (\n                    <div\n                      key={`announcement-${announcement.announcement_id}`}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: announcement.is_pinned\n                          ? '2px solid rgba(250, 204, 21, 0.3)'\n                          : '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: announcement.is_pinned\n                          ? '0 4px 20px rgba(250, 204, 21, 0.15)'\n                          : '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease',\n                        position: 'relative'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = announcement.is_pinned\n                          ? '0 8px 30px rgba(250, 204, 21, 0.25)'\n                          : '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = announcement.is_pinned\n                          ? '0 4px 20px rgba(250, 204, 21, 0.15)'\n                          : '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Pinned Badge */}\n                      {announcement.is_pinned && (\n                        <div style={{\n                          position: 'absolute',\n                          top: '-8px',\n                          right: '1rem',\n                          background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                          color: 'white',\n                          padding: '0.25rem 0.75rem',\n                          borderRadius: '12px',\n                          fontSize: '0.75rem',\n                          fontWeight: '600',\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.25rem',\n                          boxShadow: '0 2px 8px rgba(250, 204, 21, 0.3)'\n                        }}>\n                          <Pin size={12} />\n                          Pinned\n                        </div>\n                      )}\n\n                      {/* Announcement Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'flex-start',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        {(() => {\n                          if (announcement.is_alert) {\n                            return (\n                              <div style={{\n                                width: '48px',\n                                height: '48px',\n                                background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                                borderRadius: '12px',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                flexShrink: 0\n                              }}>\n                                <AlertTriangle size={24} color=\"white\" />\n                              </div>\n                            );\n                          } else {\n                            const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                            const categoryStyle = getCategoryStyle(categoryName);\n                            const IconComponent = categoryStyle.icon;\n\n                            return (\n                              <div style={{\n                                width: '48px',\n                                height: '48px',\n                                background: categoryStyle.background,\n                                borderRadius: '12px',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                flexShrink: 0\n                              }}>\n                                <IconComponent size={24} color=\"white\" />\n                              </div>\n                            );\n                          }\n                        })()}\n\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            marginBottom: '0.5rem',\n                            flexWrap: 'wrap'\n                          }}>\n                            {(() => {\n                              if (announcement.is_alert) {\n                                return (\n                                  <span style={{\n                                    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                                    color: 'white',\n                                    fontSize: '0.75rem',\n                                    fontWeight: '600',\n                                    padding: '0.25rem 0.75rem',\n                                    borderRadius: '20px',\n                                    textTransform: 'uppercase',\n                                    letterSpacing: '0.5px',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.25rem'\n                                  }}>\n                                    <AlertTriangle size={12} color=\"white\" />\n                                    Alert\n                                  </span>\n                                );\n                              } else {\n                                const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                                const categoryStyle = getCategoryStyle(categoryName);\n                                const IconComponent = categoryStyle.icon;\n\n                                return (\n                                  <span style={{\n                                    background: categoryStyle.background,\n                                    color: 'white',\n                                    fontSize: '0.75rem',\n                                    fontWeight: '600',\n                                    padding: '0.25rem 0.75rem',\n                                    borderRadius: '20px',\n                                    textTransform: 'uppercase',\n                                    letterSpacing: '0.5px',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.25rem'\n                                  }}>\n                                    <IconComponent size={12} color=\"white\" />\n                                    {categoryName}\n                                  </span>\n                                );\n                              }\n                            })()}\n\n                            {announcement.grade_level && (\n                              <span style={{\n                                background: 'rgba(59, 130, 246, 0.1)',\n                                color: '#3b82f6',\n                                fontSize: '0.75rem',\n                                fontWeight: '500',\n                                padding: '0.25rem 0.75rem',\n                                borderRadius: '20px'\n                              }}>\n                                Grade {announcement.grade_level}\n                              </span>\n                            )}\n\n                            <div style={{\n                              color: '#6b7280',\n                              fontSize: '0.875rem'\n                            }}>\n                              {new Date(announcement.created_at).toLocaleDateString('en-US', {\n                                weekday: 'short',\n                                year: 'numeric',\n                                month: 'short',\n                                day: 'numeric'\n                              })}\n                            </div>\n                          </div>\n\n                          <h3 style={{\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '1.25rem',\n                            fontWeight: '700',\n                            color: '#1f2937',\n                            lineHeight: '1.3'\n                          }}>\n                            {announcement.title}\n                          </h3>\n                        </div>\n\n                        {/* Admin Announcement Actions */}\n                        <div style={{ display: 'flex', gap: '0.5rem' }}>\n                          <button\n                            onClick={() => navigate(`/admin/posts?edit=${announcement.announcement_id}`)}\n                            style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem',\n                              padding: '0.5rem 0.75rem',\n                              background: 'rgba(34, 197, 94, 0.1)',\n                              border: '1px solid rgba(34, 197, 94, 0.2)',\n                              borderRadius: '8px',\n                              color: '#22c55e',\n                              fontSize: '0.75rem',\n                              fontWeight: '500',\n                              cursor: 'pointer',\n                              transition: 'all 0.2s ease'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = 'rgba(34, 197, 94, 0.2)';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'rgba(34, 197, 94, 0.1)';\n                            }}\n                          >\n                            <Edit size={12} />\n                            Edit\n                          </button>\n                        </div>\n                      </div>\n\n                      {/* Announcement Content */}\n                      <div style={{\n                        color: '#4b5563',\n                        fontSize: '0.95rem',\n                        lineHeight: '1.6',\n                        marginBottom: '1rem'\n                      }}>\n                        {announcement.content}\n                      </div>\n\n                      {/* Images */}\n                      {announcement.attachments && announcement.attachments.length > 0 && (\n                        <ImageGallery\n                          images={announcement.attachments}\n                          altPrefix={announcement.title}\n                          onImageClick={(index) => {\n                            openLightbox(announcement.attachments, index);\n                          }}\n                        />\n                      )}\n\n                      {/* Announcement Stats & Actions */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        padding: '1rem',\n                        background: 'rgba(0, 0, 0, 0.02)',\n                        borderRadius: '12px',\n                        marginBottom: '1rem'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1.5rem'\n                        }}>\n                          {/* Like Button */}\n                          <button\n                            onClick={() => handleLikeToggle(announcement)}\n                            style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.5rem',\n                              background: 'none',\n                              border: 'none',\n                              color: announcement.user_reaction ? '#ef4444' : '#6b7280',\n                              cursor: 'pointer',\n                              padding: '0.5rem',\n                              borderRadius: '8px',\n                              transition: 'all 0.2s ease',\n                              fontSize: '0.875rem',\n                              fontWeight: '500'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'none';\n                            }}\n                          >\n                            <Heart\n                              size={18}\n                              fill={announcement.user_reaction ? '#ef4444' : 'none'}\n                            />\n                            <span>{announcement.reaction_count || 0}</span>\n                          </button>\n\n                          {/* Comments Button */}\n                          {announcement.allow_comments && (\n                            <button\n                              onClick={() => setShowComments(\n                                showComments === announcement.announcement_id ? null : announcement.announcement_id\n                              )}\n                              style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                background: 'none',\n                                border: 'none',\n                                color: showComments === announcement.announcement_id ? '#22c55e' : '#6b7280',\n                                cursor: 'pointer',\n                                padding: '0.5rem',\n                                borderRadius: '8px',\n                                transition: 'all 0.2s ease',\n                                fontSize: '0.875rem',\n                                fontWeight: '500'\n                              }}\n                              onMouseEnter={(e) => {\n                                e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                                e.currentTarget.style.color = '#22c55e';\n                              }}\n                              onMouseLeave={(e) => {\n                                e.currentTarget.style.background = 'none';\n                                e.currentTarget.style.color = showComments === announcement.announcement_id ? '#22c55e' : '#6b7280';\n                              }}\n                            >\n                              <MessageSquare size={18} />\n                              <span>{announcement.comment_count || 0}</span>\n                            </button>\n                          )}\n\n                          {/* Views Count */}\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            color: '#6b7280',\n                            fontSize: '0.875rem'\n                          }}>\n                            <Eye size={18} />\n                            <span>{announcement.view_count || 0} views</span>\n                          </div>\n                        </div>\n\n                        {/* Admin Stats */}\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem',\n                          fontSize: '0.75rem',\n                          color: '#6b7280'\n                        }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.25rem'\n                          }}>\n                            <Users size={14} />\n                            <span>Posted by {announcement.posted_by_name || 'Admin'}</span>\n                          </div>\n\n                          <div style={{\n                            padding: '0.25rem 0.5rem',\n                            background: announcement.status === 'published'\n                              ? 'rgba(34, 197, 94, 0.1)'\n                              : 'rgba(107, 114, 128, 0.1)',\n                            color: announcement.status === 'published' ? '#22c55e' : '#6b7280',\n                            borderRadius: '6px',\n                            fontWeight: '500'\n                          }}>\n                            {announcement.status}\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Comments Section */}\n                      {showComments === announcement.announcement_id && announcement.allow_comments && (\n                        <div style={{\n                          marginTop: '1rem',\n                          paddingTop: '1rem',\n                          borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                        }}>\n                          <AdminCommentSection\n                            announcementId={announcement.announcement_id}\n                            allowComments={announcement.allow_comments}\n                            currentUserType=\"admin\"\n                          />\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </>\n              )}\n            </div>\n          )}\n          </div>\n        </div>\n      </div>\n\n      {/* Pinned Post Dialog */}\n      {selectedPinnedPost && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000,\n          padding: '2rem'\n        }}\n        onClick={() => setSelectedPinnedPost(null)}\n        >\n          <div style={{\n            backgroundColor: 'white',\n            borderRadius: '16px',\n            maxWidth: '600px',\n            width: '100%',\n            maxHeight: '80vh',\n            overflow: 'auto',\n            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n          }}\n          onClick={(e) => e.stopPropagation()}\n          >\n            {/* Dialog Header */}\n            <div style={{\n              padding: '1.5rem',\n              borderBottom: '1px solid #e5e7eb',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem'\n              }}>\n                <Pin size={20} style={{ color: '#22c55e' }} />\n                <h3 style={{\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827'\n                }}>\n                  Pinned Post\n                </h3>\n              </div>\n              <button\n                onClick={() => setSelectedPinnedPost(null)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  fontSize: '1.5rem',\n                  color: '#6b7280',\n                  cursor: 'pointer',\n                  padding: '0.25rem',\n                  borderRadius: '4px',\n                  transition: 'color 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.color = '#374151';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.color = '#6b7280';\n                }}\n              >\n                ×\n              </button>\n            </div>\n\n            {/* Dialog Content */}\n            <div style={{ padding: '1.5rem' }}>\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem',\n                marginBottom: '1rem'\n              }}>\n                {(() => {\n                  const categoryName = (selectedPinnedPost.category_name || 'GENERAL').toUpperCase();\n                  const categoryStyle = getCategoryStyle(categoryName);\n                  const IconComponent = categoryStyle.icon;\n\n                  return (\n                    <span style={{\n                      background: categoryStyle.background,\n                      color: 'white',\n                      fontSize: '0.75rem',\n                      fontWeight: '600',\n                      padding: '0.25rem 0.75rem',\n                      borderRadius: '20px',\n                      textTransform: 'uppercase',\n                      letterSpacing: '0.5px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.25rem'\n                    }}>\n                      <IconComponent size={12} color=\"white\" />\n                      {categoryName}\n                    </span>\n                  );\n                })()}\n\n                <span style={{\n                  background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                  color: 'white',\n                  padding: '0.25rem 0.75rem',\n                  borderRadius: '12px',\n                  fontSize: '0.75rem',\n                  fontWeight: '600',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                }}>\n                  <Pin size={12} />\n                  PINNED\n                </span>\n              </div>\n\n              <h2 style={{\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '700',\n                color: '#111827',\n                lineHeight: '1.3'\n              }}>\n                {selectedPinnedPost.title}\n              </h2>\n\n              <div style={{\n                color: '#4b5563',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                marginBottom: '1.5rem'\n              }}>\n                {selectedPinnedPost.content}\n              </div>\n\n              {/* Images */}\n              {selectedPinnedPost.attachments && selectedPinnedPost.attachments.length > 0 && (\n                <div style={{ marginBottom: '1.5rem' }}>\n                  <ImageGallery\n                    images={selectedPinnedPost.attachments}\n                    altPrefix={selectedPinnedPost.title}\n                    onImageClick={(index) => {\n                      openLightbox(selectedPinnedPost.attachments, index);\n                    }}\n                  />\n                </div>\n              )}\n\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem',\n                fontSize: '0.875rem',\n                color: '#6b7280',\n                paddingTop: '1rem',\n                borderTop: '1px solid #e5e7eb'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                }}>\n                  <Calendar size={16} />\n                  <span>Published: {new Date(selectedPinnedPost.created_at).toLocaleDateString()}</span>\n                </div>\n                {selectedPinnedPost.author_name && (\n                  <div>\n                    By: {selectedPinnedPost.author_name}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Image Lightbox */}\n      <ImageLightbox\n        images={lightboxImages}\n        initialIndex={lightboxInitialIndex}\n        isOpen={lightboxOpen}\n        onClose={() => setLightboxOpen(false)}\n        altPrefix=\"Announcement Image\"\n      />\n    </div>\n  );\n};\n\nexport default AdminNewsfeed;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,OAAOC,mBAAmB,MAAM,4CAA4C;AAC5E,OAAOC,oBAAoB,MAAM,8CAA8C;AAC/E,OAAOC,aAAa,MAAM,uCAAuC;AAGjE,SAASC,WAAW,EAAEC,YAAY,QAAQ,wBAAwB;AAClE,SACEC,SAAS,EACTC,MAAM,EACNC,GAAG,EACHC,QAAQ,EACRC,aAAa,EACbC,KAAK,EACLC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,eAAe,EACfC,QAAQ,EACRC,WAAW,EACXC,aAAa,EACbC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,aAAa,EACbC,IAAI,EACJC,MAAM,EACNC,KAAK,QACA,cAAc;;AAErB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAIC,SAAwB,IAAK;EAAAC,EAAA;EACnD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGtC,QAAQ,CAAgB,IAAI,CAAC;EAC7D,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,IAAI,CAACkC,SAAS,EAAE;MACdG,WAAW,CAAC,IAAI,CAAC;MACjB;IACF;IAEA,MAAMK,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI;QACF,MAAME,OAAO,GAAGpC,WAAW,CAAC2B,SAAS,CAAC;QACtC,IAAI,CAACS,OAAO,EAAE;UACZ,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;QACvC;QAEAC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEH,OAAO,CAAC;;QAE/D;QACA,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAACL,OAAO,EAAE;UACpCM,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;YAC/D,QAAQ,EAAEC,MAAM,CAACC,QAAQ,CAACC;UAC5B,CAAC;UACDC,IAAI,EAAE;QACR,CAAC,CAAC;QAEF,IAAI,CAACT,QAAQ,CAACU,EAAE,EAAE;UAChB,MAAM,IAAIb,KAAK,CAAC,QAAQG,QAAQ,CAACW,MAAM,KAAKX,QAAQ,CAACY,UAAU,EAAE,CAAC;QACpE;QAEA,MAAMC,IAAI,GAAG,MAAMb,QAAQ,CAACa,IAAI,CAAC,CAAC;QAClC,MAAMC,SAAS,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;QAC3CvB,WAAW,CAACwB,SAAS,CAAC;QAEtBhB,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MAEtD,CAAC,CAAC,OAAOkB,GAAG,EAAE;QACZnB,OAAO,CAACL,KAAK,CAAC,uBAAuB,EAAEwB,GAAG,CAAC;QAC3CvB,QAAQ,CAACuB,GAAG,YAAYpB,KAAK,GAAGoB,GAAG,CAACC,OAAO,GAAG,sBAAsB,CAAC;MACvE,CAAC,SAAS;QACR1B,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,SAAS,CAAC,CAAC;;IAEX;IACA,OAAO,MAAM;MACX,IAAIN,QAAQ,IAAIA,QAAQ,CAAC8B,UAAU,CAAC,OAAO,CAAC,EAAE;QAC5CJ,GAAG,CAACK,eAAe,CAAC/B,QAAQ,CAAC;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,CAACF,SAAS,CAAC,CAAC;EAEf,OAAO;IAAEE,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC;AACrC,CAAC;;AAED;AAAAL,EAAA,CAhEMF,cAAc;AA2EpB,MAAMmC,YAAyC,GAAGA,CAAC;EACjDlC,SAAS;EACTmC,GAAG;EACHC,KAAK;EACLC,SAAS;EACTC,MAAM;EACNC,YAAY;EACZC;AACF,CAAC,KAAK;EAAAC,GAAA;EACJ,MAAM;IAAEvC,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC,GAAGP,cAAc,CAACC,SAAS,CAAC;EAE9D,IAAII,OAAO,EAAE;IACX,oBACER,OAAA;MAAKwC,KAAK,EAAE;QACV,GAAGA,KAAK;QACRM,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE;MACT,CAAE;MAACT,SAAS,EAAEA,SAAU;MAAAU,QAAA,eACtBnD,OAAA;QAAKwC,KAAK,EAAE;UAAEY,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCnD,OAAA;UAAKwC,KAAK,EAAE;YAAEa,YAAY,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAAH,QAAA,EAAC;QAAC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnE1D,OAAA;UAAKwC,KAAK,EAAE;YAAEc,QAAQ,EAAE;UAAW,CAAE;UAAAH,QAAA,EAAC;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAIhD,KAAK,IAAI,CAACJ,QAAQ,EAAE;IACtB,oBACEN,OAAA;MAAKwC,KAAK,EAAE;QACV,GAAGA,KAAK;QACRM,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE,SAAS;QAChBS,MAAM,EAAE;MACV,CAAE;MAAClB,SAAS,EAAEA,SAAU;MAAAU,QAAA,eACtBnD,OAAA;QAAKwC,KAAK,EAAE;UAAEY,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCnD,OAAA;UAAKwC,KAAK,EAAE;YAAEa,YAAY,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAAH,QAAA,EAAC;QAAG;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrE1D,OAAA;UAAKwC,KAAK,EAAE;YAAEc,QAAQ,EAAE,UAAU;YAAEM,UAAU,EAAE;UAAM,CAAE;UAAAT,QAAA,EAAC;QAAiB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAC/EhD,KAAK,iBACJV,OAAA;UAAKwC,KAAK,EAAE;YAAEc,QAAQ,EAAE,SAAS;YAAEO,SAAS,EAAE,SAAS;YAAEX,KAAK,EAAE;UAAU,CAAE;UAAAC,QAAA,EACzEzC;QAAK;UAAA6C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE1D,OAAA;IACE8D,GAAG,EAAExD,QAAS;IACdiC,GAAG,EAAEA,GAAI;IACTC,KAAK,EAAEA,KAAM;IACbC,SAAS,EAAEA,SAAU;IACrBC,MAAM,EAAGqB,CAAC,IAAK;MACbhD,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjE0B,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAGqB,CAAC,CAAC;IACb,CAAE;IACFpB,YAAY,EAAEA,YAAa;IAC3BC,YAAY,EAAEA;EAAa;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5B,CAAC;AAEN,CAAC;;AAED;AAAAb,GAAA,CArEMP,YAAyC;EAAA,QASRnC,cAAc;AAAA;AAAA6D,EAAA,GAT/C1B,YAAyC;AA4E/C,MAAM2B,YAAyC,GAAGA,CAAC;EAAEC,MAAM;EAAEC,SAAS;EAAEC;AAAa,CAAC,KAAK;EACzF,IAAI,CAACF,MAAM,IAAIA,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAE/C,MAAMC,aAAa,GAAGJ,MAAM,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACxC,MAAMC,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;EAErD,MAAMM,iBAAiB,GAAGA,CAACC,KAAa,EAAEC,KAAa,KAA0B;IAC/E,MAAMC,SAA8B,GAAG;MACrCC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAEd,YAAY,GAAG,SAAS,GAAG;IACrC,CAAC;IAED,IAAIS,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QAAE,GAAGC,SAAS;QAAEK,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAQ,CAAC;IACzD,CAAC,MAAM,IAAIP,KAAK,KAAK,CAAC,EAAE;MACtB,OAAO;QAAE,GAAGC,SAAS;QAAEK,KAAK,EAAE,KAAK;QAAEC,MAAM,EAAE;MAAQ,CAAC;IACxD,CAAC,MAAM,IAAIP,KAAK,KAAK,CAAC,EAAE;MACtB,IAAID,KAAK,KAAK,CAAC,EAAE;QACf,OAAO;UAAE,GAAGE,SAAS;UAAEK,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAC;MACxD,CAAC,MAAM;QACL,OAAO;UAAE,GAAGN,SAAS;UAAEK,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAC;MACxD;IACF,CAAC,MAAM;MACL,IAAIR,KAAK,KAAK,CAAC,EAAE;QACf,OAAO;UAAE,GAAGE,SAAS;UAAEK,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAC;MACxD,CAAC,MAAM;QACL,OAAO;UAAE,GAAGN,SAAS;UAAEK,KAAK,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAQ,CAAC;MAC3D;IACF;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAACT,KAAa,EAAEC,KAAa,KAA0B;IAC3E,OAAO;MACLM,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdE,SAAS,EAAE,OAAgB;MAC3BC,UAAU,EAAE;IACd,CAAC;EACH,CAAC;EAED,oBACEvF,OAAA;IAAKwC,KAAK,EAAE;MACVM,OAAO,EAAE,MAAM;MACf0C,GAAG,EAAE,KAAK;MACVL,KAAK,EAAE,MAAM;MACb9B,YAAY,EAAE;IAChB,CAAE;IAAAF,QAAA,gBAEAnD,OAAA;MAAKwC,KAAK,EAAEmC,iBAAiB,CAAC,CAAC,EAAEL,aAAa,CAACD,MAAM,CAAE;MAAAlB,QAAA,gBACrDnD,OAAA,CAACsC,YAAY;QACXlC,SAAS,EAAEkE,aAAa,CAAC,CAAC,CAAC,CAACmB,SAAU;QACtClD,GAAG,EAAE,GAAG4B,SAAS,YAAa;QAC9B3B,KAAK,EAAE6C,aAAa,CAAC,CAAC,EAAEf,aAAa,CAACD,MAAM,CAAE;QAC9C1B,YAAY,EAAGoB,CAAC,IAAK;UACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,aAAa;QACjD,CAAE;QACF/C,YAAY,EAAGmB,CAAC,IAAK;UACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,UAAU;QAC9C;MAAE;QAAApC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACDU,YAAY,iBACXpE,OAAA;QACEwC,KAAK,EAAE;UACLuC,QAAQ,EAAE,UAAU;UACpBa,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTb,MAAM,EAAE;QACV,CAAE;QACFc,OAAO,EAAEA,CAAA,KAAM5B,YAAY,CAAC,CAAC;MAAE;QAAAb,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLY,aAAa,CAACD,MAAM,GAAG,CAAC,iBACvBrE,OAAA;MAAKwC,KAAK,EAAE;QACVM,OAAO,EAAE,MAAM;QACfmD,aAAa,EAAE3B,aAAa,CAACD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,QAAQ;QAC5DmB,GAAG,EAAE,KAAK;QACVL,KAAK,EAAEb,aAAa,CAACD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG;MAC9C,CAAE;MAAAlB,QAAA,EACCmB,aAAa,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC2B,GAAG,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;QAC1C,MAAMC,WAAW,GAAGD,GAAG,GAAG,CAAC;QAC3B,MAAME,MAAM,GAAGD,WAAW,KAAK/B,aAAa,CAACD,MAAM,GAAG,CAAC,IAAIG,cAAc,GAAG,CAAC;QAE7E,oBACExE,OAAA;UAEEwC,KAAK,EAAE;YACL,GAAGmC,iBAAiB,CAAC0B,WAAW,EAAE/B,aAAa,CAACD,MAAM,CAAC;YACvDU,QAAQ,EAAE;UACZ,CAAE;UAAA5B,QAAA,gBAEFnD,OAAA,CAACsC,YAAY;YACXlC,SAAS,EAAE+F,KAAK,CAACV,SAAU;YAC3BlD,GAAG,EAAE,GAAG4B,SAAS,YAAYkC,WAAW,GAAG,CAAC,EAAG;YAC/C7D,KAAK,EAAE6C,aAAa,CAACgB,WAAW,EAAE/B,aAAa,CAACD,MAAM,CAAE;YACxD1B,YAAY,EAAGoB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,aAAa;YACjD,CAAE;YACF/C,YAAY,EAAGmB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,UAAU;YAC9C;UAAE;YAAApC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGD4C,MAAM,IAAI9B,cAAc,GAAG,CAAC,iBAC3BxE,OAAA;YAAKwC,KAAK,EAAE;cACVuC,QAAQ,EAAE,UAAU;cACpBa,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACT9C,eAAe,EAAE,oBAAoB;cACrCH,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBE,KAAK,EAAE,OAAO;cACdI,QAAQ,EAAE,QAAQ;cAClBM,UAAU,EAAE;YACd,CAAE;YAAAT,QAAA,GAAC,GACA,EAACqB,cAAc;UAAA;YAAAjB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CACN,EAEAU,YAAY,iBACXpE,OAAA;YACEwC,KAAK,EAAE;cACLuC,QAAQ,EAAE,UAAU;cACpBa,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTb,MAAM,EAAE;YACV,CAAE;YACFc,OAAO,EAAEA,CAAA,KAAM5B,YAAY,CAACiC,WAAW;UAAE;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CACF;QAAA,GAlDI2C,WAAW;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmDb,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAA6C,GAAA,GAvJMtC,YAAyC;AAwJ/C,MAAMuC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACpC,MAAMC,QAAQ,GAAGvI,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMwI,YAAY,GAAGA,CAACzC,MAAgC,EAAE0C,YAAoB,KAAK;IAC/E,MAAMC,SAAS,GAAG3C,MAAM,CAACgC,GAAG,CAACY,GAAG,IAAIrI,WAAW,CAACqI,GAAG,CAACrB,SAAS,CAAC,CAAC,CAACsB,MAAM,CAACC,OAAO,CAAa;IAC3FC,iBAAiB,CAACJ,SAAS,CAAC;IAC5BK,uBAAuB,CAACN,YAAY,CAAC;IACrCO,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMC,oBAAoB,GAAGA,CAACP,SAAmB,EAAED,YAAoB,KAAK;IAC1EK,iBAAiB,CAACJ,SAAS,CAAC;IAC5BK,uBAAuB,CAACN,YAAY,CAAC;IACrCO,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAIC,YAAoB,IAAK;IACjD,MAAMC,MAAM,GAAG;MACb,UAAU,EAAE;QACVC,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEpI;MACR,CAAC;MACD,SAAS,EAAE;QACTmI,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEtI;MACR,CAAC;MACD,QAAQ,EAAE;QACRqI,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEnI;MACR,CAAC;MACD,WAAW,EAAE;QACXkI,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAElI;MACR,CAAC;MACD,QAAQ,EAAE;QACRiI,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEhI;MACR,CAAC;MACD,WAAW,EAAE;QACX+H,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEjI;MACR;IACF,CAAC;IAED,OAAO+H,MAAM,CAACD,YAAY,CAAwB,IAAIC,MAAM,CAAC,SAAS,CAAC;EACzE,CAAC;;EAED;EACA,MAAMG,mBAAmB,GAAIC,eAAuB,IAAK;IACvD,MAAMJ,MAAM,GAAG;MACb,kBAAkB,EAAE;QAClBC,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE7H;MACR,CAAC;MACD,cAAc,EAAE;QACd4H,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE9H;MACR,CAAC;MACD,gBAAgB,EAAE;QAChB6H,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE5H;MACR,CAAC;MACD,cAAc,EAAE;QACd2H,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEhI;MACR,CAAC;MACD,YAAY,EAAE;QACZ+H,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE3H;MACR,CAAC;MACD,SAAS,EAAE;QACT0H,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE/H;MACR;IACF,CAAC;IAED,OAAO6H,MAAM,CAACI,eAAe,CAAwB,IAAIJ,MAAM,CAAC,cAAc,CAAC;EACjF,CAAC;;EAED;EACA,MAAM,CAACK,cAAc,EAAEC,iBAAiB,CAAC,GAAG5J,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAAC6J,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG9J,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAAC+J,UAAU,EAAEC,aAAa,CAAC,GAAGhK,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAM,CAACiK,YAAY,EAAEC,eAAe,CAAC,GAAGlK,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAACmK,UAAU,EAAEC,aAAa,CAAC,GAAGpK,QAAQ,CAA4B,CAAC,CAAC,CAAC;EAC3E,MAAM,CAACqK,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGtK,QAAQ,CAAgB,IAAI,CAAC;EAC/E,MAAM,CAACuK,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGxK,QAAQ,CAAa,IAAI,CAAC;;EAE9E;EACA,MAAM,CAACyK,YAAY,EAAEvB,eAAe,CAAC,GAAGlJ,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC0K,cAAc,EAAE1B,iBAAiB,CAAC,GAAGhJ,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAAC2K,oBAAoB,EAAE1B,uBAAuB,CAAC,GAAGjJ,QAAQ,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAAC4K,aAAa,EAAEC,gBAAgB,CAAC,GAAG7K,QAAQ,CAAQ,EAAE,CAAC;EAC7D,MAAM,CAAC8K,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG/K,QAAQ,CAAQ,EAAE,CAAC;EACzE,MAAM,CAACgL,cAAc,EAAEC,iBAAiB,CAAC,GAAGjL,QAAQ,CAAkB,EAAE,CAAC;EACzE,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAqB,CAAC;EACxD,MAAM,CAACkL,eAAe,EAAEC,kBAAkB,CAAC,GAAGnL,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACoL,aAAa,EAAEC,gBAAgB,CAAC,GAAGrL,QAAQ,CAAqB,CAAC;EACxE,MAAM,CAACsL,cAAc,EAAEC,iBAAiB,CAAC,GAAGvL,QAAQ,CAAQ,EAAE,CAAC;EAC/D,MAAM,CAACwL,cAAc,EAAEC,iBAAiB,CAAC,GAAGzL,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAM;IAAE0L;EAAW,CAAC,GAAGtL,aAAa,CAAC,CAAC;;EAEtC;EACA,MAAMuL,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,IAAI;MACFnJ,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAACkJ,SAAS,CAAC;MAEnB,MAAM5I,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGxC,YAAY,wFAAwF,EAAE;QACpI0C,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;UAC/D,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,MAAMwI,IAAI,GAAG,MAAM7I,QAAQ,CAAC8I,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC7B,MAAMG,iBAAiB,GAAGH,IAAI,CAACA,IAAI,CAACjB,aAAa,IAAI,EAAE;;QAEvD;QACA;QACAC,gBAAgB,CAACmB,iBAAiB,CAAC;;QAEnC;QACA,MAAMC,MAAM,GAAGD,iBAAiB,CAAClD,MAAM,CAAEoD,GAAQ,IAAKA,GAAG,CAACC,SAAS,KAAK,CAAC,CAAC;QAC1EpB,sBAAsB,CAACkB,MAAM,CAAC;MAChC,CAAC,MAAM;QACLvJ,QAAQ,CAAC,8BAA8B,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOuB,GAAQ,EAAE;MACjBnB,OAAO,CAACL,KAAK,CAAC,+BAA+B,EAAEwB,GAAG,CAAC;MACnDvB,QAAQ,CAACuB,GAAG,CAACC,OAAO,IAAI,8BAA8B,CAAC;IACzD,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAM4J,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFjB,kBAAkB,CAAC,IAAI,CAAC;MACxBE,gBAAgB,CAACO,SAAS,CAAC;MAE3B,MAAM5I,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGxC,YAAY,0DAA0D,EAAE;QACtG0C,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;UAC/D,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,MAAMwI,IAAI,GAAG,MAAM7I,QAAQ,CAAC8I,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC7B,MAAMQ,UAAU,GAAGR,IAAI,CAACA,IAAI,CAACS,MAAM,IAAIT,IAAI,CAACA,IAAI,IAAI,EAAE;;QAEtD;QACA,MAAMU,gBAAgB,GAAG,MAAMC,OAAO,CAACC,GAAG,CACxCJ,UAAU,CAACpE,GAAG,CAAC,MAAOyE,KAAU,IAAK;UACnC,IAAI;YACF,MAAMC,aAAa,GAAG,MAAM1J,KAAK,CAAC,GAAGxC,YAAY,iBAAiBiM,KAAK,CAACE,WAAW,SAAS,EAAE;cAC5FzJ,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;gBAC/D,cAAc,EAAE;cAClB;YACF,CAAC,CAAC;YACF,MAAMwJ,SAAS,GAAG,MAAMF,aAAa,CAACb,IAAI,CAAC,CAAC;YAE5C,IAAIe,SAAS,CAACd,OAAO,IAAIc,SAAS,CAAChB,IAAI,EAAE;cACvCa,KAAK,CAACzG,MAAM,GAAG4G,SAAS,CAAChB,IAAI,CAACiB,WAAW,IAAI,EAAE;YACjD,CAAC,MAAM;cACLJ,KAAK,CAACzG,MAAM,GAAG,EAAE;YACnB;UACF,CAAC,CAAC,OAAO8G,MAAM,EAAE;YACfjK,OAAO,CAACkK,IAAI,CAAC,oCAAoCN,KAAK,CAACE,WAAW,GAAG,EAAEG,MAAM,CAAC;YAC9EL,KAAK,CAACzG,MAAM,GAAG,EAAE;UACnB;UACA,OAAOyG,KAAK;QACd,CAAC,CACH,CAAC;QAEDzB,iBAAiB,CAACsB,gBAAgB,CAAC;MACrC,CAAC,MAAM;QACLlB,gBAAgB,CAAC,gCAAgC,CAAC;MACpD;IACF,CAAC,CAAC,OAAOpH,GAAQ,EAAE;MACjBnB,OAAO,CAACL,KAAK,CAAC,iCAAiC,EAAEwB,GAAG,CAAC;MACrDoH,gBAAgB,CAACpH,GAAG,CAACC,OAAO,IAAI,gCAAgC,CAAC;IACnE,CAAC,SAAS;MACRiH,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAM8B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFxB,iBAAiB,CAAC,IAAI,CAAC;MACvB,MAAMzI,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGxC,YAAY,iEAAiE,EAAE;QAC7G0C,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;UAC/D,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,MAAMwI,IAAI,GAAG,MAAM7I,QAAQ,CAAC8I,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC7BN,iBAAiB,CAACM,IAAI,CAACA,IAAI,CAACqB,QAAQ,IAAI,EAAE,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOjJ,GAAQ,EAAE;MACjBnB,OAAO,CAACL,KAAK,CAAC,iCAAiC,EAAEwB,GAAG,CAAC;IACvD,CAAC,SAAS;MACRwH,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;;EAED;EACAxL,SAAS,CAAC,MAAM;IACd0L,2BAA2B,CAAC,CAAC;IAC7BS,mBAAmB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMe,gBAAgB,GAAG,MAAOC,YAAiB,IAAK;IACpD,IAAI;MACF,IAAIA,YAAY,CAACC,aAAa,EAAE;QAC9B,MAAMlN,mBAAmB,CAACmN,cAAc,CAACF,YAAY,CAACG,eAAe,CAAC;MACxE,CAAC,MAAM;QACL,MAAMvK,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGxC,YAAY,sBAAsB2M,YAAY,CAACG,eAAe,YAAY,EAAE;UAC1GrK,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;YAC/D,cAAc,EAAE;UAClB,CAAC;UACDmK,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBC,gBAAgB,EAAE,CAAC;YACnBC,YAAY,EAAE,KAAK,CAAC;UACtB,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,CAAC5K,QAAQ,CAACU,EAAE,EAAE;UAChB,MAAM,IAAIb,KAAK,CAAC,wBAAwB,CAAC;QAC3C;MACF;;MAEA;MACA,MAAM8I,2BAA2B,CAAC,CAAC;IACrC,CAAC,CAAC,OAAOlJ,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,MAAMoL,mBAAmB,GAAG,MAAAA,CAAOC,cAAsB,EAAEC,WAAmB,KAAK;IACjF,IAAI,CAACA,WAAW,CAACC,IAAI,CAAC,CAAC,EAAE;IAEzB,IAAI;MACF1D,oBAAoB,CAACwD,cAAc,CAAC;MAEpC,MAAM9K,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGxC,YAAY,4BAA4BqN,cAAc,WAAW,EAAE;QACjG5K,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;UAC/D,cAAc,EAAE;QAClB,CAAC;QACDmK,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBO,OAAO,EAAEF,WAAW;UACpBG,cAAc,EAAE,YAAY;UAC5BC,eAAe,EAAE,mBAAmB;UACpCP,YAAY,EAAE;QAChB,CAAC;MACH,CAAC,CAAC;MAEF,IAAI5K,QAAQ,CAACU,EAAE,EAAE;QACf0G,aAAa,CAACgE,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACN,cAAc,GAAG;QAAG,CAAC,CAAC,CAAC;QAC1D,MAAMnC,2BAA2B,CAAC,CAAC;MACrC,CAAC,MAAM;QACL7I,OAAO,CAACL,KAAK,CAAC,0BAA0B,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,SAAS;MACR6H,oBAAoB,CAAC,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAM+D,qBAAqB,GAAGzD,aAAa,CAAC9B,MAAM,CAACsE,YAAY,IAAI;IAAA,IAAAkB,qBAAA,EAAAC,qBAAA;IACjE,MAAMC,aAAa,GAAG,CAACzE,UAAU,IAC/BqD,YAAY,CAACqB,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5E,UAAU,CAAC2E,WAAW,CAAC,CAAC,CAAC,IACnEtB,YAAY,CAACa,OAAO,CAACS,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5E,UAAU,CAAC2E,WAAW,CAAC,CAAC,CAAC;IAEvE,MAAME,eAAe,GAAG,CAACjF,cAAc,IACrC,EAAA2E,qBAAA,GAAAlB,YAAY,CAACyB,WAAW,cAAAP,qBAAA,uBAAxBA,qBAAA,CAA0BQ,QAAQ,CAAC,CAAC,MAAKnF,cAAc;IAEzD,MAAMoF,iBAAiB,GAAG,CAAClF,gBAAgB,IACzC,EAAA0E,qBAAA,GAAAnB,YAAY,CAAC4B,WAAW,cAAAT,qBAAA,uBAAxBA,qBAAA,CAA0BO,QAAQ,CAAC,CAAC,MAAKjF,gBAAgB;IAE3D,OAAO2E,aAAa,IAAII,eAAe,IAAIG,iBAAiB;EAC9D,CAAC,CAAC;;EAEF;EACA,MAAME,sBAAsB,GAAGjE,cAAc,CAAClC,MAAM,CAAC4D,KAAK,IAAI;IAC5D,MAAM8B,aAAa,GAAG,CAACzE,UAAU,IAC/B2C,KAAK,CAAC+B,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5E,UAAU,CAAC2E,WAAW,CAAC,CAAC,CAAC,IAC3DhC,KAAK,CAACwC,WAAW,IAAIxC,KAAK,CAACwC,WAAW,CAACR,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC5E,UAAU,CAAC2E,WAAW,CAAC,CAAC,CAAE;;IAE3F;IACA,MAAMS,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAE5B,MAAMC,SAAS,GAAG,IAAIF,IAAI,CAAC1C,KAAK,CAAC6C,UAAU,CAAC;IAC5CD,SAAS,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhC,MAAMG,gBAAgB,GAAGF,SAAS,IAAIH,KAAK;IAC3C,MAAMM,WAAW,GAAI/C,KAAK,CAASgD,YAAY,KAAK,CAAC;IAErD,OAAOlB,aAAa,IAAIgB,gBAAgB,IAAIC,WAAW;EACzD,CAAC,CAAC;;EAEF;EACA,MAAME,oBAAoB,GAAGtB,qBAAqB;EAClD,MAAMuB,aAAa,GAAGX,sBAAsB;;EAE5C;EACA,MAAMY,eAAe,GAAG,CACtB,GAAGF,oBAAoB,CAAC1H,GAAG,CAAC6H,IAAI,KAAK;IAAE,GAAGA,IAAI;IAAEC,IAAI,EAAE,cAAc;IAAEC,QAAQ,EAAE,IAAIZ,IAAI,CAACU,IAAI,CAACG,UAAU;EAAE,CAAC,CAAC,CAAC,EAC7G,GAAGL,aAAa,CAAC3H,GAAG,CAAC6H,IAAI,KAAK;IAAE,GAAGA,IAAI;IAAEC,IAAI,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAIZ,IAAI,CAACU,IAAI,CAACP,UAAU;EAAE,CAAC,CAAC,CAAC,CAChG,CAACW,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACJ,QAAQ,CAACK,OAAO,CAAC,CAAC,GAAGF,CAAC,CAACH,QAAQ,CAACK,OAAO,CAAC,CAAC,CAAC;EAE7D,oBACEtO,OAAA;IAAKwC,KAAK,EAAE;MACV+L,SAAS,EAAE,OAAO;MAClB/G,UAAU,EAAE,mDAAmD;MAC/DzC,QAAQ,EAAE;IACZ,CAAE;IAAA5B,QAAA,gBAEAnD,OAAA;MAAKwC,KAAK,EAAE;QACVuC,QAAQ,EAAE,UAAU;QACpBa,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTyI,eAAe,EAAE;AACzB;AACA;AACA,SAAS;QACDC,aAAa,EAAE;MACjB;IAAE;MAAAlL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEL1D,OAAA;MAAKwC,KAAK,EAAE;QAAEuC,QAAQ,EAAE,UAAU;QAAE2J,MAAM,EAAE;MAAE,CAAE;MAAAvL,QAAA,gBAE9CnD,OAAA;QAAQwC,KAAK,EAAE;UACbgF,UAAU,EAAE,OAAO;UACnBmH,YAAY,EAAE,mBAAmB;UACjC5J,QAAQ,EAAE,QAAQ;UAClBa,GAAG,EAAE,CAAC;UACN8I,MAAM,EAAE,GAAG;UACXE,SAAS,EAAE;QACb,CAAE;QAAAzL,QAAA,eACAnD,OAAA;UAAKwC,KAAK,EAAE;YACVqM,OAAO,EAAE,QAAQ;YACjBzJ,MAAM,EAAE,MAAM;YACdtC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAG,QAAA,gBAEAnD,OAAA;YAAKwC,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE,QAAQ;cACbsJ,QAAQ,EAAE;YACZ,CAAE;YAAA3L,QAAA,gBACAnD,OAAA;cACE8D,GAAG,EAAC,iBAAiB;cACrBvB,GAAG,EAAC,WAAW;cACfC,KAAK,EAAE;gBACL2C,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdE,SAAS,EAAE;cACb;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF1D,OAAA;cAAAmD,QAAA,gBACEnD,OAAA;gBAAIwC,KAAK,EAAE;kBACTuM,MAAM,EAAE,CAAC;kBACTzL,QAAQ,EAAE,SAAS;kBACnBM,UAAU,EAAE,KAAK;kBACjBV,KAAK,EAAE,SAAS;kBAChB8L,UAAU,EAAE;gBACd,CAAE;gBAAA7L,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1D,OAAA;gBAAGwC,KAAK,EAAE;kBACRuM,MAAM,EAAE,CAAC;kBACTzL,QAAQ,EAAE,UAAU;kBACpBJ,KAAK,EAAE,SAAS;kBAChB8L,UAAU,EAAE;gBACd,CAAE;gBAAA7L,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1D,OAAA;YAAKwC,KAAK,EAAE;cACVyM,IAAI,EAAE,CAAC;cACPC,QAAQ,EAAE,OAAO;cACjBH,MAAM,EAAE;YACV,CAAE;YAAA5L,QAAA,eACAnD,OAAA;cAAKwC,KAAK,EAAE;gBAAEuC,QAAQ,EAAE;cAAW,CAAE;cAAA5B,QAAA,gBACnCnD,OAAA,CAACpB,MAAM;gBACLuQ,IAAI,EAAE,EAAG;gBACT3M,KAAK,EAAE;kBACLuC,QAAQ,EAAE,UAAU;kBACpBc,IAAI,EAAE,MAAM;kBACZD,GAAG,EAAE,KAAK;kBACVD,SAAS,EAAE,kBAAkB;kBAC7BzC,KAAK,EAAE;gBACT;cAAE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF1D,OAAA;gBACEgO,IAAI,EAAC,MAAM;gBACXoB,WAAW,EAAC,aAAa;gBACzBC,KAAK,EAAErH,UAAW;gBAClBsH,QAAQ,EAAGvL,CAAC,IAAKkE,aAAa,CAAClE,CAAC,CAACwL,MAAM,CAACF,KAAK,CAAE;gBAC/C7M,KAAK,EAAE;kBACL2C,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdyJ,OAAO,EAAE,eAAe;kBACxBlL,MAAM,EAAE,mBAAmB;kBAC3BsB,YAAY,EAAE,MAAM;kBACpBuC,UAAU,EAAE,SAAS;kBACrBtE,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,UAAU;kBACpBkM,OAAO,EAAE,MAAM;kBACfjK,UAAU,EAAE;gBACd,CAAE;gBACFkK,OAAO,EAAG1L,CAAC,IAAK;kBACdA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACkN,WAAW,GAAG,SAAS;kBAC7C3L,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgF,UAAU,GAAG,OAAO;kBAC1CzD,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoM,SAAS,GAAG,kCAAkC;gBACtE,CAAE;gBACFe,MAAM,EAAG5L,CAAC,IAAK;kBACbA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACkN,WAAW,GAAG,SAAS;kBAC7C3L,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgF,UAAU,GAAG,SAAS;kBAC5CzD,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoM,SAAS,GAAG,MAAM;gBAC1C;cAAE;gBAAArL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1D,OAAA;YAAKwC,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE,MAAM;cACXsJ,QAAQ,EAAE,OAAO;cACjB9L,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,gBAGAnD,OAAA;cAAKwC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE,QAAQ;gBACbqJ,OAAO,EAAE,QAAQ;gBACjBrH,UAAU,EAAE,SAAS;gBACrBvC,YAAY,EAAE,MAAM;gBACpBtB,MAAM,EAAE;cACV,CAAE;cAAAR,QAAA,gBACAnD,OAAA;gBACEqP,KAAK,EAAEzH,cAAe;gBACtB0H,QAAQ,EAAGvL,CAAC,IAAK8D,iBAAiB,CAAC9D,CAAC,CAACwL,MAAM,CAACF,KAAK,CAAE;gBACnD7M,KAAK,EAAE;kBACLqM,OAAO,EAAE,gBAAgB;kBACzBlL,MAAM,EAAE,MAAM;kBACdsB,YAAY,EAAE,KAAK;kBACnBuC,UAAU,EAAE,OAAO;kBACnBtE,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,UAAU;kBACpBkM,OAAO,EAAE,MAAM;kBACftK,MAAM,EAAE,SAAS;kBACjB4J,QAAQ,EAAE;gBACZ,CAAE;gBAAA3L,QAAA,gBAEFnD,OAAA;kBAAQqP,KAAK,EAAC,EAAE;kBAAAlM,QAAA,EAAC;gBAAc;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,EACvCiG,UAAU,CAACzD,GAAG,CAAC0J,QAAQ,iBACtB5P,OAAA;kBAAmCqP,KAAK,EAAEO,QAAQ,CAAC9C,WAAW,CAACC,QAAQ,CAAC,CAAE;kBAAA5J,QAAA,EACvEyM,QAAQ,CAACC;gBAAI,GADHD,QAAQ,CAAC9C,WAAW;kBAAAvJ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEzB,CACT,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAET1D,OAAA;gBACEqP,KAAK,EAAEvH,gBAAiB;gBACxBwH,QAAQ,EAAGvL,CAAC,IAAKgE,mBAAmB,CAAChE,CAAC,CAACwL,MAAM,CAACF,KAAK,CAAE;gBACrD7M,KAAK,EAAE;kBACLqM,OAAO,EAAE,gBAAgB;kBACzBlL,MAAM,EAAE,MAAM;kBACdsB,YAAY,EAAE,KAAK;kBACnBuC,UAAU,EAAE,OAAO;kBACnBtE,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,UAAU;kBACpBkM,OAAO,EAAE,MAAM;kBACftK,MAAM,EAAE,SAAS;kBACjB4J,QAAQ,EAAE;gBACZ,CAAE;gBAAA3L,QAAA,gBAEFnD,OAAA;kBAAQqP,KAAK,EAAC,EAAE;kBAAAlM,QAAA,EAAC;gBAAU;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC1D,OAAA;kBAAQqP,KAAK,EAAC,IAAI;kBAAAlM,QAAA,EAAC;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC1D,OAAA;kBAAQqP,KAAK,EAAC,IAAI;kBAAAlM,QAAA,EAAC;gBAAQ;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,EAER,CAACsE,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,kBAChD9H,OAAA;gBACEgG,OAAO,EAAEA,CAAA,KAAM;kBACbiC,aAAa,CAAC,EAAE,CAAC;kBACjBJ,iBAAiB,CAAC,EAAE,CAAC;kBACrBE,mBAAmB,CAAC,EAAE,CAAC;gBACzB,CAAE;gBACFvF,KAAK,EAAE;kBACLqM,OAAO,EAAE,gBAAgB;kBACzBlL,MAAM,EAAE,MAAM;kBACdsB,YAAY,EAAE,KAAK;kBACnBuC,UAAU,EAAE,SAAS;kBACrBtE,KAAK,EAAE,OAAO;kBACdI,QAAQ,EAAE,UAAU;kBACpBM,UAAU,EAAE,KAAK;kBACjBsB,MAAM,EAAE,SAAS;kBACjBK,UAAU,EAAE;gBACd,CAAE;gBACF5C,YAAY,EAAGoB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgF,UAAU,GAAG,SAAS;gBAC9C,CAAE;gBACF5E,YAAY,EAAGmB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgF,UAAU,GAAG,SAAS;gBAC9C,CAAE;gBAAArE,QAAA,EACH;cAED;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN1D,OAAA;cACEgG,OAAO,EAAEA,CAAA,KAAMU,QAAQ,CAAC,kBAAkB,CAAE;cAC5ClE,KAAK,EAAE;gBACLM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE,QAAQ;gBACbqJ,OAAO,EAAE,cAAc;gBACvBrH,UAAU,EAAE,mDAAmD;gBAC/D7D,MAAM,EAAE,MAAM;gBACdsB,YAAY,EAAE,MAAM;gBACpB/B,KAAK,EAAE,OAAO;gBACdI,QAAQ,EAAE,UAAU;gBACpBM,UAAU,EAAE,KAAK;gBACjBsB,MAAM,EAAE,SAAS;gBACjBK,UAAU,EAAE,eAAe;gBAC3BqJ,SAAS,EAAE;cACb,CAAE;cACFjM,YAAY,EAAGoB,CAAC,IAAK;gBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,kBAAkB;gBACpD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoM,SAAS,GAAG,mCAAmC;cACvE,CAAE;cACFhM,YAAY,EAAGmB,CAAC,IAAK;gBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,eAAe;gBACjD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoM,SAAS,GAAG,kCAAkC;cACtE,CAAE;cAAAzL,QAAA,gBAEFnD,OAAA,CAACZ,eAAe;gBAAC+P,IAAI,EAAE;cAAG;gBAAA5L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,aAE/B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAKT1D,OAAA;QAAKwC,KAAK,EAAE;UACVqM,OAAO,EAAE,MAAM;UACf/L,OAAO,EAAE,MAAM;UACf0C,GAAG,EAAE,MAAM;UACXzC,UAAU,EAAE;QACd,CAAE;QAAAI,QAAA,gBAEAnD,OAAA;UAAKwC,KAAK,EAAE;YACV2C,KAAK,EAAE,OAAO;YACd2K,UAAU,EAAE;UACd,CAAE;UAAA3M,QAAA,eACAnD,OAAA;YAAKwC,KAAK,EAAE;cACVgF,UAAU,EAAE,OAAO;cACnBvC,YAAY,EAAE,MAAM;cACpBtB,MAAM,EAAE,mBAAmB;cAC3BqB,QAAQ,EAAE,QAAQ;cAClBD,QAAQ,EAAE,QAAQ;cAClBa,GAAG,EAAE;YACP,CAAE;YAAAzC,QAAA,gBAEAnD,OAAA;cAAKwC,KAAK,EAAE;gBACVqM,OAAO,EAAE,oBAAoB;gBAC7BF,YAAY,EAAE;cAChB,CAAE;cAAAxL,QAAA,gBACAnD,OAAA;gBAAKwC,KAAK,EAAE;kBACVM,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpByC,GAAG,EAAE,SAAS;kBACdnC,YAAY,EAAE;gBAChB,CAAE;gBAAAF,QAAA,gBACAnD,OAAA,CAACnB,GAAG;kBAACsQ,IAAI,EAAE,EAAG;kBAAC3M,KAAK,EAAE;oBAAEU,KAAK,EAAE;kBAAU;gBAAE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9C1D,OAAA;kBAAIwC,KAAK,EAAE;oBACTuM,MAAM,EAAE,CAAC;oBACTzL,QAAQ,EAAE,UAAU;oBACpBM,UAAU,EAAE,KAAK;oBACjBV,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,EAAC;gBAEH;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACN1D,OAAA;gBAAGwC,KAAK,EAAE;kBACRuM,MAAM,EAAE,CAAC;kBACTzL,QAAQ,EAAE,UAAU;kBACpBJ,KAAK,EAAE;gBACT,CAAE;gBAAAC,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGN1D,OAAA;cAAKwC,KAAK,EAAE;gBAAEqM,OAAO,EAAE;cAAO,CAAE;cAAA1L,QAAA,EAC7B4F,mBAAmB,CAAC1E,MAAM,GAAG,CAAC,gBAC7BrE,OAAA,CAAAE,SAAA;gBAAAiD,QAAA,GACG4F,mBAAmB,CAACxE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC2B,GAAG,CAAC,CAACmF,YAAY,EAAEzG,KAAK,KAAK;kBAC5D;kBACA,MAAMmL,OAAO,GAAG1E,YAAY,CAAC2E,QAAQ;kBACrC,MAAM1I,YAAY,GAAG,CAAC+D,YAAY,CAAC4E,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;kBAC5E,MAAMC,aAAa,GAAG9I,gBAAgB,CAACC,YAAY,CAAC;kBAEpD,oBACEtH,OAAA;oBAEEwC,KAAK,EAAE;sBACLqM,OAAO,EAAE,MAAM;sBACfrH,UAAU,EAAEuI,OAAO,GAAG,SAAS,GAAG,SAAS;sBAC3C9K,YAAY,EAAE,MAAM;sBACpBtB,MAAM,EAAEoM,OAAO,GAAG,mBAAmB,GAAG,mBAAmB;sBAC3D1M,YAAY,EAAE,MAAM;sBACpB6B,MAAM,EAAE,SAAS;sBACjBK,UAAU,EAAE;oBACd,CAAE;oBACF5C,YAAY,EAAGoB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgF,UAAU,GAAGuI,OAAO,GAAG,SAAS,GAAG,SAAS;sBAClEhM,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACkN,WAAW,GAAGK,OAAO,GAAG,SAAS,GAAG,SAAS;oBACrE,CAAE;oBACFnN,YAAY,EAAGmB,CAAC,IAAK;sBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgF,UAAU,GAAGuI,OAAO,GAAG,SAAS,GAAG,SAAS;sBAClEhM,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACkN,WAAW,GAAGK,OAAO,GAAG,SAAS,GAAG,SAAS;oBACrE,CAAE;oBACF/J,OAAO,EAAEA,CAAA,KAAMyC,qBAAqB,CAAC4C,YAAY,CAAE;oBAAAlI,QAAA,eAEnDnD,OAAA;sBAAKwC,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,YAAY;wBACxByC,GAAG,EAAE;sBACP,CAAE;sBAAArC,QAAA,gBACAnD,OAAA;wBAAKwC,KAAK,EAAE;0BACV2C,KAAK,EAAE,KAAK;0BACZC,MAAM,EAAE,KAAK;0BACboC,UAAU,EAAEuI,OAAO,GAAG,SAAS,GAAII,aAAa,CAAC3I,UAAU,CAACoF,QAAQ,CAAC,SAAS,CAAC,GAAG,SAAS,GAAG,SAAU;0BACxG3H,YAAY,EAAE,KAAK;0BACnBpB,SAAS,EAAE,QAAQ;0BACnBiM,UAAU,EAAE;wBACd;sBAAE;wBAAAvM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACL1D,OAAA;wBAAKwC,KAAK,EAAE;0BAAEyM,IAAI,EAAE;wBAAE,CAAE;wBAAA9L,QAAA,gBACtBnD,OAAA;0BAAIwC,KAAK,EAAE;4BACTuM,MAAM,EAAE,cAAc;4BACtBzL,QAAQ,EAAE,UAAU;4BACpBM,UAAU,EAAE,KAAK;4BACjBV,KAAK,EAAE,SAAS;4BAChB8L,UAAU,EAAE;0BACd,CAAE;0BAAA7L,QAAA,EACCkI,YAAY,CAACqB;wBAAK;0BAAAnJ,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACjB,CAAC,eACL1D,OAAA;0BAAGwC,KAAK,EAAE;4BACRuM,MAAM,EAAE,cAAc;4BACtBzL,QAAQ,EAAE,QAAQ;4BAClBJ,KAAK,EAAE,SAAS;4BAChB8L,UAAU,EAAE;0BACd,CAAE;0BAAA7L,QAAA,EACCkI,YAAY,CAACa,OAAO,CAAC7H,MAAM,GAAG,EAAE,GAC7B,GAAGgH,YAAY,CAACa,OAAO,CAACkE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAC7C/E,YAAY,CAACa;wBAAO;0BAAA3I,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACvB,CAAC,eACJ1D,OAAA;0BAAKwC,KAAK,EAAE;4BACVM,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpByC,GAAG,EAAE,QAAQ;4BACblC,QAAQ,EAAE,SAAS;4BACnBJ,KAAK,EAAE;0BACT,CAAE;0BAAAC,QAAA,gBACAnD,OAAA,CAAClB,QAAQ;4BAACqQ,IAAI,EAAE;0BAAG;4BAAA5L,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACtB1D,OAAA;4BAAAmD,QAAA,EAAO,IAAIkK,IAAI,CAAChC,YAAY,CAAC6C,UAAU,CAAC,CAACmC,kBAAkB,CAAC;0BAAC;4BAAA9M,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GAhED2H,YAAY,CAACG,eAAe;oBAAAjI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAiE9B,CAAC;gBAEV,CAAC,CAAC,EAEDqF,mBAAmB,CAAC1E,MAAM,GAAG,CAAC,iBAC7BrE,OAAA;kBAAQwC,KAAK,EAAE;oBACb2C,KAAK,EAAE,MAAM;oBACb0J,OAAO,EAAE,SAAS;oBAClBlL,MAAM,EAAE,mBAAmB;oBAC3BsB,YAAY,EAAE,KAAK;oBACnBuC,UAAU,EAAE,OAAO;oBACnBtE,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,UAAU;oBACpBM,UAAU,EAAE,KAAK;oBACjBsB,MAAM,EAAE,SAAS;oBACjBK,UAAU,EAAE;kBACd,CAAE;kBACF5C,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgF,UAAU,GAAG,SAAS;oBAC5CzD,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACkN,WAAW,GAAG,SAAS;kBAC/C,CAAE;kBACF9M,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgF,UAAU,GAAG,OAAO;oBAC1CzD,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACkN,WAAW,GAAG,SAAS;kBAC/C,CAAE;kBAAAvM,QAAA,GAAC,WACQ,EAAC4F,mBAAmB,CAAC1E,MAAM,EAAC,eACvC;gBAAA;kBAAAd,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA,eACD,CAAC,gBAEH1D,OAAA;gBAAKwC,KAAK,EAAE;kBACVqM,OAAO,EAAE,WAAW;kBACpBzL,SAAS,EAAE,QAAQ;kBACnBF,KAAK,EAAE;gBACT,CAAE;gBAAAC,QAAA,gBACAnD,OAAA,CAACnB,GAAG;kBAACsQ,IAAI,EAAE,EAAG;kBAAC3M,KAAK,EAAE;oBAAEa,YAAY,EAAE,QAAQ;oBAAEiN,OAAO,EAAE;kBAAI;gBAAE;kBAAA/M,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAClE1D,OAAA;kBAAGwC,KAAK,EAAE;oBAAEuM,MAAM,EAAE,CAAC;oBAAEzL,QAAQ,EAAE;kBAAW,CAAE;kBAAAH,QAAA,EAAC;gBAE/C;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1D,OAAA;UAAKwC,KAAK,EAAE;YAAEyM,IAAI,EAAE,CAAC;YAAEH,QAAQ,EAAE;UAAE,CAAE;UAAA3L,QAAA,GAEpC,CAAC3C,OAAO,IAAI2I,eAAe,kBAC1BnJ,OAAA;YAAKwC,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfE,cAAc,EAAE,QAAQ;cACxBD,UAAU,EAAE,QAAQ;cACpBwL,SAAS,EAAE;YACb,CAAE;YAAApL,QAAA,eACAnD,OAAA;cAAKwC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfmD,aAAa,EAAE,QAAQ;gBACvBlD,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,gBACAnD,OAAA;gBAAKwC,KAAK,EAAE;kBACV2C,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdzB,MAAM,EAAE,kCAAkC;kBAC1C4M,SAAS,EAAE,mBAAmB;kBAC9BtL,YAAY,EAAE,KAAK;kBACnBuL,SAAS,EAAE;gBACb;cAAE;gBAAAjN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACT1D,OAAA;gBAAGwC,KAAK,EAAE;kBACRU,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,MAAM;kBAChBM,UAAU,EAAE;gBACd,CAAE;gBAAAT,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA,CAAChD,KAAK,IAAI2I,aAAa,KAAK,CAAC7I,OAAO,IAAI,CAAC2I,eAAe,iBACvDnJ,OAAA;YAAKwC,KAAK,EAAE;cACVqM,OAAO,EAAE,MAAM;cACfrH,UAAU,EAAE,wBAAwB;cACpC7D,MAAM,EAAE,kCAAkC;cAC1CsB,YAAY,EAAE,MAAM;cACpB7B,SAAS,EAAE;YACb,CAAE;YAAAD,QAAA,gBACAnD,OAAA;cAAKwC,KAAK,EAAE;gBACV2C,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdoC,UAAU,EAAE,wBAAwB;gBACpCvC,YAAY,EAAE,KAAK;gBACnBnC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB+L,MAAM,EAAE;cACV,CAAE;cAAA5L,QAAA,eACAnD,OAAA,CAACjB,aAAa;gBAACoQ,IAAI,EAAE,EAAG;gBAACjM,KAAK,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACN1D,OAAA;cAAIwC,KAAK,EAAE;gBACTU,KAAK,EAAE,SAAS;gBAChB6L,MAAM,EAAE,cAAc;gBACtBzL,QAAQ,EAAE,SAAS;gBACnBM,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAAC;YAEH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1D,OAAA;cAAGwC,KAAK,EAAE;gBACRU,KAAK,EAAE,SAAS;gBAChB6L,MAAM,EAAE,cAAc;gBACtBzL,QAAQ,EAAE;cACZ,CAAE;cAAAH,QAAA,EACCzC,KAAK,IAAI2I;YAAa;cAAA9F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACJ1D,OAAA;cACEgG,OAAO,EAAEA,CAAA,KAAM;gBACb4D,2BAA2B,CAAC,CAAC;gBAC7BS,mBAAmB,CAAC,CAAC;cACvB,CAAE;cACF7H,KAAK,EAAE;gBACLgF,UAAU,EAAE,mDAAmD;gBAC/DtE,KAAK,EAAE,OAAO;gBACdS,MAAM,EAAE,MAAM;gBACdsB,YAAY,EAAE,MAAM;gBACpB4J,OAAO,EAAE,gBAAgB;gBACzBvL,QAAQ,EAAE,UAAU;gBACpBM,UAAU,EAAE,KAAK;gBACjBsB,MAAM,EAAE,SAAS;gBACjBK,UAAU,EAAE;cACd,CAAE;cACF5C,YAAY,EAAGoB,CAAC,IAAK;gBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,kBAAkB;gBACpD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoM,SAAS,GAAG,mCAAmC;cACvE,CAAE;cACFhM,YAAY,EAAGmB,CAAC,IAAK;gBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,eAAe;gBACjD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoM,SAAS,GAAG,MAAM;cAC1C,CAAE;cAAAzL,QAAA,EACH;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAGA,CAAClD,OAAO,IAAI,CAAC2I,eAAe,IAAI,CAACzI,KAAK,IAAI,CAAC2I,aAAa,IACxDuE,oBAAoB,CAACvJ,MAAM,KAAK,CAAC,IAAIwJ,aAAa,CAACxJ,MAAM,KAAK,CAAC,iBAC9DrE,OAAA;YAAKwC,KAAK,EAAE;cACVqM,OAAO,EAAE,WAAW;cACpBzL,SAAS,EAAE;YACb,CAAE;YAAAD,QAAA,gBACAnD,OAAA;cAAKwC,KAAK,EAAE;gBACV2C,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdoC,UAAU,EAAE,mDAAmD;gBAC/DvC,YAAY,EAAE,KAAK;gBACnBnC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxB+L,MAAM,EAAE;cACV,CAAE;cAAA5L,QAAA,eACAnD,OAAA,CAACrB,SAAS;gBAACwQ,IAAI,EAAE,EAAG;gBAACjM,KAAK,EAAC;cAAO;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACN1D,OAAA;cAAIwC,KAAK,EAAE;gBACTU,KAAK,EAAE,SAAS;gBAChB6L,MAAM,EAAE,YAAY;gBACpBzL,QAAQ,EAAE,QAAQ;gBAClBM,UAAU,EAAE;cACd,CAAE;cAAAT,QAAA,EAAC;YAEH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1D,OAAA;cAAGwC,KAAK,EAAE;gBACRU,KAAK,EAAE,SAAS;gBAChB6L,MAAM,EAAE,YAAY;gBACpBzL,QAAQ,EAAE,MAAM;gBAChB0L,UAAU,EAAE,KAAK;gBACjBE,QAAQ,EAAE,OAAO;gBACjBuB,UAAU,EAAE,MAAM;gBAClBC,WAAW,EAAE;cACf,CAAE;cAAAvN,QAAA,EACC6E,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,GAC7C,8EAA8E,GAC9E;YAA6F;cAAAvE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhG,CAAC,EACH,CAACsE,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,kBAChD9H,OAAA;cACEgG,OAAO,EAAEA,CAAA,KAAM;gBACbiC,aAAa,CAAC,EAAE,CAAC;gBACjBJ,iBAAiB,CAAC,EAAE,CAAC;gBACrBE,mBAAmB,CAAC,EAAE,CAAC;cACzB,CAAE;cACFvF,KAAK,EAAE;gBACLgF,UAAU,EAAE,mDAAmD;gBAC/DtE,KAAK,EAAE,OAAO;gBACdS,MAAM,EAAE,MAAM;gBACdsB,YAAY,EAAE,MAAM;gBACpB4J,OAAO,EAAE,gBAAgB;gBACzBvL,QAAQ,EAAE,UAAU;gBACpBM,UAAU,EAAE,KAAK;gBACjBsB,MAAM,EAAE,SAAS;gBACjBK,UAAU,EAAE;cACd,CAAE;cACF5C,YAAY,EAAGoB,CAAC,IAAK;gBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,kBAAkB;gBACpD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoM,SAAS,GAAG,mCAAmC;cACvE,CAAE;cACFhM,YAAY,EAAGmB,CAAC,IAAK;gBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,eAAe;gBACjD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoM,SAAS,GAAG,MAAM;cAC1C,CAAE;cAAAzL,QAAA,EACH;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAGA,CAAC+F,cAAc,IAAIF,cAAc,CAAClF,MAAM,GAAG,CAAC,iBAC3CrE,OAAA;YAAKwC,KAAK,EAAE;cACVgF,UAAU,EAAE,2BAA2B;cACvCvC,YAAY,EAAE,MAAM;cACpB4J,OAAO,EAAE,QAAQ;cACjBlL,MAAM,EAAE,8BAA8B;cACtCgN,cAAc,EAAE,YAAY;cAC5B/B,SAAS,EAAE,gCAAgC;cAC3CvL,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,gBACAnD,OAAA;cAAIwC,KAAK,EAAE;gBACTc,QAAQ,EAAE,SAAS;gBACnBM,UAAU,EAAE,KAAK;gBACjBV,KAAK,EAAE,SAAS;gBAChB6L,MAAM,EAAE,YAAY;gBACpBjM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,EAAC;YAEH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1D,OAAA;cAAKwC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACf0C,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,EACCoG,cAAc,CAAChF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC2B,GAAG,CAAE0K,OAAY;gBAAA,IAAAC,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA;gBAAA,oBAC3C/Q,OAAA;kBAEEwC,KAAK,EAAE;oBACLM,OAAO,EAAE,MAAM;oBACfE,cAAc,EAAE,eAAe;oBAC/BD,UAAU,EAAE,QAAQ;oBACpB8L,OAAO,EAAE,SAAS;oBAClB5L,eAAe,EAAE,SAAS;oBAC1BgC,YAAY,EAAE,KAAK;oBACnBtB,MAAM,EAAE;kBACV,CAAE;kBAAAR,QAAA,gBAEFnD,OAAA;oBAAAmD,QAAA,gBACEnD,OAAA;sBAAKwC,KAAK,EAAE;wBACVoB,UAAU,EAAE,KAAK;wBACjBV,KAAK,EAAE,SAAS;wBAChBI,QAAQ,EAAE;sBACZ,CAAE;sBAAAH,QAAA,IAAA0N,gBAAA,GACCD,OAAO,CAACI,OAAO,cAAAH,gBAAA,uBAAfA,gBAAA,CAAiBI,UAAU,EAAC,GAAC,GAAAH,iBAAA,GAACF,OAAO,CAACI,OAAO,cAAAF,iBAAA,uBAAfA,iBAAA,CAAiBI,SAAS;oBAAA;sBAAA3N,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC,eACN1D,OAAA;sBAAKwC,KAAK,EAAE;wBACVc,QAAQ,EAAE,SAAS;wBACnBJ,KAAK,EAAE;sBACT,CAAE;sBAAAC,QAAA,GAAC,QACK,GAAA4N,iBAAA,GAACH,OAAO,CAACI,OAAO,cAAAD,iBAAA,uBAAfA,iBAAA,CAAiB9D,WAAW,EAAC,UAAG,EAAC2D,OAAO,CAACO,cAAc;oBAAA;sBAAA5N,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN1D,OAAA;oBAAKwC,KAAK,EAAE;sBACVc,QAAQ,EAAE,SAAS;sBACnBJ,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,EACC,IAAIkK,IAAI,CAACuD,OAAO,CAAC1C,UAAU,CAAC,CAACmC,kBAAkB,CAAC;kBAAC;oBAAA9M,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC;gBAAA,GA/BDkN,OAAO,CAACQ,UAAU;kBAAA7N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgCpB,CAAC;cAAA,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA,CAAClD,OAAO,IAAI,CAAC2I,eAAe,KAAKyE,oBAAoB,CAACvJ,MAAM,GAAG,CAAC,IAAIwJ,aAAa,CAACxJ,MAAM,GAAG,CAAC,CAAC,iBAC5FrE,OAAA;YAAKwC,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfmD,aAAa,EAAE,QAAQ;cACvBT,GAAG,EAAE;YACP,CAAE;YAAArC,QAAA,GAEC0K,aAAa,CAACxJ,MAAM,GAAG,CAAC,iBACvBrE,OAAA,CAAAE,SAAA;cAAAiD,QAAA,EACG0K,aAAa,CAAC3H,GAAG,CAACyE,KAAK,iBACtB3K,OAAA;gBAEEwC,KAAK,EAAE;kBACLgF,UAAU,EAAE,2BAA2B;kBACvCvC,YAAY,EAAE,MAAM;kBACpB4J,OAAO,EAAE,QAAQ;kBACjBlL,MAAM,EAAE,8BAA8B;kBACtCgN,cAAc,EAAE,YAAY;kBAC5B/B,SAAS,EAAE,gCAAgC;kBAC3CrJ,UAAU,EAAE;gBACd,CAAE;gBACF5C,YAAY,EAAGoB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,kBAAkB;kBACpD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoM,SAAS,GAAG,gCAAgC;gBACpE,CAAE;gBACFhM,YAAY,EAAGmB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,eAAe;kBACjD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoM,SAAS,GAAG,gCAAgC;gBACpE,CAAE;gBAAAzL,QAAA,gBAGFnD,OAAA;kBAAKwC,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,YAAY;oBACxByC,GAAG,EAAE,MAAM;oBACXnC,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,gBACAnD,OAAA;oBAAKwC,KAAK,EAAE;sBACV2C,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdoC,UAAU,EAAE,mDAAmD;sBAC/DvC,YAAY,EAAE,MAAM;sBACpBnC,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE,QAAQ;sBACxB8M,UAAU,EAAE;oBACd,CAAE;oBAAA3M,QAAA,eACAnD,OAAA,CAAClB,QAAQ;sBAACqQ,IAAI,EAAE,EAAG;sBAACjM,KAAK,EAAC;oBAAO;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,eAEN1D,OAAA;oBAAKwC,KAAK,EAAE;sBAAEyM,IAAI,EAAE;oBAAE,CAAE;oBAAA9L,QAAA,gBACtBnD,OAAA;sBAAKwC,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdnC,YAAY,EAAE;sBAChB,CAAE;sBAAAF,QAAA,GACC,CAAC,MAAM;wBACN,MAAMwE,eAAe,GAAGgD,KAAK,CAAC0G,iBAAiB,IAAI,cAAc;wBACjE,MAAMC,YAAY,GAAG5J,mBAAmB,CAACC,eAAe,CAAC;wBACzD,MAAM4J,aAAa,GAAGD,YAAY,CAAC7J,IAAI;wBAEvC,oBACEzH,OAAA;0BAAMwC,KAAK,EAAE;4BACXgF,UAAU,EAAE8J,YAAY,CAAC9J,UAAU;4BACnCtE,KAAK,EAAE,OAAO;4BACdI,QAAQ,EAAE,SAAS;4BACnBM,UAAU,EAAE,KAAK;4BACjBiL,OAAO,EAAE,iBAAiB;4BAC1B5J,YAAY,EAAE,MAAM;4BACpBuM,aAAa,EAAE,WAAW;4BAC1BC,aAAa,EAAE,OAAO;4BACtB3O,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpByC,GAAG,EAAE;0BACP,CAAE;0BAAArC,QAAA,gBACAnD,OAAA,CAACuR,aAAa;4BAACpC,IAAI,EAAE,EAAG;4BAACjM,KAAK,EAAC;0BAAO;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,EACxCiE,eAAe;wBAAA;0BAAApE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC;sBAEX,CAAC,EAAE,CAAC,eAEJ1D,OAAA;wBAAKwC,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpByC,GAAG,EAAE,SAAS;0BACdtC,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE;wBACZ,CAAE;wBAAAH,QAAA,gBACAnD,OAAA,CAAClB,QAAQ;0BAACqQ,IAAI,EAAE;wBAAG;0BAAA5L,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACrB,IAAI2J,IAAI,CAAC1C,KAAK,CAAC6C,UAAU,CAAC,CAAC6C,kBAAkB,CAAC,OAAO,EAAE;0BACtDqB,OAAO,EAAE,MAAM;0BACfC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,MAAM;0BACbC,GAAG,EAAE;wBACP,CAAC,CAAC;sBAAA;wBAAAtO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN1D,OAAA;sBAAIwC,KAAK,EAAE;wBACTuM,MAAM,EAAE,cAAc;wBACtBzL,QAAQ,EAAE,SAAS;wBACnBM,UAAU,EAAE,KAAK;wBACjBV,KAAK,EAAE,SAAS;wBAChB8L,UAAU,EAAE;sBACd,CAAE;sBAAA7L,QAAA,EACCwH,KAAK,CAAC+B;oBAAK;sBAAAnJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAGN1D,OAAA;oBAAKwC,KAAK,EAAE;sBAAEM,OAAO,EAAE,MAAM;sBAAE0C,GAAG,EAAE;oBAAS,CAAE;oBAAArC,QAAA,eAC7CnD,OAAA;sBACEgG,OAAO,EAAEA,CAAA,KAAMU,QAAQ,CAAC,yBAAyBiE,KAAK,CAACE,WAAW,EAAE,CAAE;sBACtErI,KAAK,EAAE;wBACLM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdqJ,OAAO,EAAE,gBAAgB;wBACzBrH,UAAU,EAAE,yBAAyB;wBACrC7D,MAAM,EAAE,mCAAmC;wBAC3CsB,YAAY,EAAE,KAAK;wBACnB/B,KAAK,EAAE,SAAS;wBAChBI,QAAQ,EAAE,SAAS;wBACnBM,UAAU,EAAE,KAAK;wBACjBsB,MAAM,EAAE,SAAS;wBACjBK,UAAU,EAAE;sBACd,CAAE;sBACF5C,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgF,UAAU,GAAG,yBAAyB;sBAC9D,CAAE;sBACF5E,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgF,UAAU,GAAG,yBAAyB;sBAC9D,CAAE;sBAAArE,QAAA,gBAEFnD,OAAA,CAACd,IAAI;wBAACiQ,IAAI,EAAE;sBAAG;wBAAA5L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,QAEpB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGLiH,KAAK,CAACwC,WAAW,iBAChBnN,OAAA;kBAAKwC,KAAK,EAAE;oBACVU,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,SAAS;oBACnB0L,UAAU,EAAE,KAAK;oBACjB3L,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,EACCwH,KAAK,CAACwC;gBAAW;kBAAA5J,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CACN,EAGA,CAAC,MAAM;kBACN;kBACA,MAAMoO,cAAwB,GAAG,EAAE;kBAEnC,IAAKnH,KAAK,CAASzG,MAAM,IAAKyG,KAAK,CAASzG,MAAM,CAACG,MAAM,GAAG,CAAC,EAAE;oBAC5DsG,KAAK,CAASzG,MAAM,CAAC6N,OAAO,CAAEjL,GAAQ,IAAK;sBAC1C,IAAIA,GAAG,CAACrB,SAAS,EAAE;wBACjB;wBACA,MAAMnF,QAAQ,GAAG7B,WAAW,CAACqI,GAAG,CAACrB,SAAS,CAAC;wBAC3C,IAAInF,QAAQ,EAAE;0BACZwR,cAAc,CAACE,IAAI,CAAC1R,QAAQ,CAAC;wBAC/B;sBACF;oBACF,CAAC,CAAC;kBACJ;kBAEA,OAAOwR,cAAc,CAACzN,MAAM,GAAG,CAAC,gBAC9BrE,OAAA;oBAAKwC,KAAK,EAAE;sBAAEa,YAAY,EAAE;oBAAO,CAAE;oBAAAF,QAAA,eACnCnD,OAAA,CAACzB,oBAAoB;sBACnB2F,MAAM,EAAE4N,cAAc,CAAC/K,MAAM,CAACC,OAAO,CAAc;sBACnD7C,SAAS,EAAEwG,KAAK,CAAC+B,KAAM;sBACvBuF,UAAU,EAAE,CAAE;sBACd7N,YAAY,EAAGQ,KAAK,IAAK;wBACvB,MAAMsN,cAAc,GAAGJ,cAAc,CAAC/K,MAAM,CAACC,OAAO,CAAa;wBACjEI,oBAAoB,CAAC8K,cAAc,EAAEtN,KAAK,CAAC;sBAC7C;oBAAE;sBAAArB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,GACJ,IAAI;gBACV,CAAC,EAAE,CAAC,eAGJ1D,OAAA;kBAAKwC,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE,QAAQ;oBACbqJ,OAAO,EAAE,MAAM;oBACfrH,UAAU,EAAE,0BAA0B;oBACtCvC,YAAY,EAAE,MAAM;oBACpB3B,QAAQ,EAAE;kBACZ,CAAE;kBAAAH,QAAA,gBACAnD,OAAA;oBAAKwC,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,QAAQ;sBACbtC,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,gBACAnD,OAAA,CAAClB,QAAQ;sBAACqQ,IAAI,EAAE;oBAAG;sBAAA5L,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACtB1D,OAAA;sBAAAmD,QAAA,EACGwH,KAAK,CAACwH,QAAQ,IAAIxH,KAAK,CAACwH,QAAQ,KAAKxH,KAAK,CAAC6C,UAAU,GAClD,GAAG,IAAIH,IAAI,CAAC1C,KAAK,CAAC6C,UAAU,CAAC,CAAC6C,kBAAkB,CAAC,CAAC,MAAM,IAAIhD,IAAI,CAAC1C,KAAK,CAACwH,QAAQ,CAAC,CAAC9B,kBAAkB,CAAC,CAAC,EAAE,GACvG,IAAIhD,IAAI,CAAC1C,KAAK,CAAC6C,UAAU,CAAC,CAAC6C,kBAAkB,CAAC;oBAAC;sBAAA9M,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAE/C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,EAELiH,KAAK,CAAC0G,iBAAiB,iBACtBrR,OAAA;oBAAKwC,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,QAAQ;sBACbtC,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,eACAnD,OAAA;sBAAMwC,KAAK,EAAE;wBACXqM,OAAO,EAAE,gBAAgB;wBACzBrH,UAAU,EAAE,yBAAyB;wBACrCvC,YAAY,EAAE,KAAK;wBACnB3B,QAAQ,EAAE,SAAS;wBACnBM,UAAU,EAAE;sBACd,CAAE;sBAAAT,QAAA,EACCwH,KAAK,CAAC0G;oBAAiB;sBAAA9N,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GAzND,SAASiH,KAAK,CAACE,WAAW,EAAE;gBAAAtH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OA0N9B,CACN;YAAC,gBACF,CACH,EAGAkK,oBAAoB,CAACvJ,MAAM,GAAG,CAAC,iBAC9BrE,OAAA,CAAAE,SAAA;cAAAiD,QAAA,EACGyK,oBAAoB,CAAC1H,GAAG,CAACmF,YAAY,iBACpCrL,OAAA;gBAEEwC,KAAK,EAAE;kBACLgF,UAAU,EAAE,2BAA2B;kBACvCvC,YAAY,EAAE,MAAM;kBACpB4J,OAAO,EAAE,QAAQ;kBACjBlL,MAAM,EAAE0H,YAAY,CAACjB,SAAS,GAC1B,mCAAmC,GACnC,8BAA8B;kBAClCuG,cAAc,EAAE,YAAY;kBAC5B/B,SAAS,EAAEvD,YAAY,CAACjB,SAAS,GAC7B,qCAAqC,GACrC,gCAAgC;kBACpC7E,UAAU,EAAE,2CAA2C;kBACvDR,QAAQ,EAAE;gBACZ,CAAE;gBACFpC,YAAY,EAAGoB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,kBAAkB;kBACpD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoM,SAAS,GAAGvD,YAAY,CAACjB,SAAS,GACpD,qCAAqC,GACrC,gCAAgC;gBACtC,CAAE;gBACFxH,YAAY,EAAGmB,CAAC,IAAK;kBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACmD,SAAS,GAAG,eAAe;kBACjD5B,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACoM,SAAS,GAAGvD,YAAY,CAACjB,SAAS,GACpD,qCAAqC,GACrC,gCAAgC;gBACtC,CAAE;gBAAAjH,QAAA,GAGDkI,YAAY,CAACjB,SAAS,iBACrBpK,OAAA;kBAAKwC,KAAK,EAAE;oBACVuC,QAAQ,EAAE,UAAU;oBACpBa,GAAG,EAAE,MAAM;oBACXE,KAAK,EAAE,MAAM;oBACb0B,UAAU,EAAE,mDAAmD;oBAC/DtE,KAAK,EAAE,OAAO;oBACd2L,OAAO,EAAE,iBAAiB;oBAC1B5J,YAAY,EAAE,MAAM;oBACpB3B,QAAQ,EAAE,SAAS;oBACnBM,UAAU,EAAE,KAAK;oBACjBd,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpByC,GAAG,EAAE,SAAS;oBACdoJ,SAAS,EAAE;kBACb,CAAE;kBAAAzL,QAAA,gBACAnD,OAAA,CAACnB,GAAG;oBAACsQ,IAAI,EAAE;kBAAG;oBAAA5L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAEnB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN,eAGD1D,OAAA;kBAAKwC,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,YAAY;oBACxByC,GAAG,EAAE,MAAM;oBACXnC,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,GACC,CAAC,MAAM;oBACN,IAAIkI,YAAY,CAAC2E,QAAQ,EAAE;sBACzB,oBACEhQ,OAAA;wBAAKwC,KAAK,EAAE;0BACV2C,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdoC,UAAU,EAAE,mDAAmD;0BAC/DvC,YAAY,EAAE,MAAM;0BACpBnC,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpBC,cAAc,EAAE,QAAQ;0BACxB8M,UAAU,EAAE;wBACd,CAAE;wBAAA3M,QAAA,eACAnD,OAAA,CAACT,aAAa;0BAAC4P,IAAI,EAAE,EAAG;0BAACjM,KAAK,EAAC;wBAAO;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAEV,CAAC,MAAM;sBACL,MAAM4D,YAAY,GAAG,CAAC+D,YAAY,CAAC4E,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;sBAC5E,MAAMC,aAAa,GAAG9I,gBAAgB,CAACC,YAAY,CAAC;sBACpD,MAAMiK,aAAa,GAAGpB,aAAa,CAAC1I,IAAI;sBAExC,oBACEzH,OAAA;wBAAKwC,KAAK,EAAE;0BACV2C,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdoC,UAAU,EAAE2I,aAAa,CAAC3I,UAAU;0BACpCvC,YAAY,EAAE,MAAM;0BACpBnC,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpBC,cAAc,EAAE,QAAQ;0BACxB8M,UAAU,EAAE;wBACd,CAAE;wBAAA3M,QAAA,eACAnD,OAAA,CAACuR,aAAa;0BAACpC,IAAI,EAAE,EAAG;0BAACjM,KAAK,EAAC;wBAAO;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAEV;kBACF,CAAC,EAAE,CAAC,eAEJ1D,OAAA;oBAAKwC,KAAK,EAAE;sBAAEyM,IAAI,EAAE;oBAAE,CAAE;oBAAA9L,QAAA,gBACtBnD,OAAA;sBAAKwC,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdnC,YAAY,EAAE,QAAQ;wBACtB+O,QAAQ,EAAE;sBACZ,CAAE;sBAAAjP,QAAA,GACC,CAAC,MAAM;wBACN,IAAIkI,YAAY,CAAC2E,QAAQ,EAAE;0BACzB,oBACEhQ,OAAA;4BAAMwC,KAAK,EAAE;8BACXgF,UAAU,EAAE,mDAAmD;8BAC/DtE,KAAK,EAAE,OAAO;8BACdI,QAAQ,EAAE,SAAS;8BACnBM,UAAU,EAAE,KAAK;8BACjBiL,OAAO,EAAE,iBAAiB;8BAC1B5J,YAAY,EAAE,MAAM;8BACpBuM,aAAa,EAAE,WAAW;8BAC1BC,aAAa,EAAE,OAAO;8BACtB3O,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpByC,GAAG,EAAE;4BACP,CAAE;4BAAArC,QAAA,gBACAnD,OAAA,CAACT,aAAa;8BAAC4P,IAAI,EAAE,EAAG;8BAACjM,KAAK,EAAC;4BAAO;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,SAE3C;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAEX,CAAC,MAAM;0BACL,MAAM4D,YAAY,GAAG,CAAC+D,YAAY,CAAC4E,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;0BAC5E,MAAMC,aAAa,GAAG9I,gBAAgB,CAACC,YAAY,CAAC;0BACpD,MAAMiK,aAAa,GAAGpB,aAAa,CAAC1I,IAAI;0BAExC,oBACEzH,OAAA;4BAAMwC,KAAK,EAAE;8BACXgF,UAAU,EAAE2I,aAAa,CAAC3I,UAAU;8BACpCtE,KAAK,EAAE,OAAO;8BACdI,QAAQ,EAAE,SAAS;8BACnBM,UAAU,EAAE,KAAK;8BACjBiL,OAAO,EAAE,iBAAiB;8BAC1B5J,YAAY,EAAE,MAAM;8BACpBuM,aAAa,EAAE,WAAW;8BAC1BC,aAAa,EAAE,OAAO;8BACtB3O,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpByC,GAAG,EAAE;4BACP,CAAE;4BAAArC,QAAA,gBACAnD,OAAA,CAACuR,aAAa;8BAACpC,IAAI,EAAE,EAAG;8BAACjM,KAAK,EAAC;4BAAO;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EACxC4D,YAAY;0BAAA;4BAAA/D,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACT,CAAC;wBAEX;sBACF,CAAC,EAAE,CAAC,EAEH2H,YAAY,CAAC4B,WAAW,iBACvBjN,OAAA;wBAAMwC,KAAK,EAAE;0BACXgF,UAAU,EAAE,yBAAyB;0BACrCtE,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE,SAAS;0BACnBM,UAAU,EAAE,KAAK;0BACjBiL,OAAO,EAAE,iBAAiB;0BAC1B5J,YAAY,EAAE;wBAChB,CAAE;wBAAA9B,QAAA,GAAC,QACK,EAACkI,YAAY,CAAC4B,WAAW;sBAAA;wBAAA1J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3B,CACP,eAED1D,OAAA;wBAAKwC,KAAK,EAAE;0BACVU,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE;wBACZ,CAAE;wBAAAH,QAAA,EACC,IAAIkK,IAAI,CAAChC,YAAY,CAAC6C,UAAU,CAAC,CAACmC,kBAAkB,CAAC,OAAO,EAAE;0BAC7DqB,OAAO,EAAE,OAAO;0BAChBC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,OAAO;0BACdC,GAAG,EAAE;wBACP,CAAC;sBAAC;wBAAAtO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN1D,OAAA;sBAAIwC,KAAK,EAAE;wBACTuM,MAAM,EAAE,cAAc;wBACtBzL,QAAQ,EAAE,SAAS;wBACnBM,UAAU,EAAE,KAAK;wBACjBV,KAAK,EAAE,SAAS;wBAChB8L,UAAU,EAAE;sBACd,CAAE;sBAAA7L,QAAA,EACCkI,YAAY,CAACqB;oBAAK;sBAAAnJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAGN1D,OAAA;oBAAKwC,KAAK,EAAE;sBAAEM,OAAO,EAAE,MAAM;sBAAE0C,GAAG,EAAE;oBAAS,CAAE;oBAAArC,QAAA,eAC7CnD,OAAA;sBACEgG,OAAO,EAAEA,CAAA,KAAMU,QAAQ,CAAC,qBAAqB2E,YAAY,CAACG,eAAe,EAAE,CAAE;sBAC7EhJ,KAAK,EAAE;wBACLM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,SAAS;wBACdqJ,OAAO,EAAE,gBAAgB;wBACzBrH,UAAU,EAAE,wBAAwB;wBACpC7D,MAAM,EAAE,kCAAkC;wBAC1CsB,YAAY,EAAE,KAAK;wBACnB/B,KAAK,EAAE,SAAS;wBAChBI,QAAQ,EAAE,SAAS;wBACnBM,UAAU,EAAE,KAAK;wBACjBsB,MAAM,EAAE,SAAS;wBACjBK,UAAU,EAAE;sBACd,CAAE;sBACF5C,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgF,UAAU,GAAG,wBAAwB;sBAC7D,CAAE;sBACF5E,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgF,UAAU,GAAG,wBAAwB;sBAC7D,CAAE;sBAAArE,QAAA,gBAEFnD,OAAA,CAACd,IAAI;wBAACiQ,IAAI,EAAE;sBAAG;wBAAA5L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,QAEpB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN1D,OAAA;kBAAKwC,KAAK,EAAE;oBACVU,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,SAAS;oBACnB0L,UAAU,EAAE,KAAK;oBACjB3L,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,EACCkI,YAAY,CAACa;gBAAO;kBAAA3I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,EAGL2H,YAAY,CAACN,WAAW,IAAIM,YAAY,CAACN,WAAW,CAAC1G,MAAM,GAAG,CAAC,iBAC9DrE,OAAA,CAACiE,YAAY;kBACXC,MAAM,EAAEmH,YAAY,CAACN,WAAY;kBACjC5G,SAAS,EAAEkH,YAAY,CAACqB,KAAM;kBAC9BtI,YAAY,EAAGQ,KAAK,IAAK;oBACvB+B,YAAY,CAAC0E,YAAY,CAACN,WAAW,EAAEnG,KAAK,CAAC;kBAC/C;gBAAE;kBAAArB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACF,eAGD1D,OAAA;kBAAKwC,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE,eAAe;oBAC/B6L,OAAO,EAAE,MAAM;oBACfrH,UAAU,EAAE,qBAAqB;oBACjCvC,YAAY,EAAE,MAAM;oBACpB5B,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,gBACAnD,OAAA;oBAAKwC,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE;oBACP,CAAE;oBAAArC,QAAA,gBAEAnD,OAAA;sBACEgG,OAAO,EAAEA,CAAA,KAAMoF,gBAAgB,CAACC,YAAY,CAAE;sBAC9C7I,KAAK,EAAE;wBACLM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,QAAQ;wBACbgC,UAAU,EAAE,MAAM;wBAClB7D,MAAM,EAAE,MAAM;wBACdT,KAAK,EAAEmI,YAAY,CAACC,aAAa,GAAG,SAAS,GAAG,SAAS;wBACzDpG,MAAM,EAAE,SAAS;wBACjB2J,OAAO,EAAE,QAAQ;wBACjB5J,YAAY,EAAE,KAAK;wBACnBM,UAAU,EAAE,eAAe;wBAC3BjC,QAAQ,EAAE,UAAU;wBACpBM,UAAU,EAAE;sBACd,CAAE;sBACFjB,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgF,UAAU,GAAG,qBAAqB;sBAC1D,CAAE;sBACF5E,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgF,UAAU,GAAG,MAAM;sBAC3C,CAAE;sBAAArE,QAAA,gBAEFnD,OAAA,CAAChB,KAAK;wBACJmQ,IAAI,EAAE,EAAG;wBACTkD,IAAI,EAAEhH,YAAY,CAACC,aAAa,GAAG,SAAS,GAAG;sBAAO;wBAAA/H,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvD,CAAC,eACF1D,OAAA;wBAAAmD,QAAA,EAAOkI,YAAY,CAACiH,cAAc,IAAI;sBAAC;wBAAA/O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzC,CAAC,EAGR2H,YAAY,CAACkH,cAAc,iBAC1BvS,OAAA;sBACEgG,OAAO,EAAEA,CAAA,KAAMmC,eAAe,CAC5BD,YAAY,KAAKmD,YAAY,CAACG,eAAe,GAAG,IAAI,GAAGH,YAAY,CAACG,eACtE,CAAE;sBACFhJ,KAAK,EAAE;wBACLM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,QAAQ;wBACbgC,UAAU,EAAE,MAAM;wBAClB7D,MAAM,EAAE,MAAM;wBACdT,KAAK,EAAEgF,YAAY,KAAKmD,YAAY,CAACG,eAAe,GAAG,SAAS,GAAG,SAAS;wBAC5EtG,MAAM,EAAE,SAAS;wBACjB2J,OAAO,EAAE,QAAQ;wBACjB5J,YAAY,EAAE,KAAK;wBACnBM,UAAU,EAAE,eAAe;wBAC3BjC,QAAQ,EAAE,UAAU;wBACpBM,UAAU,EAAE;sBACd,CAAE;sBACFjB,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgF,UAAU,GAAG,qBAAqB;wBACxDzD,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAG,SAAS;sBACzC,CAAE;sBACFN,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACgF,UAAU,GAAG,MAAM;wBACzCzD,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAGgF,YAAY,KAAKmD,YAAY,CAACG,eAAe,GAAG,SAAS,GAAG,SAAS;sBACrG,CAAE;sBAAArI,QAAA,gBAEFnD,OAAA,CAACjB,aAAa;wBAACoQ,IAAI,EAAE;sBAAG;wBAAA5L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC3B1D,OAAA;wBAAAmD,QAAA,EAAOkI,YAAY,CAACmH,aAAa,IAAI;sBAAC;wBAAAjP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CACT,eAGD1D,OAAA;sBAAKwC,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE,QAAQ;wBACbtC,KAAK,EAAE,SAAS;wBAChBI,QAAQ,EAAE;sBACZ,CAAE;sBAAAH,QAAA,gBACAnD,OAAA,CAACf,GAAG;wBAACkQ,IAAI,EAAE;sBAAG;wBAAA5L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACjB1D,OAAA;wBAAAmD,QAAA,GAAOkI,YAAY,CAACoH,UAAU,IAAI,CAAC,EAAC,QAAM;sBAAA;wBAAAlP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGN1D,OAAA;oBAAKwC,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpByC,GAAG,EAAE,MAAM;sBACXlC,QAAQ,EAAE,SAAS;sBACnBJ,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,gBACAnD,OAAA;sBAAKwC,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpByC,GAAG,EAAE;sBACP,CAAE;sBAAArC,QAAA,gBACAnD,OAAA,CAACb,KAAK;wBAACgQ,IAAI,EAAE;sBAAG;wBAAA5L,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnB1D,OAAA;wBAAAmD,QAAA,GAAM,YAAU,EAACkI,YAAY,CAACqH,cAAc,IAAI,OAAO;sBAAA;wBAAAnP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5D,CAAC,eAEN1D,OAAA;sBAAKwC,KAAK,EAAE;wBACVqM,OAAO,EAAE,gBAAgB;wBACzBrH,UAAU,EAAE6D,YAAY,CAACzJ,MAAM,KAAK,WAAW,GAC3C,wBAAwB,GACxB,0BAA0B;wBAC9BsB,KAAK,EAAEmI,YAAY,CAACzJ,MAAM,KAAK,WAAW,GAAG,SAAS,GAAG,SAAS;wBAClEqD,YAAY,EAAE,KAAK;wBACnBrB,UAAU,EAAE;sBACd,CAAE;sBAAAT,QAAA,EACCkI,YAAY,CAACzJ;oBAAM;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGLwE,YAAY,KAAKmD,YAAY,CAACG,eAAe,IAAIH,YAAY,CAACkH,cAAc,iBAC3EvS,OAAA;kBAAKwC,KAAK,EAAE;oBACVqB,SAAS,EAAE,MAAM;oBACjB8O,UAAU,EAAE,MAAM;oBAClBpC,SAAS,EAAE;kBACb,CAAE;kBAAApN,QAAA,eACAnD,OAAA,CAAC1B,mBAAmB;oBAClByN,cAAc,EAAEV,YAAY,CAACG,eAAgB;oBAC7CoH,aAAa,EAAEvH,YAAY,CAACkH,cAAe;oBAC3CM,eAAe,EAAC;kBAAO;oBAAAtP,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA,GAvXI,gBAAgB2H,YAAY,CAACG,eAAe,EAAE;gBAAAjI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwXhD,CACN;YAAC,gBACF,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL8E,kBAAkB,iBACjBxI,OAAA;MAAKwC,KAAK,EAAE;QACVuC,QAAQ,EAAE,OAAO;QACjBa,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACT9C,eAAe,EAAE,oBAAoB;QACrCH,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxB0L,MAAM,EAAE,IAAI;QACZG,OAAO,EAAE;MACX,CAAE;MACF7I,OAAO,EAAEA,CAAA,KAAMyC,qBAAqB,CAAC,IAAI,CAAE;MAAAtF,QAAA,eAEzCnD,OAAA;QAAKwC,KAAK,EAAE;UACVS,eAAe,EAAE,OAAO;UACxBgC,YAAY,EAAE,MAAM;UACpBiK,QAAQ,EAAE,OAAO;UACjB/J,KAAK,EAAE,MAAM;UACb2N,SAAS,EAAE,MAAM;UACjB9N,QAAQ,EAAE,MAAM;UAChB4J,SAAS,EAAE;QACb,CAAE;QACF5I,OAAO,EAAGjC,CAAC,IAAKA,CAAC,CAACgP,eAAe,CAAC,CAAE;QAAA5P,QAAA,gBAGlCnD,OAAA;UAAKwC,KAAK,EAAE;YACVqM,OAAO,EAAE,QAAQ;YACjBF,YAAY,EAAE,mBAAmB;YACjC7L,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAG,QAAA,gBACAnD,OAAA;YAAKwC,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE;YACP,CAAE;YAAArC,QAAA,gBACAnD,OAAA,CAACnB,GAAG;cAACsQ,IAAI,EAAE,EAAG;cAAC3M,KAAK,EAAE;gBAAEU,KAAK,EAAE;cAAU;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC9C1D,OAAA;cAAIwC,KAAK,EAAE;gBACTuM,MAAM,EAAE,CAAC;gBACTzL,QAAQ,EAAE,SAAS;gBACnBM,UAAU,EAAE,KAAK;gBACjBV,KAAK,EAAE;cACT,CAAE;cAAAC,QAAA,EAAC;YAEH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACN1D,OAAA;YACEgG,OAAO,EAAEA,CAAA,KAAMyC,qBAAqB,CAAC,IAAI,CAAE;YAC3CjG,KAAK,EAAE;cACLgF,UAAU,EAAE,MAAM;cAClB7D,MAAM,EAAE,MAAM;cACdL,QAAQ,EAAE,QAAQ;cAClBJ,KAAK,EAAE,SAAS;cAChBgC,MAAM,EAAE,SAAS;cACjB2J,OAAO,EAAE,SAAS;cAClB5J,YAAY,EAAE,KAAK;cACnBM,UAAU,EAAE;YACd,CAAE;YACF5C,YAAY,EAAGoB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAG,SAAS;YACzC,CAAE;YACFN,YAAY,EAAGmB,CAAC,IAAK;cACnBA,CAAC,CAAC2B,aAAa,CAAClD,KAAK,CAACU,KAAK,GAAG,SAAS;YACzC,CAAE;YAAAC,QAAA,EACH;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGN1D,OAAA;UAAKwC,KAAK,EAAE;YAAEqM,OAAO,EAAE;UAAS,CAAE;UAAA1L,QAAA,gBAChCnD,OAAA;YAAKwC,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE,SAAS;cACdnC,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,GACC,CAAC,MAAM;cACN,MAAMmE,YAAY,GAAG,CAACkB,kBAAkB,CAACyH,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;cAClF,MAAMC,aAAa,GAAG9I,gBAAgB,CAACC,YAAY,CAAC;cACpD,MAAMiK,aAAa,GAAGpB,aAAa,CAAC1I,IAAI;cAExC,oBACEzH,OAAA;gBAAMwC,KAAK,EAAE;kBACXgF,UAAU,EAAE2I,aAAa,CAAC3I,UAAU;kBACpCtE,KAAK,EAAE,OAAO;kBACdI,QAAQ,EAAE,SAAS;kBACnBM,UAAU,EAAE,KAAK;kBACjBiL,OAAO,EAAE,iBAAiB;kBAC1B5J,YAAY,EAAE,MAAM;kBACpBuM,aAAa,EAAE,WAAW;kBAC1BC,aAAa,EAAE,OAAO;kBACtB3O,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpByC,GAAG,EAAE;gBACP,CAAE;gBAAArC,QAAA,gBACAnD,OAAA,CAACuR,aAAa;kBAACpC,IAAI,EAAE,EAAG;kBAACjM,KAAK,EAAC;gBAAO;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,EACxC4D,YAAY;cAAA;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC;YAEX,CAAC,EAAE,CAAC,eAEJ1D,OAAA;cAAMwC,KAAK,EAAE;gBACXgF,UAAU,EAAE,mDAAmD;gBAC/DtE,KAAK,EAAE,OAAO;gBACd2L,OAAO,EAAE,iBAAiB;gBAC1B5J,YAAY,EAAE,MAAM;gBACpB3B,QAAQ,EAAE,SAAS;gBACnBM,UAAU,EAAE,KAAK;gBACjBd,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,gBACAnD,OAAA,CAACnB,GAAG;gBAACsQ,IAAI,EAAE;cAAG;gBAAA5L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEnB;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAEN1D,OAAA;YAAIwC,KAAK,EAAE;cACTuM,MAAM,EAAE,YAAY;cACpBzL,QAAQ,EAAE,QAAQ;cAClBM,UAAU,EAAE,KAAK;cACjBV,KAAK,EAAE,SAAS;cAChB8L,UAAU,EAAE;YACd,CAAE;YAAA7L,QAAA,EACCqF,kBAAkB,CAACkE;UAAK;YAAAnJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eAEL1D,OAAA;YAAKwC,KAAK,EAAE;cACVU,KAAK,EAAE,SAAS;cAChBI,QAAQ,EAAE,MAAM;cAChB0L,UAAU,EAAE,KAAK;cACjB3L,YAAY,EAAE;YAChB,CAAE;YAAAF,QAAA,EACCqF,kBAAkB,CAAC0D;UAAO;YAAA3I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB,CAAC,EAGL8E,kBAAkB,CAACuC,WAAW,IAAIvC,kBAAkB,CAACuC,WAAW,CAAC1G,MAAM,GAAG,CAAC,iBAC1ErE,OAAA;YAAKwC,KAAK,EAAE;cAAEa,YAAY,EAAE;YAAS,CAAE;YAAAF,QAAA,eACrCnD,OAAA,CAACiE,YAAY;cACXC,MAAM,EAAEsE,kBAAkB,CAACuC,WAAY;cACvC5G,SAAS,EAAEqE,kBAAkB,CAACkE,KAAM;cACpCtI,YAAY,EAAGQ,KAAK,IAAK;gBACvB+B,YAAY,CAAC6B,kBAAkB,CAACuC,WAAW,EAAEnG,KAAK,CAAC;cACrD;YAAE;cAAArB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eAED1D,OAAA;YAAKwC,KAAK,EAAE;cACVM,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpByC,GAAG,EAAE,MAAM;cACXlC,QAAQ,EAAE,UAAU;cACpBJ,KAAK,EAAE,SAAS;cAChByP,UAAU,EAAE,MAAM;cAClBpC,SAAS,EAAE;YACb,CAAE;YAAApN,QAAA,gBACAnD,OAAA;cAAKwC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpByC,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,gBACAnD,OAAA,CAAClB,QAAQ;gBAACqQ,IAAI,EAAE;cAAG;gBAAA5L,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACtB1D,OAAA;gBAAAmD,QAAA,GAAM,aAAW,EAAC,IAAIkK,IAAI,CAAC7E,kBAAkB,CAAC0F,UAAU,CAAC,CAACmC,kBAAkB,CAAC,CAAC;cAAA;gBAAA9M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnF,CAAC,EACL8E,kBAAkB,CAACwK,WAAW,iBAC7BhT,OAAA;cAAAmD,QAAA,GAAK,MACC,EAACqF,kBAAkB,CAACwK,WAAW;YAAA;cAAAzP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD1D,OAAA,CAACxB,aAAa;MACZ0F,MAAM,EAAEyE,cAAe;MACvB/B,YAAY,EAAEgC,oBAAqB;MACnCqK,MAAM,EAAEvK,YAAa;MACrBwK,OAAO,EAAEA,CAAA,KAAM/L,eAAe,CAAC,KAAK,CAAE;MACtChD,SAAS,EAAC;IAAoB;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC+C,GAAA,CA3xDID,aAAuB;EAAA,QACVrI,WAAW,EA4GLE,aAAa;AAAA;AAAA8U,GAAA,GA7GhC3M,aAAuB;AA6xD7B,eAAeA,aAAa;AAAC,IAAAxC,EAAA,EAAAuC,GAAA,EAAA4M,GAAA;AAAAC,YAAA,CAAApP,EAAA;AAAAoP,YAAA,CAAA7M,GAAA;AAAA6M,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}