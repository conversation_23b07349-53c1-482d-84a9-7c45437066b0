{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M6 11h8a4 4 0 0 0 0-8H9v18\",\n  key: \"18ai8t\"\n}], [\"path\", {\n  d: \"M6 15h8\",\n  key: \"1y8f6l\"\n}]];\nconst RussianRuble = createLucideIcon(\"russian-ruble\", __iconNode);\nexport { __iconNode, RussianRuble as default };\n//# sourceMappingURL=russian-ruble.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}