{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 20h.01\",\n  key: \"zekei9\"\n}]];\nconst WifiZero = createLucideIcon(\"wifi-zero\", __iconNode);\nexport { __iconNode, WifiZero as default };\n//# sourceMappingURL=wifi-zero.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}