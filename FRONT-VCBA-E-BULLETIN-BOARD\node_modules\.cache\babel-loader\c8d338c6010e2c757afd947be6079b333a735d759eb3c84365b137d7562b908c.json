{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M11 12h.01\",\n  key: \"1lr4k6\"\n}], [\"path\", {\n  d: \"M13 22c.5-.5 1.12-1 2.5-1-1.38 0-2-.5-2.5-1\",\n  key: \"fatpdi\"\n}], [\"path\", {\n  d: \"M14 2a3.28 3.28 0 0 1-3.227 1.798l-6.17-.561A2.387 2.387 0 1 0 4.387 8H15.5a1 1 0 0 1 0 13 1 1 0 0 0 0-5H12a7 7 0 0 1-7-7V8\",\n  key: \"kehrqe\"\n}], [\"path\", {\n  d: \"M14 8a8.5 8.5 0 0 1 0 8\",\n  key: \"1imjx2\"\n}], [\"path\", {\n  d: \"M16 16c2 0 4.5-4 4-6\",\n  key: \"z0nejz\"\n}]];\nconst Shrimp = createLucideIcon(\"shrimp\", __iconNode);\nexport { __iconNode, Shrimp as default };\n//# sourceMappingURL=shrimp.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}