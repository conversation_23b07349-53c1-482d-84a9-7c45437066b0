{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 3v18\",\n  key: \"108xh3\"\n}], [\"path\", {\n  d: \"m16 16 4-4-4-4\",\n  key: \"1js579\"\n}], [\"path\", {\n  d: \"m8 8-4 4 4 4\",\n  key: \"1whems\"\n}]];\nconst SeparatorVertical = createLucideIcon(\"separator-vertical\", __iconNode);\nexport { __iconNode, SeparatorVertical as default };\n//# sourceMappingURL=separator-vertical.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}