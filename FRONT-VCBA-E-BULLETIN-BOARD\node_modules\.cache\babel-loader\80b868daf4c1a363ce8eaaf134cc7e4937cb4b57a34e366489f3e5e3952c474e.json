{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 21v-1\",\n  key: \"1u8rkd\"\n}], [\"path\", {\n  d: \"M10 4V3\",\n  key: \"pkzwkn\"\n}], [\"path\", {\n  d: \"M10 9a3 3 0 0 0 0 6\",\n  key: \"gv75dk\"\n}], [\"path\", {\n  d: \"m14 20 1.25-2.5L18 18\",\n  key: \"1chtki\"\n}], [\"path\", {\n  d: \"m14 4 1.25 2.5L18 6\",\n  key: \"1b4wsy\"\n}], [\"path\", {\n  d: \"m17 21-3-6 1.5-3H22\",\n  key: \"o5qa3v\"\n}], [\"path\", {\n  d: \"m17 3-3 6 1.5 3\",\n  key: \"11697g\"\n}], [\"path\", {\n  d: \"M2 12h1\",\n  key: \"1uaihz\"\n}], [\"path\", {\n  d: \"m20 10-1.5 2 1.5 2\",\n  key: \"1swlpi\"\n}], [\"path\", {\n  d: \"m3.64 18.36.7-.7\",\n  key: \"105rm9\"\n}], [\"path\", {\n  d: \"m4.34 6.34-.7-.7\",\n  key: \"d3unjp\"\n}]];\nconst SunSnow = createLucideIcon(\"sun-snow\", __iconNode);\nexport { __iconNode, SunSnow as default };\n//# sourceMappingURL=sun-snow.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}