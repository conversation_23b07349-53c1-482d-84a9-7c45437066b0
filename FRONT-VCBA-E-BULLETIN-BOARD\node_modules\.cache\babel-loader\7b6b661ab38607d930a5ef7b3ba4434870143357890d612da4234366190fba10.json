{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m2.37 11.223 8.372-6.777a2 2 0 0 1 2.516 0l8.371 6.777\",\n  key: \"f1wd0e\"\n}], [\"path\", {\n  d: \"M21 15a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-5.25\",\n  key: \"1pfu07\"\n}], [\"path\", {\n  d: \"M3 15a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h9\",\n  key: \"1oq9qw\"\n}], [\"path\", {\n  d: \"m6.67 15 6.13 4.6a2 2 0 0 0 2.8-.4l3.15-4.2\",\n  key: \"1fnwu5\"\n}], [\"rect\", {\n  width: \"20\",\n  height: \"4\",\n  x: \"2\",\n  y: \"11\",\n  rx: \"1\",\n  key: \"itshg\"\n}]];\nconst Sandwich = createLucideIcon(\"sandwich\", __iconNode);\nexport { __iconNode, Sandwich as default };\n//# sourceMappingURL=sandwich.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}