{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M18 11c-1.5 0-2.5.5-3 2\",\n  key: \"1fod00\"\n}], [\"path\", {\n  d: \"M4 6a2 2 0 0 0-2 2v4a5 5 0 0 0 5 5 8 8 0 0 1 5 2 8 8 0 0 1 5-2 5 5 0 0 0 5-5V8a2 2 0 0 0-2-2h-3a8 8 0 0 0-5 2 8 8 0 0 0-5-2z\",\n  key: \"d70hit\"\n}], [\"path\", {\n  d: \"M6 11c1.5 0 2.5.5 3 2\",\n  key: \"136fht\"\n}]];\nconst VenetianMask = createLucideIcon(\"venetian-mask\", __iconNode);\nexport { __iconNode, VenetianMask as default };\n//# sourceMappingURL=venetian-mask.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}