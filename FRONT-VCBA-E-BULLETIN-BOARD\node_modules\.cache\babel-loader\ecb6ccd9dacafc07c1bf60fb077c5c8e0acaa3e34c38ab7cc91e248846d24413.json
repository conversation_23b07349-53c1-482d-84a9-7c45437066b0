{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"polygon\", {\n  points: \"12 2 19 21 12 17 5 21 12 2\",\n  key: \"x8c0qg\"\n}]];\nconst Navigation2 = createLucideIcon(\"navigation-2\", __iconNode);\nexport { __iconNode, Navigation2 as default };\n//# sourceMappingURL=navigation-2.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}