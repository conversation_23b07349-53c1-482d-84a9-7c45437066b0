{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 21v-6\",\n  key: \"lihzve\"\n}], [\"path\", {\n  d: \"M12 9V3\",\n  key: \"da5inc\"\n}], [\"path\", {\n  d: \"M3 15h18\",\n  key: \"5xshup\"\n}], [\"path\", {\n  d: \"M3 9h18\",\n  key: \"1pudct\"\n}], [\"rect\", {\n  width: \"18\",\n  height: \"18\",\n  x: \"3\",\n  y: \"3\",\n  rx: \"2\",\n  key: \"afitv7\"\n}]];\nconst TableCellsMerge = createLucideIcon(\"table-cells-merge\", __iconNode);\nexport { __iconNode, TableCellsMerge as default };\n//# sourceMappingURL=table-cells-merge.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}