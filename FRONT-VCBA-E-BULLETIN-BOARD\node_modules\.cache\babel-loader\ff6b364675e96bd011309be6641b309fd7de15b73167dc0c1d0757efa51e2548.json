{"ast": null, "code": "var _jsxFileName = \"D:\\\\capstone-e-bulletin-reactjs-proj\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\admin\\\\AdminNewsfeed.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { announcementService } from '../../services';\nimport { useCategories } from '../../hooks/useAnnouncements';\nimport AdminCommentSection from '../../components/admin/AdminCommentSection';\nimport FacebookImageGallery from '../../components/common/FacebookImageGallery';\nimport { getImageUrl, API_BASE_URL } from '../../config/constants';\nimport { Newspaper, Search, Pin, Calendar, MessageSquare, Heart, Eye, Edit, Users, LayoutDashboard, BookOpen, PartyPopper, AlertTriangle, Clock, Trophy, Briefcase, GraduationCap, Flag, Coffee, Plane } from 'lucide-react';\n\n// Image Display Component\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ImageDisplay = ({\n  imagePath,\n  alt,\n  style,\n  onMouseEnter,\n  onMouseLeave\n}) => {\n  _s();\n  const [imageError, setImageError] = useState(false);\n  const [imageLoading, setImageLoading] = useState(true);\n  const handleImageError = () => {\n    setImageError(true);\n    setImageLoading(false);\n  };\n  const handleImageLoad = () => {\n    setImageLoading(false);\n  };\n  if (imageError) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f3f4f6',\n        color: '#6b7280',\n        fontSize: '0.875rem'\n      },\n      children: \"Image not available\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'relative',\n      ...style\n    },\n    children: [imageLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f3f4f6',\n        color: '#6b7280'\n      },\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"img\", {\n      src: getImageUrl(imagePath || '') || '',\n      alt: alt,\n      style: {\n        ...style,\n        opacity: imageLoading ? 0 : 1,\n        transition: 'opacity 0.3s ease'\n      },\n      onError: handleImageError,\n      onLoad: handleImageLoad,\n      onMouseEnter: onMouseEnter,\n      onMouseLeave: onMouseLeave\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 95,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n};\n\n// Image Gallery Component\n_s(ImageDisplay, \"yA6MC4/13YXgE42AlKw5vrWMK58=\");\n_c = ImageDisplay;\nconst ImageGallery = ({\n  images,\n  altPrefix,\n  onImageClick\n}) => {\n  if (!images || images.length === 0) return null;\n  const visibleImages = images.slice(0, 4);\n  const remainingCount = Math.max(0, images.length - 4);\n  const getContainerStyle = (index, total) => {\n    const baseStyle = {\n      position: 'relative',\n      overflow: 'hidden',\n      borderRadius: '12px',\n      cursor: onImageClick ? 'pointer' : 'default'\n    };\n    if (total === 1) {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '300px'\n      };\n    } else if (total === 2) {\n      return {\n        ...baseStyle,\n        width: '50%',\n        height: '250px'\n      };\n    } else if (total === 3) {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '50%',\n          height: '250px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '50%',\n          height: '120px'\n        };\n      }\n    } else {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '50%',\n          height: '250px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '33.33%',\n          height: '120px'\n        };\n      }\n    }\n  };\n  const getImageStyle = (index, total) => {\n    return {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover',\n      transition: 'transform 0.3s ease'\n    };\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: getContainerStyle(0, visibleImages.length),\n      children: [/*#__PURE__*/_jsxDEV(ImageDisplay, {\n        imagePath: visibleImages[0].file_path,\n        alt: `${altPrefix} - Image 1`,\n        style: getImageStyle(0, visibleImages.length),\n        onMouseEnter: e => {\n          e.currentTarget.style.transform = 'scale(1.02)';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.transform = 'scale(1)';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          cursor: 'pointer'\n        },\n        onClick: () => onImageClick(0)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this), visibleImages.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: visibleImages.length === 2 ? 'row' : 'column',\n        gap: '4px',\n        width: visibleImages.length === 2 ? '50%' : '50%'\n      },\n      children: visibleImages.slice(1).map((image, idx) => {\n        const actualIndex = idx + 1;\n        const isLast = actualIndex === visibleImages.length - 1 && remainingCount > 0;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            ...getContainerStyle(actualIndex, visibleImages.length),\n            position: 'relative'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ImageDisplay, {\n            imagePath: image.file_path,\n            alt: `${altPrefix} - Image ${actualIndex + 1}`,\n            style: getImageStyle(actualIndex, visibleImages.length),\n            onMouseEnter: e => {\n              e.currentTarget.style.transform = 'scale(1.02)';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.transform = 'scale(1)';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 17\n          }, this), isLast && remainingCount > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              backgroundColor: 'rgba(0, 0, 0, 0.6)',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              color: 'white',\n              fontSize: '1.5rem',\n              fontWeight: '600'\n            },\n            children: [\"+\", remainingCount]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 19\n          }, this), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            },\n            onClick: () => onImageClick(actualIndex)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 249,\n            columnNumber: 19\n          }, this)]\n        }, actualIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 198,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 162,\n    columnNumber: 5\n  }, this);\n};\n\n// Main AdminNewsfeed Component\n_c2 = ImageGallery;\nconst AdminNewsfeed = () => {\n  _s2();\n  const navigate = useNavigate();\n\n  // Category styling function\n  const getCategoryStyle = categoryName => {\n    const styles = {\n      'Academic': {\n        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n        icon: BookOpen\n      },\n      'General': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Users\n      },\n      'Events': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: PartyPopper\n      },\n      'Emergency': {\n        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n        icon: AlertTriangle\n      },\n      'Sports': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Trophy\n      },\n      'Deadlines': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Clock\n      }\n    };\n    return styles[categoryName] || styles['General'];\n  };\n\n  // Holiday type styling function\n  const getHolidayTypeStyle = holidayTypeName => {\n    const styles = {\n      'National Holiday': {\n        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n        icon: Flag\n      },\n      'School Event': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: GraduationCap\n      },\n      'Academic Break': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Coffee\n      },\n      'Sports Event': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Trophy\n      },\n      'Field Trip': {\n        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n        icon: Plane\n      },\n      'Meeting': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Briefcase\n      }\n    };\n    return styles[holidayTypeName] || styles['School Event'];\n  };\n\n  // Filter states\n  const [filterCategory, setFilterCategory] = useState('');\n  const [filterGradeLevel, setFilterGradeLevel] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // UI states\n  const [showComments, setShowComments] = useState(null);\n  const [newComment, setNewComment] = useState({});\n  const [submittingComment, setSubmittingComment] = useState(null);\n\n  // Data states\n  const [announcements, setAnnouncements] = useState([]);\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState([]);\n  const [calendarEvents, setCalendarEvents] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState();\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState();\n  const [recentStudents, setRecentStudents] = useState([]);\n  const [studentLoading, setStudentLoading] = useState(false);\n  const {\n    categories\n  } = useCategories();\n\n  // Fetch published announcements with images (admin can see all)\n  const fetchPublishedAnnouncements = async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const response = await fetch(`${API_BASE_URL}/api/announcements?status=published&page=1&limit=50&sort_by=created_at&sort_order=DESC`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (data.success && data.data) {\n        const announcementsData = data.data.announcements || [];\n\n        // Fetch images for each announcement\n        const announcementsWithImages = await Promise.all(announcementsData.map(async announcement => {\n          try {\n            const imageResponse = await fetch(`${API_BASE_URL}/api/announcements/${announcement.announcement_id}/images`, {\n              headers: {\n                'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n                'Content-Type': 'application/json'\n              }\n            });\n            const imageData = await imageResponse.json();\n            if (imageData.success && imageData.data) {\n              announcement.images = imageData.data.images || [];\n            } else {\n              announcement.images = [];\n            }\n          } catch (imgErr) {\n            console.warn(`Failed to fetch images for announcement ${announcement.announcement_id}:`, imgErr);\n            announcement.images = [];\n          }\n          return announcement;\n        }));\n        setAnnouncements(announcementsWithImages);\n\n        // Separate pinned announcements\n        const pinned = announcementsWithImages.filter(ann => ann.is_pinned === 1);\n        setPinnedAnnouncements(pinned);\n      } else {\n        setError('Failed to load announcements');\n      }\n    } catch (err) {\n      console.error('Error fetching announcements:', err);\n      setError(err.message || 'Failed to load announcements');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch calendar events\n  const fetchCalendarEvents = async () => {\n    try {\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(eventsData.map(async event => {\n          try {\n            const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`, {\n              headers: {\n                'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n                'Content-Type': 'application/json'\n              }\n            });\n            const imageData = await imageResponse.json();\n            if (imageData.success && imageData.data) {\n              event.images = imageData.data.attachments || [];\n            } else {\n              event.images = [];\n            }\n          } catch (imgErr) {\n            console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n            event.images = [];\n          }\n          return event;\n        }));\n        setCalendarEvents(eventsWithImages);\n      } else {\n        setCalendarError('Failed to load calendar events');\n      }\n    } catch (err) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to load calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  };\n\n  // Fetch recent student registrations\n  const fetchRecentStudents = async () => {\n    try {\n      setStudentLoading(true);\n      const response = await fetch(`${API_BASE_URL}/api/students?page=1&limit=5&sort_by=created_at&sort_order=DESC`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n      if (data.success && data.data) {\n        setRecentStudents(data.data.students || []);\n      }\n    } catch (err) {\n      console.error('Error fetching recent students:', err);\n    } finally {\n      setStudentLoading(false);\n    }\n  };\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchPublishedAnnouncements();\n    fetchCalendarEvents();\n    fetchRecentStudents();\n  }, []);\n\n  // Handle like/unlike functionality (admin perspective)\n  const handleLikeToggle = async announcement => {\n    try {\n      if (announcement.user_reaction) {\n        await announcementService.removeReaction(announcement.announcement_id);\n      } else {\n        const response = await fetch(`${API_BASE_URL}/api/announcements/${announcement.announcement_id}/reactions`, {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            reaction_type_id: 1,\n            notify_admin: false // Admin doesn't need to notify themselves\n          })\n        });\n        if (!response.ok) {\n          throw new Error('Failed to add reaction');\n        }\n      }\n\n      // Refresh data\n      await fetchPublishedAnnouncements();\n    } catch (error) {\n      console.error('Error toggling like:', error);\n    }\n  };\n\n  // Handle comment submission (admin perspective)\n  const handleCommentSubmit = async (announcementId, commentText) => {\n    if (!commentText.trim()) return;\n    try {\n      setSubmittingComment(announcementId);\n      const response = await fetch(`${API_BASE_URL}/api/admin/announcements/${announcementId}/comments`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          content: commentText,\n          commenter_name: 'Admin User',\n          commenter_email: '<EMAIL>',\n          notify_admin: false\n        })\n      });\n      if (response.ok) {\n        setNewComment(prev => ({\n          ...prev,\n          [announcementId]: ''\n        }));\n        await fetchPublishedAnnouncements();\n      } else {\n        console.error('Failed to submit comment');\n      }\n    } catch (error) {\n      console.error('Error submitting comment:', error);\n    } finally {\n      setSubmittingComment(null);\n    }\n  };\n\n  // Filter announcements\n  const filteredAnnouncements = announcements.filter(announcement => {\n    var _announcement$categor, _announcement$grade_l;\n    const matchesSearch = !searchTerm || announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) || announcement.content.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = !filterCategory || ((_announcement$categor = announcement.category_id) === null || _announcement$categor === void 0 ? void 0 : _announcement$categor.toString()) === filterCategory;\n    const matchesGradeLevel = !filterGradeLevel || ((_announcement$grade_l = announcement.grade_level) === null || _announcement$grade_l === void 0 ? void 0 : _announcement$grade_l.toString()) === filterGradeLevel;\n    return matchesSearch && matchesCategory && matchesGradeLevel;\n  });\n\n  // Filter calendar events with date-based filtering\n  const filteredCalendarEvents = calendarEvents.filter(event => {\n    const matchesSearch = !searchTerm || event.title.toLowerCase().includes(searchTerm.toLowerCase()) || event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase());\n\n    // Only show events that are published and on or after today's date\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    const eventDate = new Date(event.event_date);\n    eventDate.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    const isEventDateValid = eventDate >= today;\n    const isPublished = event.is_published === 1;\n    return matchesSearch && isEventDateValid && isPublished;\n  });\n\n  // Combine and sort all content by date (most recent first)\n  const displayAnnouncements = filteredAnnouncements;\n  const displayEvents = filteredCalendarEvents;\n\n  // Create combined content array for better chronological display\n  const combinedContent = [...displayAnnouncements.map(item => ({\n    ...item,\n    type: 'announcement',\n    sortDate: new Date(item.created_at)\n  })), ...displayEvents.map(item => ({\n    ...item,\n    type: 'event',\n    sortDate: new Date(item.event_date)\n  }))].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)',\n      position: 'relative'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundImage: `\n          radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.03) 0%, transparent 50%),\n          radial-gradient(circle at 75% 75%, rgba(250, 204, 21, 0.03) 0%, transparent 50%)\n        `,\n        pointerEvents: 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 617,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'relative',\n        zIndex: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"header\", {\n        style: {\n          background: 'white',\n          borderBottom: '1px solid #e5e7eb',\n          position: 'sticky',\n          top: 0,\n          zIndex: 100,\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            padding: '0 2rem',\n            height: '72px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1.5rem',\n              minWidth: '300px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/logo/vcba1.png\",\n              alt: \"VCBA Logo\",\n              style: {\n                width: '48px',\n                height: '48px',\n                objectFit: 'contain'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 654,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                style: {\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827',\n                  lineHeight: '1.2'\n                },\n                children: \"VCBA E-Bulletin Board\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 664,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280',\n                  lineHeight: '1.2'\n                },\n                children: \"Admin Newsfeed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 673,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 648,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1,\n              maxWidth: '500px',\n              margin: '0 2rem'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                position: 'relative'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Search, {\n                size: 20,\n                style: {\n                  position: 'absolute',\n                  left: '1rem',\n                  top: '50%',\n                  transform: 'translateY(-50%)',\n                  color: '#9ca3af'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 691,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                placeholder: \"Search announcements and events...\",\n                value: searchTerm,\n                onChange: e => setSearchTerm(e.target.value),\n                style: {\n                  width: '100%',\n                  height: '44px',\n                  padding: '0 1rem 0 3rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '12px',\n                  background: '#f9fafb',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  transition: 'all 0.2s ease'\n                },\n                onFocus: e => {\n                  e.currentTarget.style.borderColor = '#22c55e';\n                  e.currentTarget.style.background = 'white';\n                  e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n                },\n                onBlur: e => {\n                  e.currentTarget.style.borderColor = '#d1d5db';\n                  e.currentTarget.style.background = '#f9fafb';\n                  e.currentTarget.style.boxShadow = 'none';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 701,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 690,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 685,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              minWidth: '400px',\n              justifyContent: 'flex-end'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.5rem',\n                background: '#f9fafb',\n                borderRadius: '12px',\n                border: '1px solid #e5e7eb'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                value: filterCategory,\n                onChange: e => setFilterCategory(e.target.value),\n                style: {\n                  padding: '0.5rem 0.75rem',\n                  border: 'none',\n                  borderRadius: '8px',\n                  background: 'white',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  cursor: 'pointer',\n                  minWidth: '110px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"All Categories\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 766,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"1\",\n                  children: \"Academic\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 767,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"2\",\n                  children: \"Events\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 768,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"3\",\n                  children: \"Administrative\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 769,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 751,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                value: filterGradeLevel,\n                onChange: e => setFilterGradeLevel(e.target.value),\n                style: {\n                  padding: '0.5rem 0.75rem',\n                  border: 'none',\n                  borderRadius: '8px',\n                  background: 'white',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  outline: 'none',\n                  cursor: 'pointer',\n                  minWidth: '100px'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"\",\n                  children: \"All Grades\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 787,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"11\",\n                  children: \"Grade 11\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 788,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                  value: \"12\",\n                  children: \"Grade 12\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 789,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 772,\n                columnNumber: 17\n              }, this), (searchTerm || filterCategory || filterGradeLevel) && /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => {\n                  setSearchTerm('');\n                  setFilterCategory('');\n                  setFilterGradeLevel('');\n                },\n                style: {\n                  padding: '0.5rem 0.75rem',\n                  border: 'none',\n                  borderRadius: '8px',\n                  background: '#ef4444',\n                  color: 'white',\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.background = '#dc2626';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.background = '#ef4444';\n                },\n                children: \"Clear\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 793,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 742,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => navigate('/admin/dashboard'),\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.75rem 1rem',\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                border: 'none',\n                borderRadius: '12px',\n                color: 'white',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease',\n                boxShadow: '0 2px 8px rgba(34, 197, 94, 0.2)'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.2)';\n              },\n              children: [/*#__PURE__*/_jsxDEV(LayoutDashboard, {\n                size: 16\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 849,\n                columnNumber: 17\n              }, this), \"Dashboard\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 823,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 733,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 640,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 632,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '2rem',\n          display: 'flex',\n          gap: '2rem',\n          alignItems: 'flex-start'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            width: '320px',\n            flexShrink: 0\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              borderRadius: '16px',\n              border: '1px solid #e5e7eb',\n              overflow: 'hidden',\n              position: 'sticky',\n              top: '100px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '1.5rem 1.5rem 1rem',\n                borderBottom: '1px solid #f3f4f6'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.75rem',\n                  marginBottom: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Pin, {\n                  size: 20,\n                  style: {\n                    color: '#22c55e'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 889,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#111827'\n                  },\n                  children: \"Pinned Posts\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 890,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 883,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                },\n                children: \"Important announcements and updates\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 899,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 879,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                padding: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '1rem',\n                  background: '#f8fafc',\n                  borderRadius: '12px',\n                  border: '1px solid #e2e8f0',\n                  marginBottom: '1rem',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.background = '#f1f5f9';\n                  e.currentTarget.style.borderColor = '#22c55e';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.background = '#f8fafc';\n                  e.currentTarget.style.borderColor = '#e2e8f0';\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    gap: '0.75rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '8px',\n                      height: '8px',\n                      background: '#22c55e',\n                      borderRadius: '50%',\n                      marginTop: '0.5rem',\n                      flexShrink: 0\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 933,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      style: {\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '0.875rem',\n                        fontWeight: '600',\n                        color: '#111827',\n                        lineHeight: '1.4'\n                      },\n                      children: \"Important: New Academic Calendar Released\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 942,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      style: {\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '0.8rem',\n                        color: '#6b7280',\n                        lineHeight: '1.4'\n                      },\n                      children: \"Please review the updated academic calendar for the upcoming semester...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 951,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        fontSize: '0.75rem',\n                        color: '#9ca3af'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                        size: 12\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 966,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"2 days ago\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 967,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 959,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 941,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 928,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 911,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '1rem',\n                  background: '#f8fafc',\n                  borderRadius: '12px',\n                  border: '1px solid #e2e8f0',\n                  marginBottom: '1rem',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.background = '#f1f5f9';\n                  e.currentTarget.style.borderColor = '#22c55e';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.background = '#f8fafc';\n                  e.currentTarget.style.borderColor = '#e2e8f0';\n                },\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    gap: '0.75rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '8px',\n                      height: '8px',\n                      background: '#ef4444',\n                      borderRadius: '50%',\n                      marginTop: '0.5rem',\n                      flexShrink: 0\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 996,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                      style: {\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '0.875rem',\n                        fontWeight: '600',\n                        color: '#111827',\n                        lineHeight: '1.4'\n                      },\n                      children: \"Urgent: System Maintenance Notice\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1005,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      style: {\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '0.8rem',\n                        color: '#6b7280',\n                        lineHeight: '1.4'\n                      },\n                      children: \"Scheduled maintenance will occur this weekend. Please save your work...\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1014,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        fontSize: '0.75rem',\n                        color: '#9ca3af'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                        size: 12\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1029,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: \"1 week ago\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1030,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1022,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1004,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 991,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 974,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                style: {\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #e5e7eb',\n                  borderRadius: '8px',\n                  background: 'white',\n                  color: '#22c55e',\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.background = '#f0fdf4';\n                  e.currentTarget.style.borderColor = '#22c55e';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.background = 'white';\n                  e.currentTarget.style.borderColor = '#e5e7eb';\n                },\n                children: \"View All Pinned Posts\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1037,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 909,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 870,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 866,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            minWidth: 0\n          },\n          children: [(loading || calendarLoading) && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              minHeight: '400px'\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                gap: '1rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '3rem',\n                  height: '3rem',\n                  border: '4px solid rgba(34, 197, 94, 0.2)',\n                  borderTop: '4px solid #22c55e',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1079,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#6b7280',\n                  fontSize: '1rem',\n                  fontWeight: '500'\n                },\n                children: \"Loading content...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1087,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1073,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1067,\n            columnNumber: 13\n          }, this), (error || calendarError) && !loading && !calendarLoading && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '2rem',\n              background: 'rgba(239, 68, 68, 0.1)',\n              border: '1px solid rgba(239, 68, 68, 0.2)',\n              borderRadius: '16px',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '4rem',\n                height: '4rem',\n                background: 'rgba(239, 68, 68, 0.1)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 1rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(MessageSquare, {\n                size: 24,\n                color: \"#ef4444\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1117,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                color: '#ef4444',\n                margin: '0 0 0.5rem 0',\n                fontSize: '1.25rem',\n                fontWeight: '600'\n              },\n              children: \"Error Loading Content\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#6b7280',\n                margin: '0 0 1.5rem 0',\n                fontSize: '1rem'\n              },\n              children: error || calendarError\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1127,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                fetchPublishedAnnouncements();\n                fetchCalendarEvents();\n              },\n              style: {\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '12px',\n                padding: '0.75rem 1.5rem',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = 'none';\n              },\n              children: \"Try Again\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1134,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1100,\n            columnNumber: 13\n          }, this), !loading && !calendarLoading && !error && !calendarError && displayAnnouncements.length === 0 && displayEvents.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '4rem 2rem',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: '5rem',\n                height: '5rem',\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 2rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(Newspaper, {\n                size: 32,\n                color: \"white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1181,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1171,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                color: '#374151',\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '600'\n              },\n              children: \"No Content Available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1183,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              style: {\n                color: '#6b7280',\n                margin: '0 0 2rem 0',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                maxWidth: '500px',\n                marginLeft: 'auto',\n                marginRight: 'auto'\n              },\n              children: searchTerm || filterCategory || filterGradeLevel ? 'No content matches your current filters. Try adjusting your search criteria.' : 'There are no published announcements or events at the moment. Check back later for updates.'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1191,\n              columnNumber: 15\n            }, this), (searchTerm || filterCategory || filterGradeLevel) && /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setSearchTerm('');\n                setFilterCategory('');\n                setFilterGradeLevel('');\n              },\n              style: {\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                color: 'white',\n                border: 'none',\n                borderRadius: '12px',\n                padding: '0.75rem 1.5rem',\n                fontSize: '0.875rem',\n                fontWeight: '600',\n                cursor: 'pointer',\n                transition: 'all 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.transform = 'translateY(-1px)';\n                e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.transform = 'translateY(0)';\n                e.currentTarget.style.boxShadow = 'none';\n              },\n              children: \"Clear Filters\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1206,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1167,\n            columnNumber: 13\n          }, this), !studentLoading && recentStudents.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'rgba(255, 255, 255, 0.95)',\n              borderRadius: '16px',\n              padding: '1.5rem',\n              border: '1px solid rgba(0, 0, 0, 0.1)',\n              backdropFilter: 'blur(10px)',\n              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n              marginBottom: '1.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              style: {\n                fontSize: '1.25rem',\n                fontWeight: '600',\n                color: '#2d5016',\n                margin: '0 0 1rem 0',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              },\n              children: \"\\uD83D\\uDC65 Recent Student Registrations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1249,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'grid',\n                gap: '0.75rem'\n              },\n              children: recentStudents.slice(0, 3).map(student => {\n                var _student$profile, _student$profile2, _student$profile3;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    justifyContent: 'space-between',\n                    alignItems: 'center',\n                    padding: '0.75rem',\n                    backgroundColor: '#f8fdf8',\n                    borderRadius: '8px',\n                    border: '1px solid #e8f5e8'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontWeight: '500',\n                        color: '#2d5016',\n                        fontSize: '0.875rem'\n                      },\n                      children: [(_student$profile = student.profile) === null || _student$profile === void 0 ? void 0 : _student$profile.first_name, \" \", (_student$profile2 = student.profile) === null || _student$profile2 === void 0 ? void 0 : _student$profile2.last_name]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1278,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        fontSize: '0.75rem',\n                        color: '#6b7280'\n                      },\n                      children: [\"Grade \", (_student$profile3 = student.profile) === null || _student$profile3 === void 0 ? void 0 : _student$profile3.grade_level, \" \\u2022 \", student.student_number]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1285,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1277,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      fontSize: '0.75rem',\n                      color: '#6b7280'\n                    },\n                    children: new Date(student.created_at).toLocaleDateString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1292,\n                    columnNumber: 21\n                  }, this)]\n                }, student.student_id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1265,\n                  columnNumber: 19\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1260,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1240,\n            columnNumber: 13\n          }, this), !loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem'\n            },\n            children: [displayEvents.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: displayEvents.map(event => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'rgba(255, 255, 255, 0.95)',\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  border: '1px solid rgba(0, 0, 0, 0.1)',\n                  backdropFilter: 'blur(10px)',\n                  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                  transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-2px)';\n                  e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    gap: '1rem',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '48px',\n                      height: '48px',\n                      background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                      borderRadius: '12px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      flexShrink: 0\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Calendar, {\n                      size: 24,\n                      color: \"white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1352,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1342,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        marginBottom: '0.5rem'\n                      },\n                      children: [(() => {\n                        const holidayTypeName = event.holiday_type_name || 'School Event';\n                        const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                        const IconComponent = holidayStyle.icon;\n                        return /*#__PURE__*/_jsxDEV(\"span\", {\n                          style: {\n                            background: holidayStyle.background,\n                            color: 'white',\n                            fontSize: '0.75rem',\n                            fontWeight: '600',\n                            padding: '0.25rem 0.75rem',\n                            borderRadius: '20px',\n                            textTransform: 'uppercase',\n                            letterSpacing: '0.5px',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.25rem'\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                            size: 12,\n                            color: \"white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1381,\n                            columnNumber: 35\n                          }, this), holidayTypeName]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1368,\n                          columnNumber: 33\n                        }, this);\n                      })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.25rem',\n                          color: '#6b7280',\n                          fontSize: '0.875rem'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                          size: 14\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1394,\n                          columnNumber: 31\n                        }, this), new Date(event.event_date).toLocaleDateString('en-US', {\n                          weekday: 'long',\n                          year: 'numeric',\n                          month: 'long',\n                          day: 'numeric'\n                        })]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1387,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1356,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      style: {\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        color: '#1f2937',\n                        lineHeight: '1.3'\n                      },\n                      children: event.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1404,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1355,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      gap: '0.5rem'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => navigate(`/admin/calendar?event=${event.calendar_id}`),\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem',\n                        padding: '0.5rem 0.75rem',\n                        background: 'rgba(59, 130, 246, 0.1)',\n                        border: '1px solid rgba(59, 130, 246, 0.2)',\n                        borderRadius: '8px',\n                        color: '#3b82f6',\n                        fontSize: '0.75rem',\n                        fontWeight: '500',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = 'rgba(59, 130, 246, 0.2)';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'rgba(59, 130, 246, 0.1)';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Edit, {\n                        size: 12\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1440,\n                        columnNumber: 29\n                      }, this), \"Edit\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1417,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1416,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1336,\n                  columnNumber: 23\n                }, this), event.description && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#4b5563',\n                    fontSize: '0.95rem',\n                    lineHeight: '1.6',\n                    marginBottom: '1rem'\n                  },\n                  children: event.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1448,\n                  columnNumber: 25\n                }, this), (() => {\n                  // Get event images if they exist\n                  const eventImageUrls = [];\n                  if (event.images && event.images.length > 0) {\n                    event.images.forEach(img => {\n                      if (img.file_path) {\n                        // Convert file_path to full URL\n                        const imageUrl = getImageUrl(img.file_path);\n                        if (imageUrl) {\n                          eventImageUrls.push(imageUrl);\n                        }\n                      }\n                    });\n                  }\n                  return eventImageUrls.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      marginBottom: '1rem'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(FacebookImageGallery, {\n                      images: eventImageUrls.filter(Boolean),\n                      altPrefix: event.title,\n                      maxVisible: 4,\n                      onImageClick: index => {\n                        console.log(`Clicked image ${index + 1} for event: ${event.title}`);\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1477,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1476,\n                    columnNumber: 27\n                  }, this) : null;\n                })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '1.5rem',\n                    padding: '1rem',\n                    background: 'rgba(59, 130, 246, 0.05)',\n                    borderRadius: '12px',\n                    fontSize: '0.875rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      color: '#6b7280'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                      size: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1505,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: event.end_date && event.end_date !== event.event_date ? `${new Date(event.event_date).toLocaleDateString()} - ${new Date(event.end_date).toLocaleDateString()}` : new Date(event.event_date).toLocaleDateString()\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1506,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1499,\n                    columnNumber: 25\n                  }, this), event.holiday_type_name && /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.5rem',\n                      color: '#6b7280'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        padding: '0.25rem 0.5rem',\n                        background: 'rgba(59, 130, 246, 0.1)',\n                        borderRadius: '6px',\n                        fontSize: '0.75rem',\n                        fontWeight: '500'\n                      },\n                      children: event.holiday_type_name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1521,\n                      columnNumber: 29\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1515,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1490,\n                  columnNumber: 23\n                }, this)]\n              }, `event-${event.calendar_id}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1315,\n                columnNumber: 21\n              }, this))\n            }, void 0, false), displayAnnouncements.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: displayAnnouncements.map(announcement => /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  background: 'rgba(255, 255, 255, 0.95)',\n                  borderRadius: '16px',\n                  padding: '1.5rem',\n                  border: announcement.is_pinned ? '2px solid rgba(250, 204, 21, 0.3)' : '1px solid rgba(0, 0, 0, 0.1)',\n                  backdropFilter: 'blur(10px)',\n                  boxShadow: announcement.is_pinned ? '0 4px 20px rgba(250, 204, 21, 0.15)' : '0 4px 20px rgba(0, 0, 0, 0.08)',\n                  transition: 'transform 0.2s ease, box-shadow 0.2s ease',\n                  position: 'relative'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.transform = 'translateY(-2px)';\n                  e.currentTarget.style.boxShadow = announcement.is_pinned ? '0 8px 30px rgba(250, 204, 21, 0.25)' : '0 8px 30px rgba(0, 0, 0, 0.12)';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = announcement.is_pinned ? '0 4px 20px rgba(250, 204, 21, 0.15)' : '0 4px 20px rgba(0, 0, 0, 0.08)';\n                },\n                children: [announcement.is_pinned && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    position: 'absolute',\n                    top: '-8px',\n                    right: '1rem',\n                    background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                    color: 'white',\n                    padding: '0.25rem 0.75rem',\n                    borderRadius: '12px',\n                    fontSize: '0.75rem',\n                    fontWeight: '600',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.25rem',\n                    boxShadow: '0 2px 8px rgba(250, 204, 21, 0.3)'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Pin, {\n                    size: 12\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1588,\n                    columnNumber: 27\n                  }, this), \"Pinned\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1573,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    gap: '1rem',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      width: '48px',\n                      height: '48px',\n                      background: announcement.is_alert ? 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)' : 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                      borderRadius: '12px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'center',\n                      flexShrink: 0\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Newspaper, {\n                      size: 24,\n                      color: \"white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1612,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1600,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      flex: 1\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.75rem',\n                        marginBottom: '0.5rem',\n                        flexWrap: 'wrap'\n                      },\n                      children: [(() => {\n                        if (announcement.is_alert) {\n                          return /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                              color: 'white',\n                              fontSize: '0.75rem',\n                              fontWeight: '600',\n                              padding: '0.25rem 0.75rem',\n                              borderRadius: '20px',\n                              textTransform: 'uppercase',\n                              letterSpacing: '0.5px',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                              size: 12,\n                              color: \"white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1639,\n                              columnNumber: 37\n                            }, this), \"Alert\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1626,\n                            columnNumber: 35\n                          }, this);\n                        } else {\n                          const categoryName = announcement.category_name || 'General';\n                          const categoryStyle = getCategoryStyle(categoryName);\n                          const IconComponent = categoryStyle.icon;\n                          return /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              background: categoryStyle.background,\n                              color: 'white',\n                              fontSize: '0.75rem',\n                              fontWeight: '600',\n                              padding: '0.25rem 0.75rem',\n                              borderRadius: '20px',\n                              textTransform: 'uppercase',\n                              letterSpacing: '0.5px',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                              size: 12,\n                              color: \"white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1662,\n                              columnNumber: 37\n                            }, this), categoryName]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1649,\n                            columnNumber: 35\n                          }, this);\n                        }\n                      })(), announcement.category_name && /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          background: 'rgba(107, 114, 128, 0.1)',\n                          color: '#6b7280',\n                          fontSize: '0.75rem',\n                          fontWeight: '500',\n                          padding: '0.25rem 0.75rem',\n                          borderRadius: '20px'\n                        },\n                        children: announcement.category_name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1670,\n                        columnNumber: 31\n                      }, this), announcement.grade_level && /*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          background: 'rgba(59, 130, 246, 0.1)',\n                          color: '#3b82f6',\n                          fontSize: '0.75rem',\n                          fontWeight: '500',\n                          padding: '0.25rem 0.75rem',\n                          borderRadius: '20px'\n                        },\n                        children: [\"Grade \", announcement.grade_level]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1683,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          color: '#6b7280',\n                          fontSize: '0.875rem'\n                        },\n                        children: new Date(announcement.created_at).toLocaleDateString('en-US', {\n                          weekday: 'short',\n                          year: 'numeric',\n                          month: 'short',\n                          day: 'numeric'\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1695,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1616,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      style: {\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        color: '#1f2937',\n                        lineHeight: '1.3'\n                      },\n                      children: announcement.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1708,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1615,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      gap: '0.5rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => navigate(`/admin/posts?edit=${announcement.announcement_id}`),\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem',\n                        padding: '0.5rem 0.75rem',\n                        background: 'rgba(34, 197, 94, 0.1)',\n                        border: '1px solid rgba(34, 197, 94, 0.2)',\n                        borderRadius: '8px',\n                        color: '#22c55e',\n                        fontSize: '0.75rem',\n                        fontWeight: '500',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = 'rgba(34, 197, 94, 0.2)';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'rgba(34, 197, 94, 0.1)';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Edit, {\n                        size: 12\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1744,\n                        columnNumber: 29\n                      }, this), \"Edit\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1721,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem',\n                        padding: '0.5rem 0.75rem',\n                        background: 'rgba(59, 130, 246, 0.1)',\n                        border: '1px solid rgba(59, 130, 246, 0.2)',\n                        borderRadius: '8px',\n                        color: '#3b82f6',\n                        fontSize: '0.75rem',\n                        fontWeight: '500',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = 'rgba(59, 130, 246, 0.2)';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'rgba(59, 130, 246, 0.1)';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Eye, {\n                        size: 12\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1770,\n                        columnNumber: 29\n                      }, this), \"Analytics\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1748,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1720,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1594,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    color: '#4b5563',\n                    fontSize: '0.95rem',\n                    lineHeight: '1.6',\n                    marginBottom: '1rem'\n                  },\n                  children: announcement.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1777,\n                  columnNumber: 23\n                }, this), announcement.attachments && announcement.attachments.length > 0 && /*#__PURE__*/_jsxDEV(ImageGallery, {\n                  images: announcement.attachments,\n                  altPrefix: announcement.title,\n                  onImageClick: index => {\n                    // Could open image viewer modal\n                    console.log('View image:', index);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1788,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'space-between',\n                    padding: '1rem',\n                    background: 'rgba(0, 0, 0, 0.02)',\n                    borderRadius: '12px',\n                    marginBottom: '1rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1.5rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => handleLikeToggle(announcement),\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        background: 'none',\n                        border: 'none',\n                        color: announcement.user_reaction ? '#ef4444' : '#6b7280',\n                        cursor: 'pointer',\n                        padding: '0.5rem',\n                        borderRadius: '8px',\n                        transition: 'all 0.2s ease',\n                        fontSize: '0.875rem',\n                        fontWeight: '500'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'none';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Heart, {\n                        size: 18,\n                        fill: announcement.user_reaction ? '#ef4444' : 'none'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1837,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: announcement.reaction_count || 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1841,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1814,\n                      columnNumber: 27\n                    }, this), announcement.allow_comments && /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setShowComments(showComments === announcement.announcement_id ? null : announcement.announcement_id),\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        background: 'none',\n                        border: 'none',\n                        color: showComments === announcement.announcement_id ? '#22c55e' : '#6b7280',\n                        cursor: 'pointer',\n                        padding: '0.5rem',\n                        borderRadius: '8px',\n                        transition: 'all 0.2s ease',\n                        fontSize: '0.875rem',\n                        fontWeight: '500'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                        e.currentTarget.style.color = '#22c55e';\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.background = 'none';\n                        e.currentTarget.style.color = showComments === announcement.announcement_id ? '#22c55e' : '#6b7280';\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n                        size: 18\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1873,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: announcement.comment_count || 0\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1874,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1846,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        color: '#6b7280',\n                        fontSize: '0.875rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Eye, {\n                        size: 18\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1886,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [announcement.view_count || 0, \" views\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1887,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1879,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1808,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1rem',\n                      fontSize: '0.75rem',\n                      color: '#6b7280'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Users, {\n                        size: 14\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1904,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [\"Posted by \", announcement.posted_by_name || 'Admin']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1905,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1899,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        padding: '0.25rem 0.5rem',\n                        background: announcement.status === 'published' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(107, 114, 128, 0.1)',\n                        color: announcement.status === 'published' ? '#22c55e' : '#6b7280',\n                        borderRadius: '6px',\n                        fontWeight: '500'\n                      },\n                      children: announcement.status\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1908,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1892,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1799,\n                  columnNumber: 23\n                }, this), showComments === announcement.announcement_id && announcement.allow_comments && /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    marginTop: '1rem',\n                    paddingTop: '1rem',\n                    borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(AdminCommentSection, {\n                    announcementId: announcement.announcement_id,\n                    allowComments: announcement.allow_comments,\n                    currentUserType: \"admin\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1929,\n                    columnNumber: 27\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1924,\n                  columnNumber: 25\n                }, this)]\n              }, `announcement-${announcement.announcement_id}`, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1542,\n                columnNumber: 21\n              }, this))\n            }, void 0, false)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1306,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1064,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 859,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 630,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 611,\n    columnNumber: 5\n  }, this);\n};\n_s2(AdminNewsfeed, \"trfHrBMIz1tfriPWusYN+ZEhOVs=\", false, function () {\n  return [useNavigate, useCategories];\n});\n_c3 = AdminNewsfeed;\nexport default AdminNewsfeed;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ImageDisplay\");\n$RefreshReg$(_c2, \"ImageGallery\");\n$RefreshReg$(_c3, \"AdminNewsfeed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "announcementService", "useCategories", "AdminCommentSection", "FacebookImageGallery", "getImageUrl", "API_BASE_URL", "Newspaper", "Search", "<PERSON>n", "Calendar", "MessageSquare", "Heart", "Eye", "Edit", "Users", "LayoutDashboard", "BookOpen", "PartyPopper", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clock", "Trophy", "Briefcase", "GraduationCap", "Flag", "Coffee", "Plane", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ImageDisplay", "imagePath", "alt", "style", "onMouseEnter", "onMouseLeave", "_s", "imageError", "setImageError", "imageLoading", "setImageLoading", "handleImageError", "handleImageLoad", "display", "alignItems", "justifyContent", "backgroundColor", "color", "fontSize", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "top", "left", "right", "bottom", "src", "opacity", "transition", "onError", "onLoad", "_c", "ImageGallery", "images", "altPrefix", "onImageClick", "length", "visibleImages", "slice", "remainingCount", "Math", "max", "getContainerStyle", "index", "total", "baseStyle", "overflow", "borderRadius", "cursor", "width", "height", "getImageStyle", "objectFit", "gap", "marginBottom", "file_path", "e", "currentTarget", "transform", "onClick", "flexDirection", "map", "image", "idx", "actualIndex", "isLast", "fontWeight", "_c2", "AdminNewsfeed", "_s2", "navigate", "getCategoryStyle", "categoryName", "styles", "background", "icon", "getHolidayTypeStyle", "holidayTypeName", "filterCategory", "setFilterCategory", "filterGradeLevel", "setFilterGradeLevel", "searchTerm", "setSearchTerm", "showComments", "setShowComments", "newComment", "setNewComment", "submittingComment", "setSubmittingComment", "announcements", "setAnnouncements", "pinnedAnnouncements", "setPinnedAnnouncements", "calendarEvents", "setCalendarEvents", "loading", "setLoading", "error", "setError", "calendarLoading", "setCalendarLoading", "calendarError", "setCalendarError", "recentStudents", "setRecentStudents", "studentLoading", "setStudentLoading", "categories", "fetchPublishedAnnouncements", "undefined", "response", "fetch", "headers", "localStorage", "getItem", "data", "json", "success", "announcementsData", "announcementsWithImages", "Promise", "all", "announcement", "imageResponse", "announcement_id", "imageData", "imgErr", "console", "warn", "pinned", "filter", "ann", "is_pinned", "err", "message", "fetchCalendarEvents", "eventsData", "events", "eventsWithImages", "event", "calendar_id", "attachments", "fetchRecentStudents", "students", "handleLikeToggle", "user_reaction", "removeReaction", "method", "body", "JSON", "stringify", "reaction_type_id", "notify_admin", "ok", "Error", "handleCommentSubmit", "announcementId", "commentText", "trim", "content", "commenter_name", "commenter_email", "prev", "filteredAnnouncements", "_announcement$categor", "_announcement$grade_l", "matchesSearch", "title", "toLowerCase", "includes", "matchesCategory", "category_id", "toString", "matchesGradeLevel", "grade_level", "filteredCalendarEvents", "description", "today", "Date", "setHours", "eventDate", "event_date", "isEventDateValid", "isPublished", "is_published", "displayAnnouncements", "displayEvents", "combinedContent", "item", "type", "sortDate", "created_at", "sort", "a", "b", "getTime", "minHeight", "backgroundImage", "pointerEvents", "zIndex", "borderBottom", "boxShadow", "padding", "min<PERSON><PERSON><PERSON>", "margin", "lineHeight", "flex", "max<PERSON><PERSON><PERSON>", "size", "placeholder", "value", "onChange", "target", "border", "outline", "onFocus", "borderColor", "onBlur", "flexShrink", "marginTop", "borderTop", "animation", "textAlign", "marginLeft", "marginRight", "<PERSON><PERSON>ilter", "student", "_student$profile", "_student$profile2", "_student$profile3", "profile", "first_name", "last_name", "student_number", "toLocaleDateString", "student_id", "holiday_type_name", "holidayStyle", "IconComponent", "textTransform", "letterSpacing", "weekday", "year", "month", "day", "eventImageUrls", "for<PERSON>ach", "img", "imageUrl", "push", "Boolean", "maxVisible", "log", "end_date", "is_alert", "flexWrap", "category_name", "categoryStyle", "fill", "reaction_count", "allow_comments", "comment_count", "view_count", "posted_by_name", "status", "paddingTop", "allowComments", "currentUserType", "_c3", "$RefreshReg$"], "sources": ["D:/capstone-e-bulletin-reactjs-proj/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/admin/AdminNewsfeed.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { announcementService } from '../../services';\nimport { useCategories } from '../../hooks/useAnnouncements';\nimport AdminCommentSection from '../../components/admin/AdminCommentSection';\nimport FacebookImageGallery from '../../components/common/FacebookImageGallery';\nimport type { AnnouncementAttachment } from '../../services/announcementService';\nimport type { CalendarEvent } from '../../types/calendar.types';\nimport { getImageUrl, API_BASE_URL } from '../../config/constants';\nimport {\n  Newspaper,\n  Search,\n  Pin,\n  Calendar,\n  MessageSquare,\n  Heart,\n  Eye,\n  Edit,\n  Users,\n  LayoutDashboard,\n  BookOpen,\n  PartyPopper,\n  AlertTriangle,\n  Clock,\n  Trophy,\n  Briefcase,\n  GraduationCap,\n  Flag,\n  Coffee,\n  Plane\n} from 'lucide-react';\n\n// Image Display Component\ninterface ImageDisplayProps {\n  imagePath: string;\n  alt: string;\n  style?: React.CSSProperties;\n  onMouseEnter?: (e: React.MouseEvent<HTMLImageElement>) => void;\n  onMouseLeave?: (e: React.MouseEvent<HTMLImageElement>) => void;\n}\n\nconst ImageDisplay: React.FC<ImageDisplayProps> = ({ \n  imagePath, \n  alt, \n  style, \n  onMouseEnter, \n  onMouseLeave \n}) => {\n  const [imageError, setImageError] = useState(false);\n  const [imageLoading, setImageLoading] = useState(true);\n\n  const handleImageError = () => {\n    setImageError(true);\n    setImageLoading(false);\n  };\n\n  const handleImageLoad = () => {\n    setImageLoading(false);\n  };\n\n  if (imageError) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f3f4f6',\n        color: '#6b7280',\n        fontSize: '0.875rem'\n      }}>\n        Image not available\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ position: 'relative', ...style }}>\n      {imageLoading && (\n        <div style={{\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          backgroundColor: '#f3f4f6',\n          color: '#6b7280'\n        }}>\n          Loading...\n        </div>\n      )}\n      <img\n        src={getImageUrl(imagePath || '') || ''}\n        alt={alt}\n        style={{\n          ...style,\n          opacity: imageLoading ? 0 : 1,\n          transition: 'opacity 0.3s ease'\n        }}\n        onError={handleImageError}\n        onLoad={handleImageLoad}\n        onMouseEnter={onMouseEnter}\n        onMouseLeave={onMouseLeave}\n      />\n    </div>\n  );\n};\n\n// Image Gallery Component\ninterface ImageGalleryProps {\n  images: AnnouncementAttachment[];\n  altPrefix: string;\n  onImageClick?: (index: number) => void;\n}\n\nconst ImageGallery: React.FC<ImageGalleryProps> = ({ images, altPrefix, onImageClick }) => {\n  if (!images || images.length === 0) return null;\n\n  const visibleImages = images.slice(0, 4);\n  const remainingCount = Math.max(0, images.length - 4);\n\n  const getContainerStyle = (index: number, total: number): React.CSSProperties => {\n    const baseStyle: React.CSSProperties = {\n      position: 'relative',\n      overflow: 'hidden',\n      borderRadius: '12px',\n      cursor: onImageClick ? 'pointer' : 'default'\n    };\n\n    if (total === 1) {\n      return { ...baseStyle, width: '100%', height: '300px' };\n    } else if (total === 2) {\n      return { ...baseStyle, width: '50%', height: '250px' };\n    } else if (total === 3) {\n      if (index === 0) {\n        return { ...baseStyle, width: '50%', height: '250px' };\n      } else {\n        return { ...baseStyle, width: '50%', height: '120px' };\n      }\n    } else {\n      if (index === 0) {\n        return { ...baseStyle, width: '50%', height: '250px' };\n      } else {\n        return { ...baseStyle, width: '33.33%', height: '120px' };\n      }\n    }\n  };\n\n  const getImageStyle = (index: number, total: number): React.CSSProperties => {\n    return {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover' as const,\n      transition: 'transform 0.3s ease'\n    };\n  };\n\n  return (\n    <div style={{\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1rem'\n    }}>\n      {/* Main image or left side */}\n      <div style={getContainerStyle(0, visibleImages.length)}>\n        <ImageDisplay\n          imagePath={visibleImages[0].file_path}\n          alt={`${altPrefix} - Image 1`}\n          style={getImageStyle(0, visibleImages.length)}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.transform = 'scale(1.02)';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.transform = 'scale(1)';\n          }}\n        />\n        {onImageClick && (\n          <div\n            style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            }}\n            onClick={() => onImageClick(0)}\n          />\n        )}\n      </div>\n\n      {/* Right side images */}\n      {visibleImages.length > 1 && (\n        <div style={{\n          display: 'flex',\n          flexDirection: visibleImages.length === 2 ? 'row' : 'column',\n          gap: '4px',\n          width: visibleImages.length === 2 ? '50%' : '50%'\n        }}>\n          {visibleImages.slice(1).map((image, idx) => {\n            const actualIndex = idx + 1;\n            const isLast = actualIndex === visibleImages.length - 1 && remainingCount > 0;\n            \n            return (\n              <div\n                key={actualIndex}\n                style={{\n                  ...getContainerStyle(actualIndex, visibleImages.length),\n                  position: 'relative'\n                }}\n              >\n                <ImageDisplay\n                  imagePath={image.file_path}\n                  alt={`${altPrefix} - Image ${actualIndex + 1}`}\n                  style={getImageStyle(actualIndex, visibleImages.length)}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'scale(1.02)';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'scale(1)';\n                  }}\n                />\n                \n                {/* Overlay for remaining images count */}\n                {isLast && remainingCount > 0 && (\n                  <div style={{\n                    position: 'absolute',\n                    top: 0,\n                    left: 0,\n                    right: 0,\n                    bottom: 0,\n                    backgroundColor: 'rgba(0, 0, 0, 0.6)',\n                    display: 'flex',\n                    alignItems: 'center',\n                    justifyContent: 'center',\n                    color: 'white',\n                    fontSize: '1.5rem',\n                    fontWeight: '600'\n                  }}>\n                    +{remainingCount}\n                  </div>\n                )}\n                \n                {onImageClick && (\n                  <div\n                    style={{\n                      position: 'absolute',\n                      top: 0,\n                      left: 0,\n                      right: 0,\n                      bottom: 0,\n                      cursor: 'pointer'\n                    }}\n                    onClick={() => onImageClick(actualIndex)}\n                  />\n                )}\n              </div>\n            );\n          })}\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Main AdminNewsfeed Component\nconst AdminNewsfeed: React.FC = () => {\n  const navigate = useNavigate();\n\n  // Category styling function\n  const getCategoryStyle = (categoryName: string) => {\n    const styles = {\n      'Academic': {\n        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n        icon: BookOpen\n      },\n      'General': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Users\n      },\n      'Events': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: PartyPopper\n      },\n      'Emergency': {\n        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n        icon: AlertTriangle\n      },\n      'Sports': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Trophy\n      },\n      'Deadlines': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Clock\n      }\n    };\n\n    return styles[categoryName as keyof typeof styles] || styles['General'];\n  };\n\n  // Holiday type styling function\n  const getHolidayTypeStyle = (holidayTypeName: string) => {\n    const styles = {\n      'National Holiday': {\n        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n        icon: Flag\n      },\n      'School Event': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: GraduationCap\n      },\n      'Academic Break': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Coffee\n      },\n      'Sports Event': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Trophy\n      },\n      'Field Trip': {\n        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n        icon: Plane\n      },\n      'Meeting': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Briefcase\n      }\n    };\n\n    return styles[holidayTypeName as keyof typeof styles] || styles['School Event'];\n  };\n\n  // Filter states\n  const [filterCategory, setFilterCategory] = useState<string>('');\n  const [filterGradeLevel, setFilterGradeLevel] = useState<string>('');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // UI states\n  const [showComments, setShowComments] = useState<number | null>(null);\n  const [newComment, setNewComment] = useState<{ [key: number]: string }>({});\n  const [submittingComment, setSubmittingComment] = useState<number | null>(null);\n\n  // Data states\n  const [announcements, setAnnouncements] = useState<any[]>([]);\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState<any[]>([]);\n  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | undefined>();\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState<string | undefined>();\n  const [recentStudents, setRecentStudents] = useState<any[]>([]);\n  const [studentLoading, setStudentLoading] = useState(false);\n\n  const { categories } = useCategories();\n\n  // Fetch published announcements with images (admin can see all)\n  const fetchPublishedAnnouncements = async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n\n      const response = await fetch(`${API_BASE_URL}/api/announcements?status=published&page=1&limit=50&sort_by=created_at&sort_order=DESC`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        const announcementsData = data.data.announcements || [];\n\n        // Fetch images for each announcement\n        const announcementsWithImages = await Promise.all(\n          announcementsData.map(async (announcement: any) => {\n            try {\n              const imageResponse = await fetch(`${API_BASE_URL}/api/announcements/${announcement.announcement_id}/images`, {\n                headers: {\n                  'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n                  'Content-Type': 'application/json'\n                }\n              });\n              const imageData = await imageResponse.json();\n\n              if (imageData.success && imageData.data) {\n                announcement.images = imageData.data.images || [];\n              } else {\n                announcement.images = [];\n              }\n            } catch (imgErr) {\n              console.warn(`Failed to fetch images for announcement ${announcement.announcement_id}:`, imgErr);\n              announcement.images = [];\n            }\n            return announcement;\n          })\n        );\n\n        setAnnouncements(announcementsWithImages);\n\n        // Separate pinned announcements\n        const pinned = announcementsWithImages.filter((ann: any) => ann.is_pinned === 1);\n        setPinnedAnnouncements(pinned);\n      } else {\n        setError('Failed to load announcements');\n      }\n    } catch (err: any) {\n      console.error('Error fetching announcements:', err);\n      setError(err.message || 'Failed to load announcements');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch calendar events\n  const fetchCalendarEvents = async () => {\n    try {\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n\n      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(\n          eventsData.map(async (event: any) => {\n            try {\n              const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`, {\n                headers: {\n                  'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n                  'Content-Type': 'application/json'\n                }\n              });\n              const imageData = await imageResponse.json();\n\n              if (imageData.success && imageData.data) {\n                event.images = imageData.data.attachments || [];\n              } else {\n                event.images = [];\n              }\n            } catch (imgErr) {\n              console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n              event.images = [];\n            }\n            return event;\n          })\n        );\n\n        setCalendarEvents(eventsWithImages);\n      } else {\n        setCalendarError('Failed to load calendar events');\n      }\n    } catch (err: any) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to load calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  };\n\n  // Fetch recent student registrations\n  const fetchRecentStudents = async () => {\n    try {\n      setStudentLoading(true);\n      const response = await fetch(`${API_BASE_URL}/api/students?page=1&limit=5&sort_by=created_at&sort_order=DESC`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json'\n        }\n      });\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        setRecentStudents(data.data.students || []);\n      }\n    } catch (err: any) {\n      console.error('Error fetching recent students:', err);\n    } finally {\n      setStudentLoading(false);\n    }\n  };\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchPublishedAnnouncements();\n    fetchCalendarEvents();\n    fetchRecentStudents();\n  }, []);\n\n  // Handle like/unlike functionality (admin perspective)\n  const handleLikeToggle = async (announcement: any) => {\n    try {\n      if (announcement.user_reaction) {\n        await announcementService.removeReaction(announcement.announcement_id);\n      } else {\n        const response = await fetch(`${API_BASE_URL}/api/announcements/${announcement.announcement_id}/reactions`, {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            reaction_type_id: 1,\n            notify_admin: false // Admin doesn't need to notify themselves\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error('Failed to add reaction');\n        }\n      }\n\n      // Refresh data\n      await fetchPublishedAnnouncements();\n    } catch (error) {\n      console.error('Error toggling like:', error);\n    }\n  };\n\n  // Handle comment submission (admin perspective)\n  const handleCommentSubmit = async (announcementId: number, commentText: string) => {\n    if (!commentText.trim()) return;\n\n    try {\n      setSubmittingComment(announcementId);\n\n      const response = await fetch(`${API_BASE_URL}/api/admin/announcements/${announcementId}/comments`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          content: commentText,\n          commenter_name: 'Admin User',\n          commenter_email: '<EMAIL>',\n          notify_admin: false\n        })\n      });\n\n      if (response.ok) {\n        setNewComment(prev => ({ ...prev, [announcementId]: '' }));\n        await fetchPublishedAnnouncements();\n      } else {\n        console.error('Failed to submit comment');\n      }\n    } catch (error) {\n      console.error('Error submitting comment:', error);\n    } finally {\n      setSubmittingComment(null);\n    }\n  };\n\n  // Filter announcements\n  const filteredAnnouncements = announcements.filter(announcement => {\n    const matchesSearch = !searchTerm ||\n      announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      announcement.content.toLowerCase().includes(searchTerm.toLowerCase());\n\n    const matchesCategory = !filterCategory ||\n      announcement.category_id?.toString() === filterCategory;\n\n    const matchesGradeLevel = !filterGradeLevel ||\n      announcement.grade_level?.toString() === filterGradeLevel;\n\n    return matchesSearch && matchesCategory && matchesGradeLevel;\n  });\n\n  // Filter calendar events with date-based filtering\n  const filteredCalendarEvents = calendarEvents.filter(event => {\n    const matchesSearch = !searchTerm ||\n      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      (event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    // Only show events that are published and on or after today's date\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    const eventDate = new Date(event.event_date);\n    eventDate.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    const isEventDateValid = eventDate >= today;\n    const isPublished = (event as any).is_published === 1;\n\n    return matchesSearch && isEventDateValid && isPublished;\n  });\n\n  // Combine and sort all content by date (most recent first)\n  const displayAnnouncements = filteredAnnouncements;\n  const displayEvents = filteredCalendarEvents;\n\n  // Create combined content array for better chronological display\n  const combinedContent = [\n    ...displayAnnouncements.map(item => ({ ...item, type: 'announcement', sortDate: new Date(item.created_at) })),\n    ...displayEvents.map(item => ({ ...item, type: 'event', sortDate: new Date(item.event_date) }))\n  ].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());\n\n  return (\n    <div style={{\n      minHeight: '100vh',\n      background: 'linear-gradient(135deg, #f8fdf8 0%, #fffef7 100%)',\n      position: 'relative'\n    }}>\n      {/* Background Pattern */}\n      <div style={{\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n        backgroundImage: `\n          radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.03) 0%, transparent 50%),\n          radial-gradient(circle at 75% 75%, rgba(250, 204, 21, 0.03) 0%, transparent 50%)\n        `,\n        pointerEvents: 'none'\n      }} />\n\n      <div style={{ position: 'relative', zIndex: 1 }}>\n        {/* Modern Admin Header */}\n        <header style={{\n          background: 'white',\n          borderBottom: '1px solid #e5e7eb',\n          position: 'sticky',\n          top: 0,\n          zIndex: 100,\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n        }}>\n          <div style={{\n            padding: '0 2rem',\n            height: '72px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          }}>\n            {/* Left Section: Logo + Page Title */}\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1.5rem',\n              minWidth: '300px'\n            }}>\n              <img\n                src=\"/logo/vcba1.png\"\n                alt=\"VCBA Logo\"\n                style={{\n                  width: '48px',\n                  height: '48px',\n                  objectFit: 'contain'\n                }}\n              />\n              <div>\n                <h1 style={{\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827',\n                  lineHeight: '1.2'\n                }}>\n                  VCBA E-Bulletin Board\n                </h1>\n                <p style={{\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280',\n                  lineHeight: '1.2'\n                }}>\n                  Admin Newsfeed\n                </p>\n              </div>\n            </div>\n\n            {/* Center Section: Search */}\n            <div style={{\n              flex: 1,\n              maxWidth: '500px',\n              margin: '0 2rem'\n            }}>\n              <div style={{ position: 'relative' }}>\n                <Search\n                  size={20}\n                  style={{\n                    position: 'absolute',\n                    left: '1rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: '#9ca3af'\n                  }}\n                />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search announcements and events...\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  style={{\n                    width: '100%',\n                    height: '44px',\n                    padding: '0 1rem 0 3rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '12px',\n                    background: '#f9fafb',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onFocus={(e) => {\n                    e.currentTarget.style.borderColor = '#22c55e';\n                    e.currentTarget.style.background = 'white';\n                    e.currentTarget.style.boxShadow = '0 0 0 3px rgba(34, 197, 94, 0.1)';\n                  }}\n                  onBlur={(e) => {\n                    e.currentTarget.style.borderColor = '#d1d5db';\n                    e.currentTarget.style.background = '#f9fafb';\n                    e.currentTarget.style.boxShadow = 'none';\n                  }}\n                />\n              </div>\n            </div>\n\n            {/* Right Section: Navigation + Filters */}\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              minWidth: '400px',\n              justifyContent: 'flex-end'\n            }}>\n              \n              {/* Filters Group */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.5rem',\n                background: '#f9fafb',\n                borderRadius: '12px',\n                border: '1px solid #e5e7eb'\n              }}>\n                <select\n                  value={filterCategory}\n                  onChange={(e) => setFilterCategory(e.target.value)}\n                  style={{\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '110px'\n                  }}\n                >\n                  <option value=\"\">All Categories</option>\n                  <option value=\"1\">Academic</option>\n                  <option value=\"2\">Events</option>\n                  <option value=\"3\">Administrative</option>\n                </select>\n\n                <select\n                  value={filterGradeLevel}\n                  onChange={(e) => setFilterGradeLevel(e.target.value)}\n                  style={{\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '100px'\n                  }}\n                >\n                  <option value=\"\">All Grades</option>\n                  <option value=\"11\">Grade 11</option>\n                  <option value=\"12\">Grade 12</option>\n                </select>\n\n                {(searchTerm || filterCategory || filterGradeLevel) && (\n                  <button\n                    onClick={() => {\n                      setSearchTerm('');\n                      setFilterCategory('');\n                      setFilterGradeLevel('');\n                    }}\n                    style={{\n                      padding: '0.5rem 0.75rem',\n                      border: 'none',\n                      borderRadius: '8px',\n                      background: '#ef4444',\n                      color: 'white',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.background = '#dc2626';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.background = '#ef4444';\n                    }}\n                  >\n                    Clear\n                  </button>\n                )}\n              </div>\n\n              {/* Back to Dashboard Button */}\n              <button\n                onClick={() => navigate('/admin/dashboard')}\n                style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  padding: '0.75rem 1rem',\n                  background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                  border: 'none',\n                  borderRadius: '12px',\n                  color: 'white',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease',\n                  boxShadow: '0 2px 8px rgba(34, 197, 94, 0.2)'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = '0 2px 8px rgba(34, 197, 94, 0.2)';\n                }}\n              >\n                <LayoutDashboard size={16} />\n                Dashboard\n              </button>\n            </div>\n          </div>\n        </header>\n\n\n\n        {/* Main Content Layout */}\n        <div style={{\n          padding: '2rem',\n          display: 'flex',\n          gap: '2rem',\n          alignItems: 'flex-start'\n        }}>\n          {/* Left Sidebar: Pinned Posts */}\n          <div style={{\n            width: '320px',\n            flexShrink: 0\n          }}>\n            <div style={{\n              background: 'white',\n              borderRadius: '16px',\n              border: '1px solid #e5e7eb',\n              overflow: 'hidden',\n              position: 'sticky',\n              top: '100px'\n            }}>\n              {/* Pinned Posts Header */}\n              <div style={{\n                padding: '1.5rem 1.5rem 1rem',\n                borderBottom: '1px solid #f3f4f6'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.75rem',\n                  marginBottom: '0.5rem'\n                }}>\n                  <Pin size={20} style={{ color: '#22c55e' }} />\n                  <h3 style={{\n                    margin: 0,\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: '#111827'\n                  }}>\n                    Pinned Posts\n                  </h3>\n                </div>\n                <p style={{\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                }}>\n                  Important announcements and updates\n                </p>\n              </div>\n\n              {/* Pinned Posts List */}\n              <div style={{ padding: '1rem' }}>\n                {/* Sample Pinned Post */}\n                <div style={{\n                  padding: '1rem',\n                  background: '#f8fafc',\n                  borderRadius: '12px',\n                  border: '1px solid #e2e8f0',\n                  marginBottom: '1rem',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.background = '#f1f5f9';\n                  e.currentTarget.style.borderColor = '#22c55e';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.background = '#f8fafc';\n                  e.currentTarget.style.borderColor = '#e2e8f0';\n                }}>\n                  <div style={{\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    gap: '0.75rem'\n                  }}>\n                    <div style={{\n                      width: '8px',\n                      height: '8px',\n                      background: '#22c55e',\n                      borderRadius: '50%',\n                      marginTop: '0.5rem',\n                      flexShrink: 0\n                    }} />\n                    <div style={{ flex: 1 }}>\n                      <h4 style={{\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '0.875rem',\n                        fontWeight: '600',\n                        color: '#111827',\n                        lineHeight: '1.4'\n                      }}>\n                        Important: New Academic Calendar Released\n                      </h4>\n                      <p style={{\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '0.8rem',\n                        color: '#6b7280',\n                        lineHeight: '1.4'\n                      }}>\n                        Please review the updated academic calendar for the upcoming semester...\n                      </p>\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        fontSize: '0.75rem',\n                        color: '#9ca3af'\n                      }}>\n                        <Calendar size={12} />\n                        <span>2 days ago</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Another Sample Pinned Post */}\n                <div style={{\n                  padding: '1rem',\n                  background: '#f8fafc',\n                  borderRadius: '12px',\n                  border: '1px solid #e2e8f0',\n                  marginBottom: '1rem',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.background = '#f1f5f9';\n                  e.currentTarget.style.borderColor = '#22c55e';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.background = '#f8fafc';\n                  e.currentTarget.style.borderColor = '#e2e8f0';\n                }}>\n                  <div style={{\n                    display: 'flex',\n                    alignItems: 'flex-start',\n                    gap: '0.75rem'\n                  }}>\n                    <div style={{\n                      width: '8px',\n                      height: '8px',\n                      background: '#ef4444',\n                      borderRadius: '50%',\n                      marginTop: '0.5rem',\n                      flexShrink: 0\n                    }} />\n                    <div style={{ flex: 1 }}>\n                      <h4 style={{\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '0.875rem',\n                        fontWeight: '600',\n                        color: '#111827',\n                        lineHeight: '1.4'\n                      }}>\n                        Urgent: System Maintenance Notice\n                      </h4>\n                      <p style={{\n                        margin: '0 0 0.5rem 0',\n                        fontSize: '0.8rem',\n                        color: '#6b7280',\n                        lineHeight: '1.4'\n                      }}>\n                        Scheduled maintenance will occur this weekend. Please save your work...\n                      </p>\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.5rem',\n                        fontSize: '0.75rem',\n                        color: '#9ca3af'\n                      }}>\n                        <Calendar size={12} />\n                        <span>1 week ago</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n\n                {/* View All Link */}\n                <button style={{\n                  width: '100%',\n                  padding: '0.75rem',\n                  border: '1px solid #e5e7eb',\n                  borderRadius: '8px',\n                  background: 'white',\n                  color: '#22c55e',\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.background = '#f0fdf4';\n                  e.currentTarget.style.borderColor = '#22c55e';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.background = 'white';\n                  e.currentTarget.style.borderColor = '#e5e7eb';\n                }}>\n                  View All Pinned Posts\n                </button>\n              </div>\n            </div>\n          </div>\n\n          {/* Right Content: Main Feed */}\n          <div style={{ flex: 1, minWidth: 0 }}>\n          {/* Loading State */}\n          {(loading || calendarLoading) && (\n            <div style={{\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              minHeight: '400px'\n            }}>\n              <div style={{\n                display: 'flex',\n                flexDirection: 'column',\n                alignItems: 'center',\n                gap: '1rem'\n              }}>\n                <div style={{\n                  width: '3rem',\n                  height: '3rem',\n                  border: '4px solid rgba(34, 197, 94, 0.2)',\n                  borderTop: '4px solid #22c55e',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }}></div>\n                <p style={{\n                  color: '#6b7280',\n                  fontSize: '1rem',\n                  fontWeight: '500'\n                }}>\n                  Loading content...\n                </p>\n              </div>\n            </div>\n          )}\n\n          {/* Error State */}\n          {(error || calendarError) && !loading && !calendarLoading && (\n            <div style={{\n              padding: '2rem',\n              background: 'rgba(239, 68, 68, 0.1)',\n              border: '1px solid rgba(239, 68, 68, 0.2)',\n              borderRadius: '16px',\n              textAlign: 'center'\n            }}>\n              <div style={{\n                width: '4rem',\n                height: '4rem',\n                background: 'rgba(239, 68, 68, 0.1)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 1rem'\n              }}>\n                <MessageSquare size={24} color=\"#ef4444\" />\n              </div>\n              <h3 style={{\n                color: '#ef4444',\n                margin: '0 0 0.5rem 0',\n                fontSize: '1.25rem',\n                fontWeight: '600'\n              }}>\n                Error Loading Content\n              </h3>\n              <p style={{\n                color: '#6b7280',\n                margin: '0 0 1.5rem 0',\n                fontSize: '1rem'\n              }}>\n                {error || calendarError}\n              </p>\n              <button\n                onClick={() => {\n                  fetchPublishedAnnouncements();\n                  fetchCalendarEvents();\n                }}\n                style={{\n                  background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                  color: 'white',\n                  border: 'none',\n                  borderRadius: '12px',\n                  padding: '0.75rem 1.5rem',\n                  fontSize: '0.875rem',\n                  fontWeight: '600',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.transform = 'translateY(-1px)';\n                  e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.transform = 'translateY(0)';\n                  e.currentTarget.style.boxShadow = 'none';\n                }}\n              >\n                Try Again\n              </button>\n            </div>\n          )}\n\n          {/* Empty State */}\n          {!loading && !calendarLoading && !error && !calendarError &&\n           displayAnnouncements.length === 0 && displayEvents.length === 0 && (\n            <div style={{\n              padding: '4rem 2rem',\n              textAlign: 'center'\n            }}>\n              <div style={{\n                width: '5rem',\n                height: '5rem',\n                background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                borderRadius: '50%',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                margin: '0 auto 2rem'\n              }}>\n                <Newspaper size={32} color=\"white\" />\n              </div>\n              <h3 style={{\n                color: '#374151',\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '600'\n              }}>\n                No Content Available\n              </h3>\n              <p style={{\n                color: '#6b7280',\n                margin: '0 0 2rem 0',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                maxWidth: '500px',\n                marginLeft: 'auto',\n                marginRight: 'auto'\n              }}>\n                {searchTerm || filterCategory || filterGradeLevel\n                  ? 'No content matches your current filters. Try adjusting your search criteria.'\n                  : 'There are no published announcements or events at the moment. Check back later for updates.'\n                }\n              </p>\n              {(searchTerm || filterCategory || filterGradeLevel) && (\n                <button\n                  onClick={() => {\n                    setSearchTerm('');\n                    setFilterCategory('');\n                    setFilterGradeLevel('');\n                  }}\n                  style={{\n                    background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                    color: 'white',\n                    border: 'none',\n                    borderRadius: '12px',\n                    padding: '0.75rem 1.5rem',\n                    fontSize: '0.875rem',\n                    fontWeight: '600',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'translateY(-1px)';\n                    e.currentTarget.style.boxShadow = '0 4px 12px rgba(34, 197, 94, 0.3)';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = 'none';\n                  }}\n                >\n                  Clear Filters\n                </button>\n              )}\n            </div>\n          )}\n\n          {/* Recent Students Section (Admin Only) */}\n          {!studentLoading && recentStudents.length > 0 && (\n            <div style={{\n              background: 'rgba(255, 255, 255, 0.95)',\n              borderRadius: '16px',\n              padding: '1.5rem',\n              border: '1px solid rgba(0, 0, 0, 0.1)',\n              backdropFilter: 'blur(10px)',\n              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n              marginBottom: '1.5rem'\n            }}>\n              <h3 style={{\n                fontSize: '1.25rem',\n                fontWeight: '600',\n                color: '#2d5016',\n                margin: '0 0 1rem 0',\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem'\n              }}>\n                👥 Recent Student Registrations\n              </h3>\n              <div style={{\n                display: 'grid',\n                gap: '0.75rem'\n              }}>\n                {recentStudents.slice(0, 3).map((student: any) => (\n                  <div\n                    key={student.student_id}\n                    style={{\n                      display: 'flex',\n                      justifyContent: 'space-between',\n                      alignItems: 'center',\n                      padding: '0.75rem',\n                      backgroundColor: '#f8fdf8',\n                      borderRadius: '8px',\n                      border: '1px solid #e8f5e8'\n                    }}\n                  >\n                    <div>\n                      <div style={{\n                        fontWeight: '500',\n                        color: '#2d5016',\n                        fontSize: '0.875rem'\n                      }}>\n                        {student.profile?.first_name} {student.profile?.last_name}\n                      </div>\n                      <div style={{\n                        fontSize: '0.75rem',\n                        color: '#6b7280'\n                      }}>\n                        Grade {student.profile?.grade_level} • {student.student_number}\n                      </div>\n                    </div>\n                    <div style={{\n                      fontSize: '0.75rem',\n                      color: '#6b7280'\n                    }}>\n                      {new Date(student.created_at).toLocaleDateString()}\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {/* Content Feed */}\n          {!loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && (\n            <div style={{\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem'\n            }}>\n              {/* Calendar Events */}\n              {displayEvents.length > 0 && (\n                <>\n                  {displayEvents.map(event => (\n                    <div\n                      key={`event-${event.calendar_id}`}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Event Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'flex-start',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        <div style={{\n                          width: '48px',\n                          height: '48px',\n                          background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',\n                          borderRadius: '12px',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          flexShrink: 0\n                        }}>\n                          <Calendar size={24} color=\"white\" />\n                        </div>\n\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            marginBottom: '0.5rem'\n                          }}>\n                            {(() => {\n                              const holidayTypeName = event.holiday_type_name || 'School Event';\n                              const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                              const IconComponent = holidayStyle.icon;\n\n                              return (\n                                <span style={{\n                                  background: holidayStyle.background,\n                                  color: 'white',\n                                  fontSize: '0.75rem',\n                                  fontWeight: '600',\n                                  padding: '0.25rem 0.75rem',\n                                  borderRadius: '20px',\n                                  textTransform: 'uppercase',\n                                  letterSpacing: '0.5px',\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: '0.25rem'\n                                }}>\n                                  <IconComponent size={12} color=\"white\" />\n                                  {holidayTypeName}\n                                </span>\n                              );\n                            })()}\n\n                            <div style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem',\n                              color: '#6b7280',\n                              fontSize: '0.875rem'\n                            }}>\n                              <Calendar size={14} />\n                              {new Date(event.event_date).toLocaleDateString('en-US', {\n                                weekday: 'long',\n                                year: 'numeric',\n                                month: 'long',\n                                day: 'numeric'\n                              })}\n                            </div>\n                          </div>\n\n                          <h3 style={{\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '1.25rem',\n                            fontWeight: '700',\n                            color: '#1f2937',\n                            lineHeight: '1.3'\n                          }}>\n                            {event.title}\n                          </h3>\n                        </div>\n\n                        {/* Admin Event Actions */}\n                        <div style={{ display: 'flex', gap: '0.5rem' }}>\n                          <button\n                            onClick={() => navigate(`/admin/calendar?event=${event.calendar_id}`)}\n                            style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem',\n                              padding: '0.5rem 0.75rem',\n                              background: 'rgba(59, 130, 246, 0.1)',\n                              border: '1px solid rgba(59, 130, 246, 0.2)',\n                              borderRadius: '8px',\n                              color: '#3b82f6',\n                              fontSize: '0.75rem',\n                              fontWeight: '500',\n                              cursor: 'pointer',\n                              transition: 'all 0.2s ease'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = 'rgba(59, 130, 246, 0.2)';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'rgba(59, 130, 246, 0.1)';\n                            }}\n                          >\n                            <Edit size={12} />\n                            Edit\n                          </button>\n                        </div>\n                      </div>\n\n                      {/* Event Content */}\n                      {event.description && (\n                        <div style={{\n                          color: '#4b5563',\n                          fontSize: '0.95rem',\n                          lineHeight: '1.6',\n                          marginBottom: '1rem'\n                        }}>\n                          {event.description}\n                        </div>\n                      )}\n\n                      {/* Event Images */}\n                      {(() => {\n                        // Get event images if they exist\n                        const eventImageUrls: string[] = [];\n\n                        if ((event as any).images && (event as any).images.length > 0) {\n                          (event as any).images.forEach((img: any) => {\n                            if (img.file_path) {\n                              // Convert file_path to full URL\n                              const imageUrl = getImageUrl(img.file_path);\n                              if (imageUrl) {\n                                eventImageUrls.push(imageUrl);\n                              }\n                            }\n                          });\n                        }\n\n                        return eventImageUrls.length > 0 ? (\n                          <div style={{ marginBottom: '1rem' }}>\n                            <FacebookImageGallery\n                              images={eventImageUrls.filter(Boolean) as string[]}\n                              altPrefix={event.title}\n                              maxVisible={4}\n                              onImageClick={(index) => {\n                                console.log(`Clicked image ${index + 1} for event: ${event.title}`);\n                              }}\n                            />\n                          </div>\n                        ) : null;\n                      })()}\n\n                      {/* Event Details */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1.5rem',\n                        padding: '1rem',\n                        background: 'rgba(59, 130, 246, 0.05)',\n                        borderRadius: '12px',\n                        fontSize: '0.875rem'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          color: '#6b7280'\n                        }}>\n                          <Calendar size={16} />\n                          <span>\n                            {event.end_date && event.end_date !== event.event_date\n                              ? `${new Date(event.event_date).toLocaleDateString()} - ${new Date(event.end_date).toLocaleDateString()}`\n                              : new Date(event.event_date).toLocaleDateString()\n                            }\n                          </span>\n                        </div>\n\n                        {event.holiday_type_name && (\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            color: '#6b7280'\n                          }}>\n                            <span style={{\n                              padding: '0.25rem 0.5rem',\n                              background: 'rgba(59, 130, 246, 0.1)',\n                              borderRadius: '6px',\n                              fontSize: '0.75rem',\n                              fontWeight: '500'\n                            }}>\n                              {event.holiday_type_name}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </>\n              )}\n\n              {/* Announcements */}\n              {displayAnnouncements.length > 0 && (\n                <>\n                  {displayAnnouncements.map(announcement => (\n                    <div\n                      key={`announcement-${announcement.announcement_id}`}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: announcement.is_pinned\n                          ? '2px solid rgba(250, 204, 21, 0.3)'\n                          : '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: announcement.is_pinned\n                          ? '0 4px 20px rgba(250, 204, 21, 0.15)'\n                          : '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease',\n                        position: 'relative'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = announcement.is_pinned\n                          ? '0 8px 30px rgba(250, 204, 21, 0.25)'\n                          : '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = announcement.is_pinned\n                          ? '0 4px 20px rgba(250, 204, 21, 0.15)'\n                          : '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Pinned Badge */}\n                      {announcement.is_pinned && (\n                        <div style={{\n                          position: 'absolute',\n                          top: '-8px',\n                          right: '1rem',\n                          background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                          color: 'white',\n                          padding: '0.25rem 0.75rem',\n                          borderRadius: '12px',\n                          fontSize: '0.75rem',\n                          fontWeight: '600',\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.25rem',\n                          boxShadow: '0 2px 8px rgba(250, 204, 21, 0.3)'\n                        }}>\n                          <Pin size={12} />\n                          Pinned\n                        </div>\n                      )}\n\n                      {/* Announcement Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'flex-start',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        <div style={{\n                          width: '48px',\n                          height: '48px',\n                          background: announcement.is_alert\n                            ? 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)'\n                            : 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                          borderRadius: '12px',\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center',\n                          flexShrink: 0\n                        }}>\n                          <Newspaper size={24} color=\"white\" />\n                        </div>\n\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.75rem',\n                            marginBottom: '0.5rem',\n                            flexWrap: 'wrap'\n                          }}>\n                            {(() => {\n                              if (announcement.is_alert) {\n                                return (\n                                  <span style={{\n                                    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                                    color: 'white',\n                                    fontSize: '0.75rem',\n                                    fontWeight: '600',\n                                    padding: '0.25rem 0.75rem',\n                                    borderRadius: '20px',\n                                    textTransform: 'uppercase',\n                                    letterSpacing: '0.5px',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.25rem'\n                                  }}>\n                                    <AlertTriangle size={12} color=\"white\" />\n                                    Alert\n                                  </span>\n                                );\n                              } else {\n                                const categoryName = announcement.category_name || 'General';\n                                const categoryStyle = getCategoryStyle(categoryName);\n                                const IconComponent = categoryStyle.icon;\n\n                                return (\n                                  <span style={{\n                                    background: categoryStyle.background,\n                                    color: 'white',\n                                    fontSize: '0.75rem',\n                                    fontWeight: '600',\n                                    padding: '0.25rem 0.75rem',\n                                    borderRadius: '20px',\n                                    textTransform: 'uppercase',\n                                    letterSpacing: '0.5px',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.25rem'\n                                  }}>\n                                    <IconComponent size={12} color=\"white\" />\n                                    {categoryName}\n                                  </span>\n                                );\n                              }\n                            })()}\n\n                            {announcement.category_name && (\n                              <span style={{\n                                background: 'rgba(107, 114, 128, 0.1)',\n                                color: '#6b7280',\n                                fontSize: '0.75rem',\n                                fontWeight: '500',\n                                padding: '0.25rem 0.75rem',\n                                borderRadius: '20px'\n                              }}>\n                                {announcement.category_name}\n                              </span>\n                            )}\n\n                            {announcement.grade_level && (\n                              <span style={{\n                                background: 'rgba(59, 130, 246, 0.1)',\n                                color: '#3b82f6',\n                                fontSize: '0.75rem',\n                                fontWeight: '500',\n                                padding: '0.25rem 0.75rem',\n                                borderRadius: '20px'\n                              }}>\n                                Grade {announcement.grade_level}\n                              </span>\n                            )}\n\n                            <div style={{\n                              color: '#6b7280',\n                              fontSize: '0.875rem'\n                            }}>\n                              {new Date(announcement.created_at).toLocaleDateString('en-US', {\n                                weekday: 'short',\n                                year: 'numeric',\n                                month: 'short',\n                                day: 'numeric'\n                              })}\n                            </div>\n                          </div>\n\n                          <h3 style={{\n                            margin: '0 0 0.5rem 0',\n                            fontSize: '1.25rem',\n                            fontWeight: '700',\n                            color: '#1f2937',\n                            lineHeight: '1.3'\n                          }}>\n                            {announcement.title}\n                          </h3>\n                        </div>\n\n                        {/* Admin Announcement Actions */}\n                        <div style={{ display: 'flex', gap: '0.5rem' }}>\n                          <button\n                            onClick={() => navigate(`/admin/posts?edit=${announcement.announcement_id}`)}\n                            style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem',\n                              padding: '0.5rem 0.75rem',\n                              background: 'rgba(34, 197, 94, 0.1)',\n                              border: '1px solid rgba(34, 197, 94, 0.2)',\n                              borderRadius: '8px',\n                              color: '#22c55e',\n                              fontSize: '0.75rem',\n                              fontWeight: '500',\n                              cursor: 'pointer',\n                              transition: 'all 0.2s ease'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = 'rgba(34, 197, 94, 0.2)';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'rgba(34, 197, 94, 0.1)';\n                            }}\n                          >\n                            <Edit size={12} />\n                            Edit\n                          </button>\n\n                          <button\n                            style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem',\n                              padding: '0.5rem 0.75rem',\n                              background: 'rgba(59, 130, 246, 0.1)',\n                              border: '1px solid rgba(59, 130, 246, 0.2)',\n                              borderRadius: '8px',\n                              color: '#3b82f6',\n                              fontSize: '0.75rem',\n                              fontWeight: '500',\n                              cursor: 'pointer',\n                              transition: 'all 0.2s ease'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = 'rgba(59, 130, 246, 0.2)';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'rgba(59, 130, 246, 0.1)';\n                            }}\n                          >\n                            <Eye size={12} />\n                            Analytics\n                          </button>\n                        </div>\n                      </div>\n\n                      {/* Announcement Content */}\n                      <div style={{\n                        color: '#4b5563',\n                        fontSize: '0.95rem',\n                        lineHeight: '1.6',\n                        marginBottom: '1rem'\n                      }}>\n                        {announcement.content}\n                      </div>\n\n                      {/* Images */}\n                      {announcement.attachments && announcement.attachments.length > 0 && (\n                        <ImageGallery\n                          images={announcement.attachments}\n                          altPrefix={announcement.title}\n                          onImageClick={(index) => {\n                            // Could open image viewer modal\n                            console.log('View image:', index);\n                          }}\n                        />\n                      )}\n\n                      {/* Announcement Stats & Actions */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        padding: '1rem',\n                        background: 'rgba(0, 0, 0, 0.02)',\n                        borderRadius: '12px',\n                        marginBottom: '1rem'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1.5rem'\n                        }}>\n                          {/* Like Button */}\n                          <button\n                            onClick={() => handleLikeToggle(announcement)}\n                            style={{\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.5rem',\n                              background: 'none',\n                              border: 'none',\n                              color: announcement.user_reaction ? '#ef4444' : '#6b7280',\n                              cursor: 'pointer',\n                              padding: '0.5rem',\n                              borderRadius: '8px',\n                              transition: 'all 0.2s ease',\n                              fontSize: '0.875rem',\n                              fontWeight: '500'\n                            }}\n                            onMouseEnter={(e) => {\n                              e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                            }}\n                            onMouseLeave={(e) => {\n                              e.currentTarget.style.background = 'none';\n                            }}\n                          >\n                            <Heart\n                              size={18}\n                              fill={announcement.user_reaction ? '#ef4444' : 'none'}\n                            />\n                            <span>{announcement.reaction_count || 0}</span>\n                          </button>\n\n                          {/* Comments Button */}\n                          {announcement.allow_comments && (\n                            <button\n                              onClick={() => setShowComments(\n                                showComments === announcement.announcement_id ? null : announcement.announcement_id\n                              )}\n                              style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                background: 'none',\n                                border: 'none',\n                                color: showComments === announcement.announcement_id ? '#22c55e' : '#6b7280',\n                                cursor: 'pointer',\n                                padding: '0.5rem',\n                                borderRadius: '8px',\n                                transition: 'all 0.2s ease',\n                                fontSize: '0.875rem',\n                                fontWeight: '500'\n                              }}\n                              onMouseEnter={(e) => {\n                                e.currentTarget.style.background = 'rgba(0, 0, 0, 0.05)';\n                                e.currentTarget.style.color = '#22c55e';\n                              }}\n                              onMouseLeave={(e) => {\n                                e.currentTarget.style.background = 'none';\n                                e.currentTarget.style.color = showComments === announcement.announcement_id ? '#22c55e' : '#6b7280';\n                              }}\n                            >\n                              <MessageSquare size={18} />\n                              <span>{announcement.comment_count || 0}</span>\n                            </button>\n                          )}\n\n                          {/* Views Count */}\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            color: '#6b7280',\n                            fontSize: '0.875rem'\n                          }}>\n                            <Eye size={18} />\n                            <span>{announcement.view_count || 0} views</span>\n                          </div>\n                        </div>\n\n                        {/* Admin Stats */}\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem',\n                          fontSize: '0.75rem',\n                          color: '#6b7280'\n                        }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.25rem'\n                          }}>\n                            <Users size={14} />\n                            <span>Posted by {announcement.posted_by_name || 'Admin'}</span>\n                          </div>\n\n                          <div style={{\n                            padding: '0.25rem 0.5rem',\n                            background: announcement.status === 'published'\n                              ? 'rgba(34, 197, 94, 0.1)'\n                              : 'rgba(107, 114, 128, 0.1)',\n                            color: announcement.status === 'published' ? '#22c55e' : '#6b7280',\n                            borderRadius: '6px',\n                            fontWeight: '500'\n                          }}>\n                            {announcement.status}\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Comments Section */}\n                      {showComments === announcement.announcement_id && announcement.allow_comments && (\n                        <div style={{\n                          marginTop: '1rem',\n                          paddingTop: '1rem',\n                          borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                        }}>\n                          <AdminCommentSection\n                            announcementId={announcement.announcement_id}\n                            allowComments={announcement.allow_comments}\n                            currentUserType=\"admin\"\n                          />\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </>\n              )}\n            </div>\n          )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AdminNewsfeed;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,aAAa,QAAQ,8BAA8B;AAC5D,OAAOC,mBAAmB,MAAM,4CAA4C;AAC5E,OAAOC,oBAAoB,MAAM,8CAA8C;AAG/E,SAASC,WAAW,EAAEC,YAAY,QAAQ,wBAAwB;AAClE,SACEC,SAAS,EACTC,MAAM,EACNC,GAAG,EACHC,QAAQ,EACRC,aAAa,EACbC,KAAK,EACLC,GAAG,EACHC,IAAI,EACJC,KAAK,EACLC,eAAe,EACfC,QAAQ,EACRC,WAAW,EACXC,aAAa,EACbC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,aAAa,EACbC,IAAI,EACJC,MAAM,EACNC,KAAK,QACA,cAAc;;AAErB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AASA,MAAMC,YAAyC,GAAGA,CAAC;EACjDC,SAAS;EACTC,GAAG;EACHC,KAAK;EACLC,YAAY;EACZC;AACF,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAAC0C,YAAY,EAAEC,eAAe,CAAC,GAAG3C,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAM4C,gBAAgB,GAAGA,CAAA,KAAM;IAC7BH,aAAa,CAAC,IAAI,CAAC;IACnBE,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5BF,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;EAED,IAAIH,UAAU,EAAE;IACd,oBACEV,OAAA;MAAKM,KAAK,EAAE;QACV,GAAGA,KAAK;QACRU,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE;MACZ,CAAE;MAAAC,QAAA,EAAC;IAEH;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEV;EAEA,oBACE1B,OAAA;IAAKM,KAAK,EAAE;MAAEqB,QAAQ,EAAE,UAAU;MAAE,GAAGrB;IAAM,CAAE;IAAAgB,QAAA,GAC5CV,YAAY,iBACXZ,OAAA;MAAKM,KAAK,EAAE;QACVqB,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACTf,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE;MACT,CAAE;MAAAE,QAAA,EAAC;IAEH;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CACN,eACD1B,OAAA;MACEgC,GAAG,EAAEvD,WAAW,CAAC2B,SAAS,IAAI,EAAE,CAAC,IAAI,EAAG;MACxCC,GAAG,EAAEA,GAAI;MACTC,KAAK,EAAE;QACL,GAAGA,KAAK;QACR2B,OAAO,EAAErB,YAAY,GAAG,CAAC,GAAG,CAAC;QAC7BsB,UAAU,EAAE;MACd,CAAE;MACFC,OAAO,EAAErB,gBAAiB;MAC1BsB,MAAM,EAAErB,eAAgB;MACxBR,YAAY,EAAEA,YAAa;MAC3BC,YAAY,EAAEA;IAAa;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;;AAED;AAAAjB,EAAA,CAtEMN,YAAyC;AAAAkC,EAAA,GAAzClC,YAAyC;AA6E/C,MAAMmC,YAAyC,GAAGA,CAAC;EAAEC,MAAM;EAAEC,SAAS;EAAEC;AAAa,CAAC,KAAK;EACzF,IAAI,CAACF,MAAM,IAAIA,MAAM,CAACG,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAE/C,MAAMC,aAAa,GAAGJ,MAAM,CAACK,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;EACxC,MAAMC,cAAc,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAER,MAAM,CAACG,MAAM,GAAG,CAAC,CAAC;EAErD,MAAMM,iBAAiB,GAAGA,CAACC,KAAa,EAAEC,KAAa,KAA0B;IAC/E,MAAMC,SAA8B,GAAG;MACrCxB,QAAQ,EAAE,UAAU;MACpByB,QAAQ,EAAE,QAAQ;MAClBC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAEb,YAAY,GAAG,SAAS,GAAG;IACrC,CAAC;IAED,IAAIS,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QAAE,GAAGC,SAAS;QAAEI,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAQ,CAAC;IACzD,CAAC,MAAM,IAAIN,KAAK,KAAK,CAAC,EAAE;MACtB,OAAO;QAAE,GAAGC,SAAS;QAAEI,KAAK,EAAE,KAAK;QAAEC,MAAM,EAAE;MAAQ,CAAC;IACxD,CAAC,MAAM,IAAIN,KAAK,KAAK,CAAC,EAAE;MACtB,IAAID,KAAK,KAAK,CAAC,EAAE;QACf,OAAO;UAAE,GAAGE,SAAS;UAAEI,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAC;MACxD,CAAC,MAAM;QACL,OAAO;UAAE,GAAGL,SAAS;UAAEI,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAC;MACxD;IACF,CAAC,MAAM;MACL,IAAIP,KAAK,KAAK,CAAC,EAAE;QACf,OAAO;UAAE,GAAGE,SAAS;UAAEI,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE;QAAQ,CAAC;MACxD,CAAC,MAAM;QACL,OAAO;UAAE,GAAGL,SAAS;UAAEI,KAAK,EAAE,QAAQ;UAAEC,MAAM,EAAE;QAAQ,CAAC;MAC3D;IACF;EACF,CAAC;EAED,MAAMC,aAAa,GAAGA,CAACR,KAAa,EAAEC,KAAa,KAA0B;IAC3E,OAAO;MACLK,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdE,SAAS,EAAE,OAAgB;MAC3BxB,UAAU,EAAE;IACd,CAAC;EACH,CAAC;EAED,oBACElC,OAAA;IAAKM,KAAK,EAAE;MACVU,OAAO,EAAE,MAAM;MACf2C,GAAG,EAAE,KAAK;MACVJ,KAAK,EAAE,MAAM;MACbK,YAAY,EAAE;IAChB,CAAE;IAAAtC,QAAA,gBAEAtB,OAAA;MAAKM,KAAK,EAAE0C,iBAAiB,CAAC,CAAC,EAAEL,aAAa,CAACD,MAAM,CAAE;MAAApB,QAAA,gBACrDtB,OAAA,CAACG,YAAY;QACXC,SAAS,EAAEuC,aAAa,CAAC,CAAC,CAAC,CAACkB,SAAU;QACtCxD,GAAG,EAAE,GAAGmC,SAAS,YAAa;QAC9BlC,KAAK,EAAEmD,aAAa,CAAC,CAAC,EAAEd,aAAa,CAACD,MAAM,CAAE;QAC9CnC,YAAY,EAAGuD,CAAC,IAAK;UACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,aAAa;QACjD,CAAE;QACFxD,YAAY,EAAGsD,CAAC,IAAK;UACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,UAAU;QAC9C;MAAE;QAAAzC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACDe,YAAY,iBACXzC,OAAA;QACEM,KAAK,EAAE;UACLqB,QAAQ,EAAE,UAAU;UACpBC,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTuB,MAAM,EAAE;QACV,CAAE;QACFW,OAAO,EAAEA,CAAA,KAAMxB,YAAY,CAAC,CAAC;MAAE;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLiB,aAAa,CAACD,MAAM,GAAG,CAAC,iBACvB1C,OAAA;MAAKM,KAAK,EAAE;QACVU,OAAO,EAAE,MAAM;QACfkD,aAAa,EAAEvB,aAAa,CAACD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG,QAAQ;QAC5DiB,GAAG,EAAE,KAAK;QACVJ,KAAK,EAAEZ,aAAa,CAACD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG;MAC9C,CAAE;MAAApB,QAAA,EACCqB,aAAa,CAACC,KAAK,CAAC,CAAC,CAAC,CAACuB,GAAG,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;QAC1C,MAAMC,WAAW,GAAGD,GAAG,GAAG,CAAC;QAC3B,MAAME,MAAM,GAAGD,WAAW,KAAK3B,aAAa,CAACD,MAAM,GAAG,CAAC,IAAIG,cAAc,GAAG,CAAC;QAE7E,oBACE7C,OAAA;UAEEM,KAAK,EAAE;YACL,GAAG0C,iBAAiB,CAACsB,WAAW,EAAE3B,aAAa,CAACD,MAAM,CAAC;YACvDf,QAAQ,EAAE;UACZ,CAAE;UAAAL,QAAA,gBAEFtB,OAAA,CAACG,YAAY;YACXC,SAAS,EAAEgE,KAAK,CAACP,SAAU;YAC3BxD,GAAG,EAAE,GAAGmC,SAAS,YAAY8B,WAAW,GAAG,CAAC,EAAG;YAC/ChE,KAAK,EAAEmD,aAAa,CAACa,WAAW,EAAE3B,aAAa,CAACD,MAAM,CAAE;YACxDnC,YAAY,EAAGuD,CAAC,IAAK;cACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,aAAa;YACjD,CAAE;YACFxD,YAAY,EAAGsD,CAAC,IAAK;cACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,UAAU;YAC9C;UAAE;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGD6C,MAAM,IAAI1B,cAAc,GAAG,CAAC,iBAC3B7C,OAAA;YAAKM,KAAK,EAAE;cACVqB,QAAQ,EAAE,UAAU;cACpBC,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTZ,eAAe,EAAE,oBAAoB;cACrCH,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBE,KAAK,EAAE,OAAO;cACdC,QAAQ,EAAE,QAAQ;cAClBmD,UAAU,EAAE;YACd,CAAE;YAAAlD,QAAA,GAAC,GACA,EAACuB,cAAc;UAAA;YAAAtB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CACN,EAEAe,YAAY,iBACXzC,OAAA;YACEM,KAAK,EAAE;cACLqB,QAAQ,EAAE,UAAU;cACpBC,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTuB,MAAM,EAAE;YACV,CAAE;YACFW,OAAO,EAAEA,CAAA,KAAMxB,YAAY,CAAC6B,WAAW;UAAE;YAAA/C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CACF;QAAA,GAlDI4C,WAAW;UAAA/C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmDb,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;;AAED;AAAA+C,GAAA,GAvJMnC,YAAyC;AAwJ/C,MAAMoC,aAAuB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACpC,MAAMC,QAAQ,GAAGxG,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMyG,gBAAgB,GAAIC,YAAoB,IAAK;IACjD,MAAMC,MAAM,GAAG;MACb,UAAU,EAAE;QACVC,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE5F;MACR,CAAC;MACD,SAAS,EAAE;QACT2F,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE9F;MACR,CAAC;MACD,QAAQ,EAAE;QACR6F,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE3F;MACR,CAAC;MACD,WAAW,EAAE;QACX0F,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE1F;MACR,CAAC;MACD,QAAQ,EAAE;QACRyF,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAExF;MACR,CAAC;MACD,WAAW,EAAE;QACXuF,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEzF;MACR;IACF,CAAC;IAED,OAAOuF,MAAM,CAACD,YAAY,CAAwB,IAAIC,MAAM,CAAC,SAAS,CAAC;EACzE,CAAC;;EAED;EACA,MAAMG,mBAAmB,GAAIC,eAAuB,IAAK;IACvD,MAAMJ,MAAM,GAAG;MACb,kBAAkB,EAAE;QAClBC,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAErF;MACR,CAAC;MACD,cAAc,EAAE;QACdoF,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEtF;MACR,CAAC;MACD,gBAAgB,EAAE;QAChBqF,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEpF;MACR,CAAC;MACD,cAAc,EAAE;QACdmF,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAExF;MACR,CAAC;MACD,YAAY,EAAE;QACZuF,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEnF;MACR,CAAC;MACD,SAAS,EAAE;QACTkF,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEvF;MACR;IACF,CAAC;IAED,OAAOqF,MAAM,CAACI,eAAe,CAAwB,IAAIJ,MAAM,CAAC,cAAc,CAAC;EACjF,CAAC;;EAED;EACA,MAAM,CAACK,cAAc,EAAEC,iBAAiB,CAAC,GAAGnH,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAACoH,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGrH,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAACsH,UAAU,EAAEC,aAAa,CAAC,GAAGvH,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAM,CAACwH,YAAY,EAAEC,eAAe,CAAC,GAAGzH,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAAC0H,UAAU,EAAEC,aAAa,CAAC,GAAG3H,QAAQ,CAA4B,CAAC,CAAC,CAAC;EAC3E,MAAM,CAAC4H,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG7H,QAAQ,CAAgB,IAAI,CAAC;;EAE/E;EACA,MAAM,CAAC8H,aAAa,EAAEC,gBAAgB,CAAC,GAAG/H,QAAQ,CAAQ,EAAE,CAAC;EAC7D,MAAM,CAACgI,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGjI,QAAQ,CAAQ,EAAE,CAAC;EACzE,MAAM,CAACkI,cAAc,EAAEC,iBAAiB,CAAC,GAAGnI,QAAQ,CAAkB,EAAE,CAAC;EACzE,MAAM,CAACoI,OAAO,EAAEC,UAAU,CAAC,GAAGrI,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsI,KAAK,EAAEC,QAAQ,CAAC,GAAGvI,QAAQ,CAAqB,CAAC;EACxD,MAAM,CAACwI,eAAe,EAAEC,kBAAkB,CAAC,GAAGzI,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0I,aAAa,EAAEC,gBAAgB,CAAC,GAAG3I,QAAQ,CAAqB,CAAC;EACxE,MAAM,CAAC4I,cAAc,EAAEC,iBAAiB,CAAC,GAAG7I,QAAQ,CAAQ,EAAE,CAAC;EAC/D,MAAM,CAAC8I,cAAc,EAAEC,iBAAiB,CAAC,GAAG/I,QAAQ,CAAC,KAAK,CAAC;EAE3D,MAAM;IAAEgJ;EAAW,CAAC,GAAG5I,aAAa,CAAC,CAAC;;EAEtC;EACA,MAAM6I,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,IAAI;MACFZ,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAACW,SAAS,CAAC;MAEnB,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG5I,YAAY,wFAAwF,EAAE;QACpI6I,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;UAC/D,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC7B,MAAMG,iBAAiB,GAAGH,IAAI,CAACA,IAAI,CAAC1B,aAAa,IAAI,EAAE;;QAEvD;QACA,MAAM8B,uBAAuB,GAAG,MAAMC,OAAO,CAACC,GAAG,CAC/CH,iBAAiB,CAAC1D,GAAG,CAAC,MAAO8D,YAAiB,IAAK;UACjD,IAAI;YACF,MAAMC,aAAa,GAAG,MAAMZ,KAAK,CAAC,GAAG5I,YAAY,sBAAsBuJ,YAAY,CAACE,eAAe,SAAS,EAAE;cAC5GZ,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;gBAC/D,cAAc,EAAE;cAClB;YACF,CAAC,CAAC;YACF,MAAMW,SAAS,GAAG,MAAMF,aAAa,CAACP,IAAI,CAAC,CAAC;YAE5C,IAAIS,SAAS,CAACR,OAAO,IAAIQ,SAAS,CAACV,IAAI,EAAE;cACvCO,YAAY,CAAC1F,MAAM,GAAG6F,SAAS,CAACV,IAAI,CAACnF,MAAM,IAAI,EAAE;YACnD,CAAC,MAAM;cACL0F,YAAY,CAAC1F,MAAM,GAAG,EAAE;YAC1B;UACF,CAAC,CAAC,OAAO8F,MAAM,EAAE;YACfC,OAAO,CAACC,IAAI,CAAC,2CAA2CN,YAAY,CAACE,eAAe,GAAG,EAAEE,MAAM,CAAC;YAChGJ,YAAY,CAAC1F,MAAM,GAAG,EAAE;UAC1B;UACA,OAAO0F,YAAY;QACrB,CAAC,CACH,CAAC;QAEDhC,gBAAgB,CAAC6B,uBAAuB,CAAC;;QAEzC;QACA,MAAMU,MAAM,GAAGV,uBAAuB,CAACW,MAAM,CAAEC,GAAQ,IAAKA,GAAG,CAACC,SAAS,KAAK,CAAC,CAAC;QAChFxC,sBAAsB,CAACqC,MAAM,CAAC;MAChC,CAAC,MAAM;QACL/B,QAAQ,CAAC,8BAA8B,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOmC,GAAQ,EAAE;MACjBN,OAAO,CAAC9B,KAAK,CAAC,+BAA+B,EAAEoC,GAAG,CAAC;MACnDnC,QAAQ,CAACmC,GAAG,CAACC,OAAO,IAAI,8BAA8B,CAAC;IACzD,CAAC,SAAS;MACRtC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuC,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFnC,kBAAkB,CAAC,IAAI,CAAC;MACxBE,gBAAgB,CAACO,SAAS,CAAC;MAE3B,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG5I,YAAY,0DAA0D,EAAE;QACtG6I,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;UAC/D,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC7B,MAAMqB,UAAU,GAAGrB,IAAI,CAACA,IAAI,CAACsB,MAAM,IAAItB,IAAI,CAACA,IAAI,IAAI,EAAE;;QAEtD;QACA,MAAMuB,gBAAgB,GAAG,MAAMlB,OAAO,CAACC,GAAG,CACxCe,UAAU,CAAC5E,GAAG,CAAC,MAAO+E,KAAU,IAAK;UACnC,IAAI;YACF,MAAMhB,aAAa,GAAG,MAAMZ,KAAK,CAAC,GAAG5I,YAAY,iBAAiBwK,KAAK,CAACC,WAAW,SAAS,EAAE;cAC5F5B,OAAO,EAAE;gBACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;gBAC/D,cAAc,EAAE;cAClB;YACF,CAAC,CAAC;YACF,MAAMW,SAAS,GAAG,MAAMF,aAAa,CAACP,IAAI,CAAC,CAAC;YAE5C,IAAIS,SAAS,CAACR,OAAO,IAAIQ,SAAS,CAACV,IAAI,EAAE;cACvCwB,KAAK,CAAC3G,MAAM,GAAG6F,SAAS,CAACV,IAAI,CAAC0B,WAAW,IAAI,EAAE;YACjD,CAAC,MAAM;cACLF,KAAK,CAAC3G,MAAM,GAAG,EAAE;YACnB;UACF,CAAC,CAAC,OAAO8F,MAAM,EAAE;YACfC,OAAO,CAACC,IAAI,CAAC,oCAAoCW,KAAK,CAACC,WAAW,GAAG,EAAEd,MAAM,CAAC;YAC9Ea,KAAK,CAAC3G,MAAM,GAAG,EAAE;UACnB;UACA,OAAO2G,KAAK;QACd,CAAC,CACH,CAAC;QAED7C,iBAAiB,CAAC4C,gBAAgB,CAAC;MACrC,CAAC,MAAM;QACLpC,gBAAgB,CAAC,gCAAgC,CAAC;MACpD;IACF,CAAC,CAAC,OAAO+B,GAAQ,EAAE;MACjBN,OAAO,CAAC9B,KAAK,CAAC,iCAAiC,EAAEoC,GAAG,CAAC;MACrD/B,gBAAgB,CAAC+B,GAAG,CAACC,OAAO,IAAI,gCAAgC,CAAC;IACnE,CAAC,SAAS;MACRlC,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACA,MAAM0C,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACFpC,iBAAiB,CAAC,IAAI,CAAC;MACvB,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG5I,YAAY,iEAAiE,EAAE;QAC7G6I,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;UAC/D,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,MAAMC,IAAI,GAAG,MAAML,QAAQ,CAACM,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC7BX,iBAAiB,CAACW,IAAI,CAACA,IAAI,CAAC4B,QAAQ,IAAI,EAAE,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOV,GAAQ,EAAE;MACjBN,OAAO,CAAC9B,KAAK,CAAC,iCAAiC,EAAEoC,GAAG,CAAC;IACvD,CAAC,SAAS;MACR3B,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;;EAED;EACA9I,SAAS,CAAC,MAAM;IACdgJ,2BAA2B,CAAC,CAAC;IAC7B2B,mBAAmB,CAAC,CAAC;IACrBO,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAME,gBAAgB,GAAG,MAAOtB,YAAiB,IAAK;IACpD,IAAI;MACF,IAAIA,YAAY,CAACuB,aAAa,EAAE;QAC9B,MAAMnL,mBAAmB,CAACoL,cAAc,CAACxB,YAAY,CAACE,eAAe,CAAC;MACxE,CAAC,MAAM;QACL,MAAMd,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG5I,YAAY,sBAAsBuJ,YAAY,CAACE,eAAe,YAAY,EAAE;UAC1GuB,MAAM,EAAE,MAAM;UACdnC,OAAO,EAAE;YACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;YAC/D,cAAc,EAAE;UAClB,CAAC;UACDkC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBC,gBAAgB,EAAE,CAAC;YACnBC,YAAY,EAAE,KAAK,CAAC;UACtB,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,CAAC1C,QAAQ,CAAC2C,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;QAC3C;MACF;;MAEA;MACA,MAAM9C,2BAA2B,CAAC,CAAC;IACrC,CAAC,CAAC,OAAOX,KAAK,EAAE;MACd8B,OAAO,CAAC9B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,MAAM0D,mBAAmB,GAAG,MAAAA,CAAOC,cAAsB,EAAEC,WAAmB,KAAK;IACjF,IAAI,CAACA,WAAW,CAACC,IAAI,CAAC,CAAC,EAAE;IAEzB,IAAI;MACFtE,oBAAoB,CAACoE,cAAc,CAAC;MAEpC,MAAM9C,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG5I,YAAY,4BAA4ByL,cAAc,WAAW,EAAE;QACjGT,MAAM,EAAE,MAAM;QACdnC,OAAO,EAAE;UACP,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC,EAAE;UAC/D,cAAc,EAAE;QAClB,CAAC;QACDkC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBS,OAAO,EAAEF,WAAW;UACpBG,cAAc,EAAE,YAAY;UAC5BC,eAAe,EAAE,mBAAmB;UACpCT,YAAY,EAAE;QAChB,CAAC;MACH,CAAC,CAAC;MAEF,IAAI1C,QAAQ,CAAC2C,EAAE,EAAE;QACfnE,aAAa,CAAC4E,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACN,cAAc,GAAG;QAAG,CAAC,CAAC,CAAC;QAC1D,MAAMhD,2BAA2B,CAAC,CAAC;MACrC,CAAC,MAAM;QACLmB,OAAO,CAAC9B,KAAK,CAAC,0BAA0B,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd8B,OAAO,CAAC9B,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,SAAS;MACRT,oBAAoB,CAAC,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAM2E,qBAAqB,GAAG1E,aAAa,CAACyC,MAAM,CAACR,YAAY,IAAI;IAAA,IAAA0C,qBAAA,EAAAC,qBAAA;IACjE,MAAMC,aAAa,GAAG,CAACrF,UAAU,IAC/ByC,YAAY,CAAC6C,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxF,UAAU,CAACuF,WAAW,CAAC,CAAC,CAAC,IACnE9C,YAAY,CAACqC,OAAO,CAACS,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxF,UAAU,CAACuF,WAAW,CAAC,CAAC,CAAC;IAEvE,MAAME,eAAe,GAAG,CAAC7F,cAAc,IACrC,EAAAuF,qBAAA,GAAA1C,YAAY,CAACiD,WAAW,cAAAP,qBAAA,uBAAxBA,qBAAA,CAA0BQ,QAAQ,CAAC,CAAC,MAAK/F,cAAc;IAEzD,MAAMgG,iBAAiB,GAAG,CAAC9F,gBAAgB,IACzC,EAAAsF,qBAAA,GAAA3C,YAAY,CAACoD,WAAW,cAAAT,qBAAA,uBAAxBA,qBAAA,CAA0BO,QAAQ,CAAC,CAAC,MAAK7F,gBAAgB;IAE3D,OAAOuF,aAAa,IAAII,eAAe,IAAIG,iBAAiB;EAC9D,CAAC,CAAC;;EAEF;EACA,MAAME,sBAAsB,GAAGlF,cAAc,CAACqC,MAAM,CAACS,KAAK,IAAI;IAC5D,MAAM2B,aAAa,GAAG,CAACrF,UAAU,IAC/B0D,KAAK,CAAC4B,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxF,UAAU,CAACuF,WAAW,CAAC,CAAC,CAAC,IAC3D7B,KAAK,CAACqC,WAAW,IAAIrC,KAAK,CAACqC,WAAW,CAACR,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxF,UAAU,CAACuF,WAAW,CAAC,CAAC,CAAE;;IAE3F;IACA,MAAMS,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAE5B,MAAMC,SAAS,GAAG,IAAIF,IAAI,CAACvC,KAAK,CAAC0C,UAAU,CAAC;IAC5CD,SAAS,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhC,MAAMG,gBAAgB,GAAGF,SAAS,IAAIH,KAAK;IAC3C,MAAMM,WAAW,GAAI5C,KAAK,CAAS6C,YAAY,KAAK,CAAC;IAErD,OAAOlB,aAAa,IAAIgB,gBAAgB,IAAIC,WAAW;EACzD,CAAC,CAAC;;EAEF;EACA,MAAME,oBAAoB,GAAGtB,qBAAqB;EAClD,MAAMuB,aAAa,GAAGX,sBAAsB;;EAE5C;EACA,MAAMY,eAAe,GAAG,CACtB,GAAGF,oBAAoB,CAAC7H,GAAG,CAACgI,IAAI,KAAK;IAAE,GAAGA,IAAI;IAAEC,IAAI,EAAE,cAAc;IAAEC,QAAQ,EAAE,IAAIZ,IAAI,CAACU,IAAI,CAACG,UAAU;EAAE,CAAC,CAAC,CAAC,EAC7G,GAAGL,aAAa,CAAC9H,GAAG,CAACgI,IAAI,KAAK;IAAE,GAAGA,IAAI;IAAEC,IAAI,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAIZ,IAAI,CAACU,IAAI,CAACP,UAAU;EAAE,CAAC,CAAC,CAAC,CAChG,CAACW,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACJ,QAAQ,CAACK,OAAO,CAAC,CAAC,GAAGF,CAAC,CAACH,QAAQ,CAACK,OAAO,CAAC,CAAC,CAAC;EAE7D,oBACE1M,OAAA;IAAKM,KAAK,EAAE;MACVqM,SAAS,EAAE,OAAO;MAClB3H,UAAU,EAAE,mDAAmD;MAC/DrD,QAAQ,EAAE;IACZ,CAAE;IAAAL,QAAA,gBAEAtB,OAAA;MAAKM,KAAK,EAAE;QACVqB,QAAQ,EAAE,UAAU;QACpBC,GAAG,EAAE,CAAC;QACNC,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC;QACT6K,eAAe,EAAE;AACzB;AACA;AACA,SAAS;QACDC,aAAa,EAAE;MACjB;IAAE;MAAAtL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEL1B,OAAA;MAAKM,KAAK,EAAE;QAAEqB,QAAQ,EAAE,UAAU;QAAEmL,MAAM,EAAE;MAAE,CAAE;MAAAxL,QAAA,gBAE9CtB,OAAA;QAAQM,KAAK,EAAE;UACb0E,UAAU,EAAE,OAAO;UACnB+H,YAAY,EAAE,mBAAmB;UACjCpL,QAAQ,EAAE,QAAQ;UAClBC,GAAG,EAAE,CAAC;UACNkL,MAAM,EAAE,GAAG;UACXE,SAAS,EAAE;QACb,CAAE;QAAA1L,QAAA,eACAtB,OAAA;UAAKM,KAAK,EAAE;YACV2M,OAAO,EAAE,QAAQ;YACjBzJ,MAAM,EAAE,MAAM;YACdxC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAI,QAAA,gBAEAtB,OAAA;YAAKM,KAAK,EAAE;cACVU,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpB0C,GAAG,EAAE,QAAQ;cACbuJ,QAAQ,EAAE;YACZ,CAAE;YAAA5L,QAAA,gBACAtB,OAAA;cACEgC,GAAG,EAAC,iBAAiB;cACrB3B,GAAG,EAAC,WAAW;cACfC,KAAK,EAAE;gBACLiD,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdE,SAAS,EAAE;cACb;YAAE;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACF1B,OAAA;cAAAsB,QAAA,gBACEtB,OAAA;gBAAIM,KAAK,EAAE;kBACT6M,MAAM,EAAE,CAAC;kBACT9L,QAAQ,EAAE,SAAS;kBACnBmD,UAAU,EAAE,KAAK;kBACjBpD,KAAK,EAAE,SAAS;kBAChBgM,UAAU,EAAE;gBACd,CAAE;gBAAA9L,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL1B,OAAA;gBAAGM,KAAK,EAAE;kBACR6M,MAAM,EAAE,CAAC;kBACT9L,QAAQ,EAAE,UAAU;kBACpBD,KAAK,EAAE,SAAS;kBAChBgM,UAAU,EAAE;gBACd,CAAE;gBAAA9L,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1B,OAAA;YAAKM,KAAK,EAAE;cACV+M,IAAI,EAAE,CAAC;cACPC,QAAQ,EAAE,OAAO;cACjBH,MAAM,EAAE;YACV,CAAE;YAAA7L,QAAA,eACAtB,OAAA;cAAKM,KAAK,EAAE;gBAAEqB,QAAQ,EAAE;cAAW,CAAE;cAAAL,QAAA,gBACnCtB,OAAA,CAACpB,MAAM;gBACL2O,IAAI,EAAE,EAAG;gBACTjN,KAAK,EAAE;kBACLqB,QAAQ,EAAE,UAAU;kBACpBE,IAAI,EAAE,MAAM;kBACZD,GAAG,EAAE,KAAK;kBACVoC,SAAS,EAAE,kBAAkB;kBAC7B5C,KAAK,EAAE;gBACT;cAAE;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF1B,OAAA;gBACEoM,IAAI,EAAC,MAAM;gBACXoB,WAAW,EAAC,oCAAoC;gBAChDC,KAAK,EAAEjI,UAAW;gBAClBkI,QAAQ,EAAG5J,CAAC,IAAK2B,aAAa,CAAC3B,CAAC,CAAC6J,MAAM,CAACF,KAAK,CAAE;gBAC/CnN,KAAK,EAAE;kBACLiD,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdyJ,OAAO,EAAE,eAAe;kBACxBW,MAAM,EAAE,mBAAmB;kBAC3BvK,YAAY,EAAE,MAAM;kBACpB2B,UAAU,EAAE,SAAS;kBACrB5D,KAAK,EAAE,SAAS;kBAChBC,QAAQ,EAAE,UAAU;kBACpBwM,OAAO,EAAE,MAAM;kBACf3L,UAAU,EAAE;gBACd,CAAE;gBACF4L,OAAO,EAAGhK,CAAC,IAAK;kBACdA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAACyN,WAAW,GAAG,SAAS;kBAC7CjK,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,OAAO;kBAC1ClB,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0M,SAAS,GAAG,kCAAkC;gBACtE,CAAE;gBACFgB,MAAM,EAAGlK,CAAC,IAAK;kBACbA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAACyN,WAAW,GAAG,SAAS;kBAC7CjK,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,SAAS;kBAC5ClB,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0M,SAAS,GAAG,MAAM;gBAC1C;cAAE;gBAAAzL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN1B,OAAA;YAAKM,KAAK,EAAE;cACVU,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpB0C,GAAG,EAAE,MAAM;cACXuJ,QAAQ,EAAE,OAAO;cACjBhM,cAAc,EAAE;YAClB,CAAE;YAAAI,QAAA,gBAGAtB,OAAA;cAAKM,KAAK,EAAE;gBACVU,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB0C,GAAG,EAAE,QAAQ;gBACbsJ,OAAO,EAAE,QAAQ;gBACjBjI,UAAU,EAAE,SAAS;gBACrB3B,YAAY,EAAE,MAAM;gBACpBuK,MAAM,EAAE;cACV,CAAE;cAAAtM,QAAA,gBACAtB,OAAA;gBACEyN,KAAK,EAAErI,cAAe;gBACtBsI,QAAQ,EAAG5J,CAAC,IAAKuB,iBAAiB,CAACvB,CAAC,CAAC6J,MAAM,CAACF,KAAK,CAAE;gBACnDnN,KAAK,EAAE;kBACL2M,OAAO,EAAE,gBAAgB;kBACzBW,MAAM,EAAE,MAAM;kBACdvK,YAAY,EAAE,KAAK;kBACnB2B,UAAU,EAAE,OAAO;kBACnB5D,KAAK,EAAE,SAAS;kBAChBC,QAAQ,EAAE,UAAU;kBACpBwM,OAAO,EAAE,MAAM;kBACfvK,MAAM,EAAE,SAAS;kBACjB4J,QAAQ,EAAE;gBACZ,CAAE;gBAAA5L,QAAA,gBAEFtB,OAAA;kBAAQyN,KAAK,EAAC,EAAE;kBAAAnM,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACxC1B,OAAA;kBAAQyN,KAAK,EAAC,GAAG;kBAAAnM,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnC1B,OAAA;kBAAQyN,KAAK,EAAC,GAAG;kBAAAnM,QAAA,EAAC;gBAAM;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjC1B,OAAA;kBAAQyN,KAAK,EAAC,GAAG;kBAAAnM,QAAA,EAAC;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC,CAAC,eAET1B,OAAA;gBACEyN,KAAK,EAAEnI,gBAAiB;gBACxBoI,QAAQ,EAAG5J,CAAC,IAAKyB,mBAAmB,CAACzB,CAAC,CAAC6J,MAAM,CAACF,KAAK,CAAE;gBACrDnN,KAAK,EAAE;kBACL2M,OAAO,EAAE,gBAAgB;kBACzBW,MAAM,EAAE,MAAM;kBACdvK,YAAY,EAAE,KAAK;kBACnB2B,UAAU,EAAE,OAAO;kBACnB5D,KAAK,EAAE,SAAS;kBAChBC,QAAQ,EAAE,UAAU;kBACpBwM,OAAO,EAAE,MAAM;kBACfvK,MAAM,EAAE,SAAS;kBACjB4J,QAAQ,EAAE;gBACZ,CAAE;gBAAA5L,QAAA,gBAEFtB,OAAA;kBAAQyN,KAAK,EAAC,EAAE;kBAAAnM,QAAA,EAAC;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC1B,OAAA;kBAAQyN,KAAK,EAAC,IAAI;kBAAAnM,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpC1B,OAAA;kBAAQyN,KAAK,EAAC,IAAI;kBAAAnM,QAAA,EAAC;gBAAQ;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CAAC,EAER,CAAC8D,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,kBAChDtF,OAAA;gBACEiE,OAAO,EAAEA,CAAA,KAAM;kBACbwB,aAAa,CAAC,EAAE,CAAC;kBACjBJ,iBAAiB,CAAC,EAAE,CAAC;kBACrBE,mBAAmB,CAAC,EAAE,CAAC;gBACzB,CAAE;gBACFjF,KAAK,EAAE;kBACL2M,OAAO,EAAE,gBAAgB;kBACzBW,MAAM,EAAE,MAAM;kBACdvK,YAAY,EAAE,KAAK;kBACnB2B,UAAU,EAAE,SAAS;kBACrB5D,KAAK,EAAE,OAAO;kBACdC,QAAQ,EAAE,UAAU;kBACpBmD,UAAU,EAAE,KAAK;kBACjBlB,MAAM,EAAE,SAAS;kBACjBpB,UAAU,EAAE;gBACd,CAAE;gBACF3B,YAAY,EAAGuD,CAAC,IAAK;kBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,SAAS;gBAC9C,CAAE;gBACFxE,YAAY,EAAGsD,CAAC,IAAK;kBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,SAAS;gBAC9C,CAAE;gBAAA1D,QAAA,EACH;cAED;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAGN1B,OAAA;cACEiE,OAAO,EAAEA,CAAA,KAAMW,QAAQ,CAAC,kBAAkB,CAAE;cAC5CtE,KAAK,EAAE;gBACLU,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB0C,GAAG,EAAE,QAAQ;gBACbsJ,OAAO,EAAE,cAAc;gBACvBjI,UAAU,EAAE,mDAAmD;gBAC/D4I,MAAM,EAAE,MAAM;gBACdvK,YAAY,EAAE,MAAM;gBACpBjC,KAAK,EAAE,OAAO;gBACdC,QAAQ,EAAE,UAAU;gBACpBmD,UAAU,EAAE,KAAK;gBACjBlB,MAAM,EAAE,SAAS;gBACjBpB,UAAU,EAAE,eAAe;gBAC3B8K,SAAS,EAAE;cACb,CAAE;cACFzM,YAAY,EAAGuD,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,kBAAkB;gBACpDF,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0M,SAAS,GAAG,mCAAmC;cACvE,CAAE;cACFxM,YAAY,EAAGsD,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,eAAe;gBACjDF,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0M,SAAS,GAAG,kCAAkC;cACtE,CAAE;cAAA1L,QAAA,gBAEFtB,OAAA,CAACZ,eAAe;gBAACmO,IAAI,EAAE;cAAG;gBAAAhM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,aAE/B;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAKT1B,OAAA;QAAKM,KAAK,EAAE;UACV2M,OAAO,EAAE,MAAM;UACfjM,OAAO,EAAE,MAAM;UACf2C,GAAG,EAAE,MAAM;UACX1C,UAAU,EAAE;QACd,CAAE;QAAAK,QAAA,gBAEAtB,OAAA;UAAKM,KAAK,EAAE;YACViD,KAAK,EAAE,OAAO;YACd0K,UAAU,EAAE;UACd,CAAE;UAAA3M,QAAA,eACAtB,OAAA;YAAKM,KAAK,EAAE;cACV0E,UAAU,EAAE,OAAO;cACnB3B,YAAY,EAAE,MAAM;cACpBuK,MAAM,EAAE,mBAAmB;cAC3BxK,QAAQ,EAAE,QAAQ;cAClBzB,QAAQ,EAAE,QAAQ;cAClBC,GAAG,EAAE;YACP,CAAE;YAAAN,QAAA,gBAEAtB,OAAA;cAAKM,KAAK,EAAE;gBACV2M,OAAO,EAAE,oBAAoB;gBAC7BF,YAAY,EAAE;cAChB,CAAE;cAAAzL,QAAA,gBACAtB,OAAA;gBAAKM,KAAK,EAAE;kBACVU,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpB0C,GAAG,EAAE,SAAS;kBACdC,YAAY,EAAE;gBAChB,CAAE;gBAAAtC,QAAA,gBACAtB,OAAA,CAACnB,GAAG;kBAAC0O,IAAI,EAAE,EAAG;kBAACjN,KAAK,EAAE;oBAAEc,KAAK,EAAE;kBAAU;gBAAE;kBAAAG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC9C1B,OAAA;kBAAIM,KAAK,EAAE;oBACT6M,MAAM,EAAE,CAAC;oBACT9L,QAAQ,EAAE,UAAU;oBACpBmD,UAAU,EAAE,KAAK;oBACjBpD,KAAK,EAAE;kBACT,CAAE;kBAAAE,QAAA,EAAC;gBAEH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC,eACN1B,OAAA;gBAAGM,KAAK,EAAE;kBACR6M,MAAM,EAAE,CAAC;kBACT9L,QAAQ,EAAE,UAAU;kBACpBD,KAAK,EAAE;gBACT,CAAE;gBAAAE,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGN1B,OAAA;cAAKM,KAAK,EAAE;gBAAE2M,OAAO,EAAE;cAAO,CAAE;cAAA3L,QAAA,gBAE9BtB,OAAA;gBAAKM,KAAK,EAAE;kBACV2M,OAAO,EAAE,MAAM;kBACfjI,UAAU,EAAE,SAAS;kBACrB3B,YAAY,EAAE,MAAM;kBACpBuK,MAAM,EAAE,mBAAmB;kBAC3BhK,YAAY,EAAE,MAAM;kBACpBN,MAAM,EAAE,SAAS;kBACjBpB,UAAU,EAAE;gBACd,CAAE;gBACF3B,YAAY,EAAGuD,CAAC,IAAK;kBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,SAAS;kBAC5ClB,CAAC,CAACC,aAAa,CAACzD,KAAK,CAACyN,WAAW,GAAG,SAAS;gBAC/C,CAAE;gBACFvN,YAAY,EAAGsD,CAAC,IAAK;kBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,SAAS;kBAC5ClB,CAAC,CAACC,aAAa,CAACzD,KAAK,CAACyN,WAAW,GAAG,SAAS;gBAC/C,CAAE;gBAAAzM,QAAA,eACAtB,OAAA;kBAAKM,KAAK,EAAE;oBACVU,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,YAAY;oBACxB0C,GAAG,EAAE;kBACP,CAAE;kBAAArC,QAAA,gBACAtB,OAAA;oBAAKM,KAAK,EAAE;sBACViD,KAAK,EAAE,KAAK;sBACZC,MAAM,EAAE,KAAK;sBACbwB,UAAU,EAAE,SAAS;sBACrB3B,YAAY,EAAE,KAAK;sBACnB6K,SAAS,EAAE,QAAQ;sBACnBD,UAAU,EAAE;oBACd;kBAAE;oBAAA1M,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACL1B,OAAA;oBAAKM,KAAK,EAAE;sBAAE+M,IAAI,EAAE;oBAAE,CAAE;oBAAA/L,QAAA,gBACtBtB,OAAA;sBAAIM,KAAK,EAAE;wBACT6M,MAAM,EAAE,cAAc;wBACtB9L,QAAQ,EAAE,UAAU;wBACpBmD,UAAU,EAAE,KAAK;wBACjBpD,KAAK,EAAE,SAAS;wBAChBgM,UAAU,EAAE;sBACd,CAAE;sBAAA9L,QAAA,EAAC;oBAEH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACL1B,OAAA;sBAAGM,KAAK,EAAE;wBACR6M,MAAM,EAAE,cAAc;wBACtB9L,QAAQ,EAAE,QAAQ;wBAClBD,KAAK,EAAE,SAAS;wBAChBgM,UAAU,EAAE;sBACd,CAAE;sBAAA9L,QAAA,EAAC;oBAEH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACJ1B,OAAA;sBAAKM,KAAK,EAAE;wBACVU,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB0C,GAAG,EAAE,QAAQ;wBACbtC,QAAQ,EAAE,SAAS;wBACnBD,KAAK,EAAE;sBACT,CAAE;sBAAAE,QAAA,gBACAtB,OAAA,CAAClB,QAAQ;wBAACyO,IAAI,EAAE;sBAAG;wBAAAhM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACtB1B,OAAA;wBAAAsB,QAAA,EAAM;sBAAU;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN1B,OAAA;gBAAKM,KAAK,EAAE;kBACV2M,OAAO,EAAE,MAAM;kBACfjI,UAAU,EAAE,SAAS;kBACrB3B,YAAY,EAAE,MAAM;kBACpBuK,MAAM,EAAE,mBAAmB;kBAC3BhK,YAAY,EAAE,MAAM;kBACpBN,MAAM,EAAE,SAAS;kBACjBpB,UAAU,EAAE;gBACd,CAAE;gBACF3B,YAAY,EAAGuD,CAAC,IAAK;kBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,SAAS;kBAC5ClB,CAAC,CAACC,aAAa,CAACzD,KAAK,CAACyN,WAAW,GAAG,SAAS;gBAC/C,CAAE;gBACFvN,YAAY,EAAGsD,CAAC,IAAK;kBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,SAAS;kBAC5ClB,CAAC,CAACC,aAAa,CAACzD,KAAK,CAACyN,WAAW,GAAG,SAAS;gBAC/C,CAAE;gBAAAzM,QAAA,eACAtB,OAAA;kBAAKM,KAAK,EAAE;oBACVU,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,YAAY;oBACxB0C,GAAG,EAAE;kBACP,CAAE;kBAAArC,QAAA,gBACAtB,OAAA;oBAAKM,KAAK,EAAE;sBACViD,KAAK,EAAE,KAAK;sBACZC,MAAM,EAAE,KAAK;sBACbwB,UAAU,EAAE,SAAS;sBACrB3B,YAAY,EAAE,KAAK;sBACnB6K,SAAS,EAAE,QAAQ;sBACnBD,UAAU,EAAE;oBACd;kBAAE;oBAAA1M,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACL1B,OAAA;oBAAKM,KAAK,EAAE;sBAAE+M,IAAI,EAAE;oBAAE,CAAE;oBAAA/L,QAAA,gBACtBtB,OAAA;sBAAIM,KAAK,EAAE;wBACT6M,MAAM,EAAE,cAAc;wBACtB9L,QAAQ,EAAE,UAAU;wBACpBmD,UAAU,EAAE,KAAK;wBACjBpD,KAAK,EAAE,SAAS;wBAChBgM,UAAU,EAAE;sBACd,CAAE;sBAAA9L,QAAA,EAAC;oBAEH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACL1B,OAAA;sBAAGM,KAAK,EAAE;wBACR6M,MAAM,EAAE,cAAc;wBACtB9L,QAAQ,EAAE,QAAQ;wBAClBD,KAAK,EAAE,SAAS;wBAChBgM,UAAU,EAAE;sBACd,CAAE;sBAAA9L,QAAA,EAAC;oBAEH;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACJ1B,OAAA;sBAAKM,KAAK,EAAE;wBACVU,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB0C,GAAG,EAAE,QAAQ;wBACbtC,QAAQ,EAAE,SAAS;wBACnBD,KAAK,EAAE;sBACT,CAAE;sBAAAE,QAAA,gBACAtB,OAAA,CAAClB,QAAQ;wBAACyO,IAAI,EAAE;sBAAG;wBAAAhM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACtB1B,OAAA;wBAAAsB,QAAA,EAAM;sBAAU;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN1B,OAAA;gBAAQM,KAAK,EAAE;kBACbiD,KAAK,EAAE,MAAM;kBACb0J,OAAO,EAAE,SAAS;kBAClBW,MAAM,EAAE,mBAAmB;kBAC3BvK,YAAY,EAAE,KAAK;kBACnB2B,UAAU,EAAE,OAAO;kBACnB5D,KAAK,EAAE,SAAS;kBAChBC,QAAQ,EAAE,UAAU;kBACpBmD,UAAU,EAAE,KAAK;kBACjBlB,MAAM,EAAE,SAAS;kBACjBpB,UAAU,EAAE;gBACd,CAAE;gBACF3B,YAAY,EAAGuD,CAAC,IAAK;kBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,SAAS;kBAC5ClB,CAAC,CAACC,aAAa,CAACzD,KAAK,CAACyN,WAAW,GAAG,SAAS;gBAC/C,CAAE;gBACFvN,YAAY,EAAGsD,CAAC,IAAK;kBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,OAAO;kBAC1ClB,CAAC,CAACC,aAAa,CAACzD,KAAK,CAACyN,WAAW,GAAG,SAAS;gBAC/C,CAAE;gBAAAzM,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN1B,OAAA;UAAKM,KAAK,EAAE;YAAE+M,IAAI,EAAE,CAAC;YAAEH,QAAQ,EAAE;UAAE,CAAE;UAAA5L,QAAA,GAEpC,CAACgF,OAAO,IAAII,eAAe,kBAC1B1G,OAAA;YAAKM,KAAK,EAAE;cACVU,OAAO,EAAE,MAAM;cACfE,cAAc,EAAE,QAAQ;cACxBD,UAAU,EAAE,QAAQ;cACpB0L,SAAS,EAAE;YACb,CAAE;YAAArL,QAAA,eACAtB,OAAA;cAAKM,KAAK,EAAE;gBACVU,OAAO,EAAE,MAAM;gBACfkD,aAAa,EAAE,QAAQ;gBACvBjD,UAAU,EAAE,QAAQ;gBACpB0C,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,gBACAtB,OAAA;gBAAKM,KAAK,EAAE;kBACViD,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdoK,MAAM,EAAE,kCAAkC;kBAC1CO,SAAS,EAAE,mBAAmB;kBAC9B9K,YAAY,EAAE,KAAK;kBACnB+K,SAAS,EAAE;gBACb;cAAE;gBAAA7M,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACT1B,OAAA;gBAAGM,KAAK,EAAE;kBACRc,KAAK,EAAE,SAAS;kBAChBC,QAAQ,EAAE,MAAM;kBAChBmD,UAAU,EAAE;gBACd,CAAE;gBAAAlD,QAAA,EAAC;cAEH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA,CAAC8E,KAAK,IAAII,aAAa,KAAK,CAACN,OAAO,IAAI,CAACI,eAAe,iBACvD1G,OAAA;YAAKM,KAAK,EAAE;cACV2M,OAAO,EAAE,MAAM;cACfjI,UAAU,EAAE,wBAAwB;cACpC4I,MAAM,EAAE,kCAAkC;cAC1CvK,YAAY,EAAE,MAAM;cACpBgL,SAAS,EAAE;YACb,CAAE;YAAA/M,QAAA,gBACAtB,OAAA;cAAKM,KAAK,EAAE;gBACViD,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdwB,UAAU,EAAE,wBAAwB;gBACpC3B,YAAY,EAAE,KAAK;gBACnBrC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBiM,MAAM,EAAE;cACV,CAAE;cAAA7L,QAAA,eACAtB,OAAA,CAACjB,aAAa;gBAACwO,IAAI,EAAE,EAAG;gBAACnM,KAAK,EAAC;cAAS;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eACN1B,OAAA;cAAIM,KAAK,EAAE;gBACTc,KAAK,EAAE,SAAS;gBAChB+L,MAAM,EAAE,cAAc;gBACtB9L,QAAQ,EAAE,SAAS;gBACnBmD,UAAU,EAAE;cACd,CAAE;cAAAlD,QAAA,EAAC;YAEH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1B,OAAA;cAAGM,KAAK,EAAE;gBACRc,KAAK,EAAE,SAAS;gBAChB+L,MAAM,EAAE,cAAc;gBACtB9L,QAAQ,EAAE;cACZ,CAAE;cAAAC,QAAA,EACCkF,KAAK,IAAII;YAAa;cAAArF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACJ1B,OAAA;cACEiE,OAAO,EAAEA,CAAA,KAAM;gBACbkD,2BAA2B,CAAC,CAAC;gBAC7B2B,mBAAmB,CAAC,CAAC;cACvB,CAAE;cACFxI,KAAK,EAAE;gBACL0E,UAAU,EAAE,mDAAmD;gBAC/D5D,KAAK,EAAE,OAAO;gBACdwM,MAAM,EAAE,MAAM;gBACdvK,YAAY,EAAE,MAAM;gBACpB4J,OAAO,EAAE,gBAAgB;gBACzB5L,QAAQ,EAAE,UAAU;gBACpBmD,UAAU,EAAE,KAAK;gBACjBlB,MAAM,EAAE,SAAS;gBACjBpB,UAAU,EAAE;cACd,CAAE;cACF3B,YAAY,EAAGuD,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,kBAAkB;gBACpDF,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0M,SAAS,GAAG,mCAAmC;cACvE,CAAE;cACFxM,YAAY,EAAGsD,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,eAAe;gBACjDF,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0M,SAAS,GAAG,MAAM;cAC1C,CAAE;cAAA1L,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAGA,CAAC4E,OAAO,IAAI,CAACI,eAAe,IAAI,CAACF,KAAK,IAAI,CAACI,aAAa,IACxDoF,oBAAoB,CAACtJ,MAAM,KAAK,CAAC,IAAIuJ,aAAa,CAACvJ,MAAM,KAAK,CAAC,iBAC9D1C,OAAA;YAAKM,KAAK,EAAE;cACV2M,OAAO,EAAE,WAAW;cACpBoB,SAAS,EAAE;YACb,CAAE;YAAA/M,QAAA,gBACAtB,OAAA;cAAKM,KAAK,EAAE;gBACViD,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdwB,UAAU,EAAE,mDAAmD;gBAC/D3B,YAAY,EAAE,KAAK;gBACnBrC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBiM,MAAM,EAAE;cACV,CAAE;cAAA7L,QAAA,eACAtB,OAAA,CAACrB,SAAS;gBAAC4O,IAAI,EAAE,EAAG;gBAACnM,KAAK,EAAC;cAAO;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,eACN1B,OAAA;cAAIM,KAAK,EAAE;gBACTc,KAAK,EAAE,SAAS;gBAChB+L,MAAM,EAAE,YAAY;gBACpB9L,QAAQ,EAAE,QAAQ;gBAClBmD,UAAU,EAAE;cACd,CAAE;cAAAlD,QAAA,EAAC;YAEH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1B,OAAA;cAAGM,KAAK,EAAE;gBACRc,KAAK,EAAE,SAAS;gBAChB+L,MAAM,EAAE,YAAY;gBACpB9L,QAAQ,EAAE,MAAM;gBAChB+L,UAAU,EAAE,KAAK;gBACjBE,QAAQ,EAAE,OAAO;gBACjBgB,UAAU,EAAE,MAAM;gBAClBC,WAAW,EAAE;cACf,CAAE;cAAAjN,QAAA,EACCkE,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,GAC7C,8EAA8E,GAC9E;YAA6F;cAAA/D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhG,CAAC,EACH,CAAC8D,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,kBAChDtF,OAAA;cACEiE,OAAO,EAAEA,CAAA,KAAM;gBACbwB,aAAa,CAAC,EAAE,CAAC;gBACjBJ,iBAAiB,CAAC,EAAE,CAAC;gBACrBE,mBAAmB,CAAC,EAAE,CAAC;cACzB,CAAE;cACFjF,KAAK,EAAE;gBACL0E,UAAU,EAAE,mDAAmD;gBAC/D5D,KAAK,EAAE,OAAO;gBACdwM,MAAM,EAAE,MAAM;gBACdvK,YAAY,EAAE,MAAM;gBACpB4J,OAAO,EAAE,gBAAgB;gBACzB5L,QAAQ,EAAE,UAAU;gBACpBmD,UAAU,EAAE,KAAK;gBACjBlB,MAAM,EAAE,SAAS;gBACjBpB,UAAU,EAAE;cACd,CAAE;cACF3B,YAAY,EAAGuD,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,kBAAkB;gBACpDF,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0M,SAAS,GAAG,mCAAmC;cACvE,CAAE;cACFxM,YAAY,EAAGsD,CAAC,IAAK;gBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,eAAe;gBACjDF,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0M,SAAS,GAAG,MAAM;cAC1C,CAAE;cAAA1L,QAAA,EACH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CACT;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN,EAGA,CAACsF,cAAc,IAAIF,cAAc,CAACpE,MAAM,GAAG,CAAC,iBAC3C1C,OAAA;YAAKM,KAAK,EAAE;cACV0E,UAAU,EAAE,2BAA2B;cACvC3B,YAAY,EAAE,MAAM;cACpB4J,OAAO,EAAE,QAAQ;cACjBW,MAAM,EAAE,8BAA8B;cACtCY,cAAc,EAAE,YAAY;cAC5BxB,SAAS,EAAE,gCAAgC;cAC3CpJ,YAAY,EAAE;YAChB,CAAE;YAAAtC,QAAA,gBACAtB,OAAA;cAAIM,KAAK,EAAE;gBACTe,QAAQ,EAAE,SAAS;gBACnBmD,UAAU,EAAE,KAAK;gBACjBpD,KAAK,EAAE,SAAS;gBAChB+L,MAAM,EAAE,YAAY;gBACpBnM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB0C,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,EAAC;YAEH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1B,OAAA;cAAKM,KAAK,EAAE;gBACVU,OAAO,EAAE,MAAM;gBACf2C,GAAG,EAAE;cACP,CAAE;cAAArC,QAAA,EACCwF,cAAc,CAAClE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACuB,GAAG,CAAEsK,OAAY;gBAAA,IAAAC,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA;gBAAA,oBAC3C5O,OAAA;kBAEEM,KAAK,EAAE;oBACLU,OAAO,EAAE,MAAM;oBACfE,cAAc,EAAE,eAAe;oBAC/BD,UAAU,EAAE,QAAQ;oBACpBgM,OAAO,EAAE,SAAS;oBAClB9L,eAAe,EAAE,SAAS;oBAC1BkC,YAAY,EAAE,KAAK;oBACnBuK,MAAM,EAAE;kBACV,CAAE;kBAAAtM,QAAA,gBAEFtB,OAAA;oBAAAsB,QAAA,gBACEtB,OAAA;sBAAKM,KAAK,EAAE;wBACVkE,UAAU,EAAE,KAAK;wBACjBpD,KAAK,EAAE,SAAS;wBAChBC,QAAQ,EAAE;sBACZ,CAAE;sBAAAC,QAAA,IAAAoN,gBAAA,GACCD,OAAO,CAACI,OAAO,cAAAH,gBAAA,uBAAfA,gBAAA,CAAiBI,UAAU,EAAC,GAAC,GAAAH,iBAAA,GAACF,OAAO,CAACI,OAAO,cAAAF,iBAAA,uBAAfA,iBAAA,CAAiBI,SAAS;oBAAA;sBAAAxN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACtD,CAAC,eACN1B,OAAA;sBAAKM,KAAK,EAAE;wBACVe,QAAQ,EAAE,SAAS;wBACnBD,KAAK,EAAE;sBACT,CAAE;sBAAAE,QAAA,GAAC,QACK,GAAAsN,iBAAA,GAACH,OAAO,CAACI,OAAO,cAAAD,iBAAA,uBAAfA,iBAAA,CAAiBvD,WAAW,EAAC,UAAG,EAACoD,OAAO,CAACO,cAAc;oBAAA;sBAAAzN,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC3D,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eACN1B,OAAA;oBAAKM,KAAK,EAAE;sBACVe,QAAQ,EAAE,SAAS;sBACnBD,KAAK,EAAE;oBACT,CAAE;oBAAAE,QAAA,EACC,IAAImK,IAAI,CAACgD,OAAO,CAACnC,UAAU,CAAC,CAAC2C,kBAAkB,CAAC;kBAAC;oBAAA1N,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/C,CAAC;gBAAA,GA/BD+M,OAAO,CAACS,UAAU;kBAAA3N,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAgCpB,CAAC;cAAA,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAGA,CAAC4E,OAAO,IAAI,CAACI,eAAe,KAAKsF,oBAAoB,CAACtJ,MAAM,GAAG,CAAC,IAAIuJ,aAAa,CAACvJ,MAAM,GAAG,CAAC,CAAC,iBAC5F1C,OAAA;YAAKM,KAAK,EAAE;cACVU,OAAO,EAAE,MAAM;cACfkD,aAAa,EAAE,QAAQ;cACvBP,GAAG,EAAE;YACP,CAAE;YAAArC,QAAA,GAEC2K,aAAa,CAACvJ,MAAM,GAAG,CAAC,iBACvB1C,OAAA,CAAAE,SAAA;cAAAoB,QAAA,EACG2K,aAAa,CAAC9H,GAAG,CAAC+E,KAAK,iBACtBlJ,OAAA;gBAEEM,KAAK,EAAE;kBACL0E,UAAU,EAAE,2BAA2B;kBACvC3B,YAAY,EAAE,MAAM;kBACpB4J,OAAO,EAAE,QAAQ;kBACjBW,MAAM,EAAE,8BAA8B;kBACtCY,cAAc,EAAE,YAAY;kBAC5BxB,SAAS,EAAE,gCAAgC;kBAC3C9K,UAAU,EAAE;gBACd,CAAE;gBACF3B,YAAY,EAAGuD,CAAC,IAAK;kBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,kBAAkB;kBACpDF,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0M,SAAS,GAAG,gCAAgC;gBACpE,CAAE;gBACFxM,YAAY,EAAGsD,CAAC,IAAK;kBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,eAAe;kBACjDF,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0M,SAAS,GAAG,gCAAgC;gBACpE,CAAE;gBAAA1L,QAAA,gBAGFtB,OAAA;kBAAKM,KAAK,EAAE;oBACVU,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,YAAY;oBACxB0C,GAAG,EAAE,MAAM;oBACXC,YAAY,EAAE;kBAChB,CAAE;kBAAAtC,QAAA,gBACAtB,OAAA;oBAAKM,KAAK,EAAE;sBACViD,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdwB,UAAU,EAAE,mDAAmD;sBAC/D3B,YAAY,EAAE,MAAM;sBACpBrC,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE,QAAQ;sBACxB+M,UAAU,EAAE;oBACd,CAAE;oBAAA3M,QAAA,eACAtB,OAAA,CAAClB,QAAQ;sBAACyO,IAAI,EAAE,EAAG;sBAACnM,KAAK,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC,CAAC,eAEN1B,OAAA;oBAAKM,KAAK,EAAE;sBAAE+M,IAAI,EAAE;oBAAE,CAAE;oBAAA/L,QAAA,gBACtBtB,OAAA;sBAAKM,KAAK,EAAE;wBACVU,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB0C,GAAG,EAAE,SAAS;wBACdC,YAAY,EAAE;sBAChB,CAAE;sBAAAtC,QAAA,GACC,CAAC,MAAM;wBACN,MAAM6D,eAAe,GAAG+D,KAAK,CAACiG,iBAAiB,IAAI,cAAc;wBACjE,MAAMC,YAAY,GAAGlK,mBAAmB,CAACC,eAAe,CAAC;wBACzD,MAAMkK,aAAa,GAAGD,YAAY,CAACnK,IAAI;wBAEvC,oBACEjF,OAAA;0BAAMM,KAAK,EAAE;4BACX0E,UAAU,EAAEoK,YAAY,CAACpK,UAAU;4BACnC5D,KAAK,EAAE,OAAO;4BACdC,QAAQ,EAAE,SAAS;4BACnBmD,UAAU,EAAE,KAAK;4BACjByI,OAAO,EAAE,iBAAiB;4BAC1B5J,YAAY,EAAE,MAAM;4BACpBiM,aAAa,EAAE,WAAW;4BAC1BC,aAAa,EAAE,OAAO;4BACtBvO,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpB0C,GAAG,EAAE;0BACP,CAAE;0BAAArC,QAAA,gBACAtB,OAAA,CAACqP,aAAa;4BAAC9B,IAAI,EAAE,EAAG;4BAACnM,KAAK,EAAC;0BAAO;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,EACxCyD,eAAe;wBAAA;0BAAA5D,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACZ,CAAC;sBAEX,CAAC,EAAE,CAAC,eAEJ1B,OAAA;wBAAKM,KAAK,EAAE;0BACVU,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB0C,GAAG,EAAE,SAAS;0BACdvC,KAAK,EAAE,SAAS;0BAChBC,QAAQ,EAAE;wBACZ,CAAE;wBAAAC,QAAA,gBACAtB,OAAA,CAAClB,QAAQ;0BAACyO,IAAI,EAAE;wBAAG;0BAAAhM,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,EACrB,IAAI+J,IAAI,CAACvC,KAAK,CAAC0C,UAAU,CAAC,CAACqD,kBAAkB,CAAC,OAAO,EAAE;0BACtDO,OAAO,EAAE,MAAM;0BACfC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,MAAM;0BACbC,GAAG,EAAE;wBACP,CAAC,CAAC;sBAAA;wBAAApO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN1B,OAAA;sBAAIM,KAAK,EAAE;wBACT6M,MAAM,EAAE,cAAc;wBACtB9L,QAAQ,EAAE,SAAS;wBACnBmD,UAAU,EAAE,KAAK;wBACjBpD,KAAK,EAAE,SAAS;wBAChBgM,UAAU,EAAE;sBACd,CAAE;sBAAA9L,QAAA,EACC4H,KAAK,CAAC4B;oBAAK;sBAAAvJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACV,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAGN1B,OAAA;oBAAKM,KAAK,EAAE;sBAAEU,OAAO,EAAE,MAAM;sBAAE2C,GAAG,EAAE;oBAAS,CAAE;oBAAArC,QAAA,eAC7CtB,OAAA;sBACEiE,OAAO,EAAEA,CAAA,KAAMW,QAAQ,CAAC,yBAAyBsE,KAAK,CAACC,WAAW,EAAE,CAAE;sBACtE7I,KAAK,EAAE;wBACLU,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB0C,GAAG,EAAE,SAAS;wBACdsJ,OAAO,EAAE,gBAAgB;wBACzBjI,UAAU,EAAE,yBAAyB;wBACrC4I,MAAM,EAAE,mCAAmC;wBAC3CvK,YAAY,EAAE,KAAK;wBACnBjC,KAAK,EAAE,SAAS;wBAChBC,QAAQ,EAAE,SAAS;wBACnBmD,UAAU,EAAE,KAAK;wBACjBlB,MAAM,EAAE,SAAS;wBACjBpB,UAAU,EAAE;sBACd,CAAE;sBACF3B,YAAY,EAAGuD,CAAC,IAAK;wBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,yBAAyB;sBAC9D,CAAE;sBACFxE,YAAY,EAAGsD,CAAC,IAAK;wBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,yBAAyB;sBAC9D,CAAE;sBAAA1D,QAAA,gBAEFtB,OAAA,CAACd,IAAI;wBAACqO,IAAI,EAAE;sBAAG;wBAAAhM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,QAEpB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGLwH,KAAK,CAACqC,WAAW,iBAChBvL,OAAA;kBAAKM,KAAK,EAAE;oBACVc,KAAK,EAAE,SAAS;oBAChBC,QAAQ,EAAE,SAAS;oBACnB+L,UAAU,EAAE,KAAK;oBACjBxJ,YAAY,EAAE;kBAChB,CAAE;kBAAAtC,QAAA,EACC4H,KAAK,CAACqC;gBAAW;kBAAAhK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CACN,EAGA,CAAC,MAAM;kBACN;kBACA,MAAMkO,cAAwB,GAAG,EAAE;kBAEnC,IAAK1G,KAAK,CAAS3G,MAAM,IAAK2G,KAAK,CAAS3G,MAAM,CAACG,MAAM,GAAG,CAAC,EAAE;oBAC5DwG,KAAK,CAAS3G,MAAM,CAACsN,OAAO,CAAEC,GAAQ,IAAK;sBAC1C,IAAIA,GAAG,CAACjM,SAAS,EAAE;wBACjB;wBACA,MAAMkM,QAAQ,GAAGtR,WAAW,CAACqR,GAAG,CAACjM,SAAS,CAAC;wBAC3C,IAAIkM,QAAQ,EAAE;0BACZH,cAAc,CAACI,IAAI,CAACD,QAAQ,CAAC;wBAC/B;sBACF;oBACF,CAAC,CAAC;kBACJ;kBAEA,OAAOH,cAAc,CAAClN,MAAM,GAAG,CAAC,gBAC9B1C,OAAA;oBAAKM,KAAK,EAAE;sBAAEsD,YAAY,EAAE;oBAAO,CAAE;oBAAAtC,QAAA,eACnCtB,OAAA,CAACxB,oBAAoB;sBACnB+D,MAAM,EAAEqN,cAAc,CAACnH,MAAM,CAACwH,OAAO,CAAc;sBACnDzN,SAAS,EAAE0G,KAAK,CAAC4B,KAAM;sBACvBoF,UAAU,EAAE,CAAE;sBACdzN,YAAY,EAAGQ,KAAK,IAAK;wBACvBqF,OAAO,CAAC6H,GAAG,CAAC,iBAAiBlN,KAAK,GAAG,CAAC,eAAeiG,KAAK,CAAC4B,KAAK,EAAE,CAAC;sBACrE;oBAAE;sBAAAvJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,GACJ,IAAI;gBACV,CAAC,EAAE,CAAC,eAGJ1B,OAAA;kBAAKM,KAAK,EAAE;oBACVU,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpB0C,GAAG,EAAE,QAAQ;oBACbsJ,OAAO,EAAE,MAAM;oBACfjI,UAAU,EAAE,0BAA0B;oBACtC3B,YAAY,EAAE,MAAM;oBACpBhC,QAAQ,EAAE;kBACZ,CAAE;kBAAAC,QAAA,gBACAtB,OAAA;oBAAKM,KAAK,EAAE;sBACVU,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB0C,GAAG,EAAE,QAAQ;sBACbvC,KAAK,EAAE;oBACT,CAAE;oBAAAE,QAAA,gBACAtB,OAAA,CAAClB,QAAQ;sBAACyO,IAAI,EAAE;oBAAG;sBAAAhM,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACtB1B,OAAA;sBAAAsB,QAAA,EACG4H,KAAK,CAACkH,QAAQ,IAAIlH,KAAK,CAACkH,QAAQ,KAAKlH,KAAK,CAAC0C,UAAU,GAClD,GAAG,IAAIH,IAAI,CAACvC,KAAK,CAAC0C,UAAU,CAAC,CAACqD,kBAAkB,CAAC,CAAC,MAAM,IAAIxD,IAAI,CAACvC,KAAK,CAACkH,QAAQ,CAAC,CAACnB,kBAAkB,CAAC,CAAC,EAAE,GACvG,IAAIxD,IAAI,CAACvC,KAAK,CAAC0C,UAAU,CAAC,CAACqD,kBAAkB,CAAC;oBAAC;sBAAA1N,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAE/C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,EAELwH,KAAK,CAACiG,iBAAiB,iBACtBnP,OAAA;oBAAKM,KAAK,EAAE;sBACVU,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB0C,GAAG,EAAE,QAAQ;sBACbvC,KAAK,EAAE;oBACT,CAAE;oBAAAE,QAAA,eACAtB,OAAA;sBAAMM,KAAK,EAAE;wBACX2M,OAAO,EAAE,gBAAgB;wBACzBjI,UAAU,EAAE,yBAAyB;wBACrC3B,YAAY,EAAE,KAAK;wBACnBhC,QAAQ,EAAE,SAAS;wBACnBmD,UAAU,EAAE;sBACd,CAAE;sBAAAlD,QAAA,EACC4H,KAAK,CAACiG;oBAAiB;sBAAA5N,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpB;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACE,CAAC;cAAA,GAxND,SAASwH,KAAK,CAACC,WAAW,EAAE;gBAAA5H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyN9B,CACN;YAAC,gBACF,CACH,EAGAsK,oBAAoB,CAACtJ,MAAM,GAAG,CAAC,iBAC9B1C,OAAA,CAAAE,SAAA;cAAAoB,QAAA,EACG0K,oBAAoB,CAAC7H,GAAG,CAAC8D,YAAY,iBACpCjI,OAAA;gBAEEM,KAAK,EAAE;kBACL0E,UAAU,EAAE,2BAA2B;kBACvC3B,YAAY,EAAE,MAAM;kBACpB4J,OAAO,EAAE,QAAQ;kBACjBW,MAAM,EAAE3F,YAAY,CAACU,SAAS,GAC1B,mCAAmC,GACnC,8BAA8B;kBAClC6F,cAAc,EAAE,YAAY;kBAC5BxB,SAAS,EAAE/E,YAAY,CAACU,SAAS,GAC7B,qCAAqC,GACrC,gCAAgC;kBACpCzG,UAAU,EAAE,2CAA2C;kBACvDP,QAAQ,EAAE;gBACZ,CAAE;gBACFpB,YAAY,EAAGuD,CAAC,IAAK;kBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,kBAAkB;kBACpDF,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0M,SAAS,GAAG/E,YAAY,CAACU,SAAS,GACpD,qCAAqC,GACrC,gCAAgC;gBACtC,CAAE;gBACFnI,YAAY,EAAGsD,CAAC,IAAK;kBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0D,SAAS,GAAG,eAAe;kBACjDF,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0M,SAAS,GAAG/E,YAAY,CAACU,SAAS,GACpD,qCAAqC,GACrC,gCAAgC;gBACtC,CAAE;gBAAArH,QAAA,GAGD2G,YAAY,CAACU,SAAS,iBACrB3I,OAAA;kBAAKM,KAAK,EAAE;oBACVqB,QAAQ,EAAE,UAAU;oBACpBC,GAAG,EAAE,MAAM;oBACXE,KAAK,EAAE,MAAM;oBACbkD,UAAU,EAAE,mDAAmD;oBAC/D5D,KAAK,EAAE,OAAO;oBACd6L,OAAO,EAAE,iBAAiB;oBAC1B5J,YAAY,EAAE,MAAM;oBACpBhC,QAAQ,EAAE,SAAS;oBACnBmD,UAAU,EAAE,KAAK;oBACjBxD,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpB0C,GAAG,EAAE,SAAS;oBACdqJ,SAAS,EAAE;kBACb,CAAE;kBAAA1L,QAAA,gBACAtB,OAAA,CAACnB,GAAG;oBAAC0O,IAAI,EAAE;kBAAG;oBAAAhM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,UAEnB;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CACN,eAGD1B,OAAA;kBAAKM,KAAK,EAAE;oBACVU,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,YAAY;oBACxB0C,GAAG,EAAE,MAAM;oBACXC,YAAY,EAAE;kBAChB,CAAE;kBAAAtC,QAAA,gBACAtB,OAAA;oBAAKM,KAAK,EAAE;sBACViD,KAAK,EAAE,MAAM;sBACbC,MAAM,EAAE,MAAM;sBACdwB,UAAU,EAAEiD,YAAY,CAACoI,QAAQ,GAC7B,mDAAmD,GACnD,mDAAmD;sBACvDhN,YAAY,EAAE,MAAM;sBACpBrC,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE,QAAQ;sBACxB+M,UAAU,EAAE;oBACd,CAAE;oBAAA3M,QAAA,eACAtB,OAAA,CAACrB,SAAS;sBAAC4O,IAAI,EAAE,EAAG;sBAACnM,KAAK,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClC,CAAC,eAEN1B,OAAA;oBAAKM,KAAK,EAAE;sBAAE+M,IAAI,EAAE;oBAAE,CAAE;oBAAA/L,QAAA,gBACtBtB,OAAA;sBAAKM,KAAK,EAAE;wBACVU,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB0C,GAAG,EAAE,SAAS;wBACdC,YAAY,EAAE,QAAQ;wBACtB0M,QAAQ,EAAE;sBACZ,CAAE;sBAAAhP,QAAA,GACC,CAAC,MAAM;wBACN,IAAI2G,YAAY,CAACoI,QAAQ,EAAE;0BACzB,oBACErQ,OAAA;4BAAMM,KAAK,EAAE;8BACX0E,UAAU,EAAE,mDAAmD;8BAC/D5D,KAAK,EAAE,OAAO;8BACdC,QAAQ,EAAE,SAAS;8BACnBmD,UAAU,EAAE,KAAK;8BACjByI,OAAO,EAAE,iBAAiB;8BAC1B5J,YAAY,EAAE,MAAM;8BACpBiM,aAAa,EAAE,WAAW;8BAC1BC,aAAa,EAAE,OAAO;8BACtBvO,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpB0C,GAAG,EAAE;4BACP,CAAE;4BAAArC,QAAA,gBACAtB,OAAA,CAACT,aAAa;8BAACgO,IAAI,EAAE,EAAG;8BAACnM,KAAK,EAAC;4BAAO;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,SAE3C;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAEX,CAAC,MAAM;0BACL,MAAMoD,YAAY,GAAGmD,YAAY,CAACsI,aAAa,IAAI,SAAS;0BAC5D,MAAMC,aAAa,GAAG3L,gBAAgB,CAACC,YAAY,CAAC;0BACpD,MAAMuK,aAAa,GAAGmB,aAAa,CAACvL,IAAI;0BAExC,oBACEjF,OAAA;4BAAMM,KAAK,EAAE;8BACX0E,UAAU,EAAEwL,aAAa,CAACxL,UAAU;8BACpC5D,KAAK,EAAE,OAAO;8BACdC,QAAQ,EAAE,SAAS;8BACnBmD,UAAU,EAAE,KAAK;8BACjByI,OAAO,EAAE,iBAAiB;8BAC1B5J,YAAY,EAAE,MAAM;8BACpBiM,aAAa,EAAE,WAAW;8BAC1BC,aAAa,EAAE,OAAO;8BACtBvO,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpB0C,GAAG,EAAE;4BACP,CAAE;4BAAArC,QAAA,gBACAtB,OAAA,CAACqP,aAAa;8BAAC9B,IAAI,EAAE,EAAG;8BAACnM,KAAK,EAAC;4BAAO;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EACxCoD,YAAY;0BAAA;4BAAAvD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACT,CAAC;wBAEX;sBACF,CAAC,EAAE,CAAC,EAEHuG,YAAY,CAACsI,aAAa,iBACzBvQ,OAAA;wBAAMM,KAAK,EAAE;0BACX0E,UAAU,EAAE,0BAA0B;0BACtC5D,KAAK,EAAE,SAAS;0BAChBC,QAAQ,EAAE,SAAS;0BACnBmD,UAAU,EAAE,KAAK;0BACjByI,OAAO,EAAE,iBAAiB;0BAC1B5J,YAAY,EAAE;wBAChB,CAAE;wBAAA/B,QAAA,EACC2G,YAAY,CAACsI;sBAAa;wBAAAhP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvB,CACP,EAEAuG,YAAY,CAACoD,WAAW,iBACvBrL,OAAA;wBAAMM,KAAK,EAAE;0BACX0E,UAAU,EAAE,yBAAyB;0BACrC5D,KAAK,EAAE,SAAS;0BAChBC,QAAQ,EAAE,SAAS;0BACnBmD,UAAU,EAAE,KAAK;0BACjByI,OAAO,EAAE,iBAAiB;0BAC1B5J,YAAY,EAAE;wBAChB,CAAE;wBAAA/B,QAAA,GAAC,QACK,EAAC2G,YAAY,CAACoD,WAAW;sBAAA;wBAAA9J,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3B,CACP,eAED1B,OAAA;wBAAKM,KAAK,EAAE;0BACVc,KAAK,EAAE,SAAS;0BAChBC,QAAQ,EAAE;wBACZ,CAAE;wBAAAC,QAAA,EACC,IAAImK,IAAI,CAACxD,YAAY,CAACqE,UAAU,CAAC,CAAC2C,kBAAkB,CAAC,OAAO,EAAE;0BAC7DO,OAAO,EAAE,OAAO;0BAChBC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,OAAO;0BACdC,GAAG,EAAE;wBACP,CAAC;sBAAC;wBAAApO,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAEN1B,OAAA;sBAAIM,KAAK,EAAE;wBACT6M,MAAM,EAAE,cAAc;wBACtB9L,QAAQ,EAAE,SAAS;wBACnBmD,UAAU,EAAE,KAAK;wBACjBpD,KAAK,EAAE,SAAS;wBAChBgM,UAAU,EAAE;sBACd,CAAE;sBAAA9L,QAAA,EACC2G,YAAY,CAAC6C;oBAAK;sBAAAvJ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF,CAAC,eAGN1B,OAAA;oBAAKM,KAAK,EAAE;sBAAEU,OAAO,EAAE,MAAM;sBAAE2C,GAAG,EAAE;oBAAS,CAAE;oBAAArC,QAAA,gBAC7CtB,OAAA;sBACEiE,OAAO,EAAEA,CAAA,KAAMW,QAAQ,CAAC,qBAAqBqD,YAAY,CAACE,eAAe,EAAE,CAAE;sBAC7E7H,KAAK,EAAE;wBACLU,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB0C,GAAG,EAAE,SAAS;wBACdsJ,OAAO,EAAE,gBAAgB;wBACzBjI,UAAU,EAAE,wBAAwB;wBACpC4I,MAAM,EAAE,kCAAkC;wBAC1CvK,YAAY,EAAE,KAAK;wBACnBjC,KAAK,EAAE,SAAS;wBAChBC,QAAQ,EAAE,SAAS;wBACnBmD,UAAU,EAAE,KAAK;wBACjBlB,MAAM,EAAE,SAAS;wBACjBpB,UAAU,EAAE;sBACd,CAAE;sBACF3B,YAAY,EAAGuD,CAAC,IAAK;wBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,wBAAwB;sBAC7D,CAAE;sBACFxE,YAAY,EAAGsD,CAAC,IAAK;wBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,wBAAwB;sBAC7D,CAAE;sBAAA1D,QAAA,gBAEFtB,OAAA,CAACd,IAAI;wBAACqO,IAAI,EAAE;sBAAG;wBAAAhM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,QAEpB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC,eAET1B,OAAA;sBACEM,KAAK,EAAE;wBACLU,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB0C,GAAG,EAAE,SAAS;wBACdsJ,OAAO,EAAE,gBAAgB;wBACzBjI,UAAU,EAAE,yBAAyB;wBACrC4I,MAAM,EAAE,mCAAmC;wBAC3CvK,YAAY,EAAE,KAAK;wBACnBjC,KAAK,EAAE,SAAS;wBAChBC,QAAQ,EAAE,SAAS;wBACnBmD,UAAU,EAAE,KAAK;wBACjBlB,MAAM,EAAE,SAAS;wBACjBpB,UAAU,EAAE;sBACd,CAAE;sBACF3B,YAAY,EAAGuD,CAAC,IAAK;wBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,yBAAyB;sBAC9D,CAAE;sBACFxE,YAAY,EAAGsD,CAAC,IAAK;wBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,yBAAyB;sBAC9D,CAAE;sBAAA1D,QAAA,gBAEFtB,OAAA,CAACf,GAAG;wBAACsO,IAAI,EAAE;sBAAG;wBAAAhM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,aAEnB;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAQ,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGN1B,OAAA;kBAAKM,KAAK,EAAE;oBACVc,KAAK,EAAE,SAAS;oBAChBC,QAAQ,EAAE,SAAS;oBACnB+L,UAAU,EAAE,KAAK;oBACjBxJ,YAAY,EAAE;kBAChB,CAAE;kBAAAtC,QAAA,EACC2G,YAAY,CAACqC;gBAAO;kBAAA/I,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClB,CAAC,EAGLuG,YAAY,CAACmB,WAAW,IAAInB,YAAY,CAACmB,WAAW,CAAC1G,MAAM,GAAG,CAAC,iBAC9D1C,OAAA,CAACsC,YAAY;kBACXC,MAAM,EAAE0F,YAAY,CAACmB,WAAY;kBACjC5G,SAAS,EAAEyF,YAAY,CAAC6C,KAAM;kBAC9BrI,YAAY,EAAGQ,KAAK,IAAK;oBACvB;oBACAqF,OAAO,CAAC6H,GAAG,CAAC,aAAa,EAAElN,KAAK,CAAC;kBACnC;gBAAE;kBAAA1B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACF,eAGD1B,OAAA;kBAAKM,KAAK,EAAE;oBACVU,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBC,cAAc,EAAE,eAAe;oBAC/B+L,OAAO,EAAE,MAAM;oBACfjI,UAAU,EAAE,qBAAqB;oBACjC3B,YAAY,EAAE,MAAM;oBACpBO,YAAY,EAAE;kBAChB,CAAE;kBAAAtC,QAAA,gBACAtB,OAAA;oBAAKM,KAAK,EAAE;sBACVU,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB0C,GAAG,EAAE;oBACP,CAAE;oBAAArC,QAAA,gBAEAtB,OAAA;sBACEiE,OAAO,EAAEA,CAAA,KAAMsF,gBAAgB,CAACtB,YAAY,CAAE;sBAC9C3H,KAAK,EAAE;wBACLU,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB0C,GAAG,EAAE,QAAQ;wBACbqB,UAAU,EAAE,MAAM;wBAClB4I,MAAM,EAAE,MAAM;wBACdxM,KAAK,EAAE6G,YAAY,CAACuB,aAAa,GAAG,SAAS,GAAG,SAAS;wBACzDlG,MAAM,EAAE,SAAS;wBACjB2J,OAAO,EAAE,QAAQ;wBACjB5J,YAAY,EAAE,KAAK;wBACnBnB,UAAU,EAAE,eAAe;wBAC3Bb,QAAQ,EAAE,UAAU;wBACpBmD,UAAU,EAAE;sBACd,CAAE;sBACFjE,YAAY,EAAGuD,CAAC,IAAK;wBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,qBAAqB;sBAC1D,CAAE;sBACFxE,YAAY,EAAGsD,CAAC,IAAK;wBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,MAAM;sBAC3C,CAAE;sBAAA1D,QAAA,gBAEFtB,OAAA,CAAChB,KAAK;wBACJuO,IAAI,EAAE,EAAG;wBACTkD,IAAI,EAAExI,YAAY,CAACuB,aAAa,GAAG,SAAS,GAAG;sBAAO;wBAAAjI,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvD,CAAC,eACF1B,OAAA;wBAAAsB,QAAA,EAAO2G,YAAY,CAACyI,cAAc,IAAI;sBAAC;wBAAAnP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzC,CAAC,EAGRuG,YAAY,CAAC0I,cAAc,iBAC1B3Q,OAAA;sBACEiE,OAAO,EAAEA,CAAA,KAAM0B,eAAe,CAC5BD,YAAY,KAAKuC,YAAY,CAACE,eAAe,GAAG,IAAI,GAAGF,YAAY,CAACE,eACtE,CAAE;sBACF7H,KAAK,EAAE;wBACLU,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB0C,GAAG,EAAE,QAAQ;wBACbqB,UAAU,EAAE,MAAM;wBAClB4I,MAAM,EAAE,MAAM;wBACdxM,KAAK,EAAEsE,YAAY,KAAKuC,YAAY,CAACE,eAAe,GAAG,SAAS,GAAG,SAAS;wBAC5E7E,MAAM,EAAE,SAAS;wBACjB2J,OAAO,EAAE,QAAQ;wBACjB5J,YAAY,EAAE,KAAK;wBACnBnB,UAAU,EAAE,eAAe;wBAC3Bb,QAAQ,EAAE,UAAU;wBACpBmD,UAAU,EAAE;sBACd,CAAE;sBACFjE,YAAY,EAAGuD,CAAC,IAAK;wBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,qBAAqB;wBACxDlB,CAAC,CAACC,aAAa,CAACzD,KAAK,CAACc,KAAK,GAAG,SAAS;sBACzC,CAAE;sBACFZ,YAAY,EAAGsD,CAAC,IAAK;wBACnBA,CAAC,CAACC,aAAa,CAACzD,KAAK,CAAC0E,UAAU,GAAG,MAAM;wBACzClB,CAAC,CAACC,aAAa,CAACzD,KAAK,CAACc,KAAK,GAAGsE,YAAY,KAAKuC,YAAY,CAACE,eAAe,GAAG,SAAS,GAAG,SAAS;sBACrG,CAAE;sBAAA7G,QAAA,gBAEFtB,OAAA,CAACjB,aAAa;wBAACwO,IAAI,EAAE;sBAAG;wBAAAhM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC3B1B,OAAA;wBAAAsB,QAAA,EAAO2G,YAAY,CAAC2I,aAAa,IAAI;sBAAC;wBAAArP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CACT,eAGD1B,OAAA;sBAAKM,KAAK,EAAE;wBACVU,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB0C,GAAG,EAAE,QAAQ;wBACbvC,KAAK,EAAE,SAAS;wBAChBC,QAAQ,EAAE;sBACZ,CAAE;sBAAAC,QAAA,gBACAtB,OAAA,CAACf,GAAG;wBAACsO,IAAI,EAAE;sBAAG;wBAAAhM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACjB1B,OAAA;wBAAAsB,QAAA,GAAO2G,YAAY,CAAC4I,UAAU,IAAI,CAAC,EAAC,QAAM;sBAAA;wBAAAtP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGN1B,OAAA;oBAAKM,KAAK,EAAE;sBACVU,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB0C,GAAG,EAAE,MAAM;sBACXtC,QAAQ,EAAE,SAAS;sBACnBD,KAAK,EAAE;oBACT,CAAE;oBAAAE,QAAA,gBACAtB,OAAA;sBAAKM,KAAK,EAAE;wBACVU,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB0C,GAAG,EAAE;sBACP,CAAE;sBAAArC,QAAA,gBACAtB,OAAA,CAACb,KAAK;wBAACoO,IAAI,EAAE;sBAAG;wBAAAhM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnB1B,OAAA;wBAAAsB,QAAA,GAAM,YAAU,EAAC2G,YAAY,CAAC6I,cAAc,IAAI,OAAO;sBAAA;wBAAAvP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAO,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC5D,CAAC,eAEN1B,OAAA;sBAAKM,KAAK,EAAE;wBACV2M,OAAO,EAAE,gBAAgB;wBACzBjI,UAAU,EAAEiD,YAAY,CAAC8I,MAAM,KAAK,WAAW,GAC3C,wBAAwB,GACxB,0BAA0B;wBAC9B3P,KAAK,EAAE6G,YAAY,CAAC8I,MAAM,KAAK,WAAW,GAAG,SAAS,GAAG,SAAS;wBAClE1N,YAAY,EAAE,KAAK;wBACnBmB,UAAU,EAAE;sBACd,CAAE;sBAAAlD,QAAA,EACC2G,YAAY,CAAC8I;oBAAM;sBAAAxP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAGLgE,YAAY,KAAKuC,YAAY,CAACE,eAAe,IAAIF,YAAY,CAAC0I,cAAc,iBAC3E3Q,OAAA;kBAAKM,KAAK,EAAE;oBACV4N,SAAS,EAAE,MAAM;oBACjB8C,UAAU,EAAE,MAAM;oBAClB7C,SAAS,EAAE;kBACb,CAAE;kBAAA7M,QAAA,eACAtB,OAAA,CAACzB,mBAAmB;oBAClB4L,cAAc,EAAElC,YAAY,CAACE,eAAgB;oBAC7C8I,aAAa,EAAEhJ,YAAY,CAAC0I,cAAe;oBAC3CO,eAAe,EAAC;kBAAO;oBAAA3P,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA,GAxYI,gBAAgBuG,YAAY,CAACE,eAAe,EAAE;gBAAA5G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAyYhD,CACN;YAAC,gBACF,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACiD,GAAA,CA5oDID,aAAuB;EAAA,QACVtG,WAAW,EAuFLE,aAAa;AAAA;AAAA6S,GAAA,GAxFhCzM,aAAuB;AA8oD7B,eAAeA,aAAa;AAAC,IAAArC,EAAA,EAAAoC,GAAA,EAAA0M,GAAA;AAAAC,YAAA,CAAA/O,EAAA;AAAA+O,YAAA,CAAA3M,GAAA;AAAA2M,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}