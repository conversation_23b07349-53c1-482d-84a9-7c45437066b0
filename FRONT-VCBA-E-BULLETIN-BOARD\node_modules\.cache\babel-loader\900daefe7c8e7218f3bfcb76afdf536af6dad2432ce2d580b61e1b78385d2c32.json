{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  x: \"3\",\n  y: \"3\",\n  width: \"18\",\n  height: \"18\",\n  rx: \"2\",\n  key: \"h1oib\"\n}], [\"rect\", {\n  x: \"8\",\n  y: \"8\",\n  width: \"8\",\n  height: \"8\",\n  rx: \"1\",\n  key: \"z9xiuo\"\n}]];\nconst SquareSquare = createLucideIcon(\"square-square\", __iconNode);\nexport { __iconNode, SquareSquare as default };\n//# sourceMappingURL=square-square.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}