{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8\",\n  key: \"14sxne\"\n}], [\"path\", {\n  d: \"M3 3v5h5\",\n  key: \"1xhq8a\"\n}], [\"path\", {\n  d: \"M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16\",\n  key: \"1hlbsb\"\n}], [\"path\", {\n  d: \"M16 16h5v5\",\n  key: \"ccwih5\"\n}]];\nconst RefreshCcw = createLucideIcon(\"refresh-ccw\", __iconNode);\nexport { __iconNode, RefreshCcw as default };\n//# sourceMappingURL=refresh-ccw.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}