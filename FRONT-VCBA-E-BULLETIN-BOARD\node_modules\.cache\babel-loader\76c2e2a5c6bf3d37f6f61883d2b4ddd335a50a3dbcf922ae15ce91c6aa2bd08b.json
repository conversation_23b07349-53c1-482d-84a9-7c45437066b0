{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 11a9 9 0 0 1 9 9\",\n  key: \"pv89mb\"\n}], [\"path\", {\n  d: \"M4 4a16 16 0 0 1 16 16\",\n  key: \"k0647b\"\n}], [\"circle\", {\n  cx: \"5\",\n  cy: \"19\",\n  r: \"1\",\n  key: \"bfqh0e\"\n}]];\nconst Rss = createLucideIcon(\"rss\", __iconNode);\nexport { __iconNode, Rss as default };\n//# sourceMappingURL=rss.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}