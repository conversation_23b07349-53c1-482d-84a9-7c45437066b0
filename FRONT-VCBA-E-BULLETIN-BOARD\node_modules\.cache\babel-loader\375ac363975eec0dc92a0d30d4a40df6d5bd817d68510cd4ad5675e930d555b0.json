{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z\",\n  key: \"qn84l0\"\n}], [\"path\", {\n  d: \"m9.5 14.5 5-5\",\n  key: \"qviqfa\"\n}]];\nconst TicketSlash = createLucideIcon(\"ticket-slash\", __iconNode);\nexport { __iconNode, TicketSlash as default };\n//# sourceMappingURL=ticket-slash.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}