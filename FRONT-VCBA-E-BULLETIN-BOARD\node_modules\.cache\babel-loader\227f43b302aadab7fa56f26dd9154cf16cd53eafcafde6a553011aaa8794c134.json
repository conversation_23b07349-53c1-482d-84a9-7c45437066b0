{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 3v11\",\n  key: \"o3l5kj\"\n}], [\"path\", {\n  d: \"M10 9H7a1 1 0 0 1 0-6h8\",\n  key: \"1wb1nc\"\n}], [\"path\", {\n  d: \"M14 3v11\",\n  key: \"mlfb7b\"\n}], [\"path\", {\n  d: \"m18 14 4 4H2\",\n  key: \"4r8io1\"\n}], [\"path\", {\n  d: \"m22 18-4 4\",\n  key: \"1hjjrd\"\n}]];\nconst PilcrowRight = createLucideIcon(\"pilcrow-right\", __iconNode);\nexport { __iconNode, PilcrowRight as default };\n//# sourceMappingURL=pilcrow-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}