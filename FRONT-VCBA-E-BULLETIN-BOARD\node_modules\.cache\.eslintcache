[{"D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\index.tsx": "1", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\reportWebVitals.ts": "2", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\App.tsx": "3", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\AdminAuthContext.tsx": "4", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\StudentAuthContext.tsx": "5", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\ToastContext.tsx": "6", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Calendar.tsx": "7", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Settings.tsx": "8", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminNewsfeed.tsx": "9", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\StudentManagement.tsx": "10", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminDashboard.tsx": "11", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\PostManagement.tsx": "12", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentSettings.tsx": "13", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentDashboard.tsx": "14", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentNewsfeed.tsx": "15", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\index.ts": "16", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminLayout.tsx": "17", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentLayout.tsx": "18", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\index.ts": "19", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\index.ts": "20", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\Toast.tsx": "21", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\admin-auth.service.ts": "22", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\student-auth.service.ts": "23", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarService.ts": "24", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\studentService.ts": "25", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminCommentSection.tsx": "26", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\CommentSection.tsx": "27", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\FacebookImageGallery.tsx": "28", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendar.ts": "29", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\config\\constants.ts": "30", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useAnnouncements.ts": "31", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\CalendarEventModal.tsx": "32", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementViewDialog.tsx": "33", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementModal.tsx": "34", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminHeader.tsx": "35", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminSidebar.tsx": "36", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentHeader.tsx": "37", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentSidebar.tsx": "38", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ProtectedRoute.tsx": "39", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\PublicRoute.tsx": "40", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\ErrorBoundary.tsx": "41", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\index.ts": "42", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\index.ts": "43", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\api.service.ts": "44", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useComments.ts": "45", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendarImageUpload.ts": "46", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useMultipleImageUpload.ts": "47", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\MultipleImageUpload.tsx": "48", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CalendarImageUpload.tsx": "49", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CascadingCategoryDropdown.tsx": "50", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\formUtils.ts": "51", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\NotificationBell.tsx": "52", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\announcementService.ts": "53", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\commentService.ts": "54", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\auth.service.ts": "55", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationService.ts": "56", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminLogin\\AdminLogin.tsx": "57", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminRegister\\AdminRegister.tsx": "58", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\index.ts": "59", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\StudentLogin.tsx": "60", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ImageLightbox.tsx": "61"}, {"size": 554, "mtime": 1752306944742, "results": "62", "hashOfConfig": "63"}, {"size": 419, "mtime": 1751128028957, "results": "64", "hashOfConfig": "63"}, {"size": 5906, "mtime": 1752266972985, "results": "65", "hashOfConfig": "63"}, {"size": 7830, "mtime": 1752307543875, "results": "66", "hashOfConfig": "63"}, {"size": 6136, "mtime": 1751378085426, "results": "67", "hashOfConfig": "63"}, {"size": 3769, "mtime": 1751807733289, "results": "68", "hashOfConfig": "63"}, {"size": 47756, "mtime": 1752317028223, "results": "69", "hashOfConfig": "63"}, {"size": 15866, "mtime": 1751480707238, "results": "70", "hashOfConfig": "63"}, {"size": 82649, "mtime": 1752328850349, "results": "71", "hashOfConfig": "63"}, {"size": 62476, "mtime": 1752106868965, "results": "72", "hashOfConfig": "63"}, {"size": 7447, "mtime": 1752271830297, "results": "73", "hashOfConfig": "63"}, {"size": 45339, "mtime": 1752316796748, "results": "74", "hashOfConfig": "63"}, {"size": 15127, "mtime": 1751481859621, "results": "75", "hashOfConfig": "63"}, {"size": 5269, "mtime": 1751481708021, "results": "76", "hashOfConfig": "63"}, {"size": 73296, "mtime": 1752329014610, "results": "77", "hashOfConfig": "63"}, {"size": 56, "mtime": 1751129203253, "results": "78", "hashOfConfig": "63"}, {"size": 1342, "mtime": 1751155291295, "results": "79", "hashOfConfig": "63"}, {"size": 1688, "mtime": 1751208621481, "results": "80", "hashOfConfig": "63"}, {"size": 232, "mtime": 1751541103375, "results": "81", "hashOfConfig": "63"}, {"size": 103, "mtime": 1751140878593, "results": "82", "hashOfConfig": "63"}, {"size": 3996, "mtime": 1751807709103, "results": "83", "hashOfConfig": "63"}, {"size": 10798, "mtime": 1752109432199, "results": "84", "hashOfConfig": "63"}, {"size": 8102, "mtime": 1752100520664, "results": "85", "hashOfConfig": "63"}, {"size": 13886, "mtime": 1752305313186, "results": "86", "hashOfConfig": "63"}, {"size": 6186, "mtime": 1751790575203, "results": "87", "hashOfConfig": "63"}, {"size": 19731, "mtime": 1752266378135, "results": "88", "hashOfConfig": "63"}, {"size": 13372, "mtime": 1751482206518, "results": "89", "hashOfConfig": "63"}, {"size": 8585, "mtime": 1752311807991, "results": "90", "hashOfConfig": "63"}, {"size": 8980, "mtime": 1752306585350, "results": "91", "hashOfConfig": "63"}, {"size": 5764, "mtime": 1752149847136, "results": "92", "hashOfConfig": "63"}, {"size": 10792, "mtime": 1752109432212, "results": "93", "hashOfConfig": "63"}, {"size": 22941, "mtime": 1752310630400, "results": "94", "hashOfConfig": "63"}, {"size": 23944, "mtime": 1752113263732, "results": "95", "hashOfConfig": "63"}, {"size": 25874, "mtime": 1752150485699, "results": "96", "hashOfConfig": "63"}, {"size": 11500, "mtime": 1752265827596, "results": "97", "hashOfConfig": "63"}, {"size": 6146, "mtime": 1752267324453, "results": "98", "hashOfConfig": "63"}, {"size": 10202, "mtime": 1751482014980, "results": "99", "hashOfConfig": "63"}, {"size": 5776, "mtime": 1751482122441, "results": "100", "hashOfConfig": "63"}, {"size": 4237, "mtime": 1751374891872, "results": "101", "hashOfConfig": "63"}, {"size": 2236, "mtime": 1751374983369, "results": "102", "hashOfConfig": "63"}, {"size": 2063, "mtime": 1751140856772, "results": "103", "hashOfConfig": "63"}, {"size": 616, "mtime": 1751483637613, "results": "104", "hashOfConfig": "63"}, {"size": 230, "mtime": 1751371669679, "results": "105", "hashOfConfig": "63"}, {"size": 6431, "mtime": 1752085386246, "results": "106", "hashOfConfig": "63"}, {"size": 8725, "mtime": 1751171509520, "results": "107", "hashOfConfig": "63"}, {"size": 10510, "mtime": 1752310981659, "results": "108", "hashOfConfig": "63"}, {"size": 10877, "mtime": 1752092600455, "results": "109", "hashOfConfig": "63"}, {"size": 20052, "mtime": 1752311857431, "results": "110", "hashOfConfig": "63"}, {"size": 17383, "mtime": 1752311647059, "results": "111", "hashOfConfig": "63"}, {"size": 13056, "mtime": 1752091667929, "results": "112", "hashOfConfig": "63"}, {"size": 4490, "mtime": 1752089541913, "results": "113", "hashOfConfig": "63"}, {"size": 11279, "mtime": 1751536473578, "results": "114", "hashOfConfig": "63"}, {"size": 15732, "mtime": 1752118360279, "results": "115", "hashOfConfig": "63"}, {"size": 8568, "mtime": 1751168806926, "results": "116", "hashOfConfig": "63"}, {"size": 10034, "mtime": 1752100506540, "results": "117", "hashOfConfig": "63"}, {"size": 10252, "mtime": 1751483425775, "results": "118", "hashOfConfig": "63"}, {"size": 9958, "mtime": 1751375132323, "results": "119", "hashOfConfig": "63"}, {"size": 15760, "mtime": 1752103072849, "results": "120", "hashOfConfig": "63"}, {"size": 42, "mtime": 1751129053363, "results": "121", "hashOfConfig": "63"}, {"size": 9297, "mtime": 1751476824841, "results": "122", "hashOfConfig": "63"}, {"size": 10389, "mtime": 1752328691800, "results": "123", "hashOfConfig": "63"}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1oyp4vo", {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\index.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\reportWebVitals.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\App.tsx", ["307"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\AdminAuthContext.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\StudentAuthContext.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\ToastContext.tsx", ["308"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Calendar.tsx", ["309", "310", "311", "312", "313"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Settings.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminNewsfeed.tsx", ["314", "315", "316", "317", "318"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\StudentManagement.tsx", ["319", "320"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminDashboard.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\PostManagement.tsx", ["321", "322", "323"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentSettings.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentDashboard.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentNewsfeed.tsx", ["324", "325", "326", "327", "328", "329"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\index.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminLayout.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentLayout.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\index.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\index.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\Toast.tsx", ["330"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\admin-auth.service.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\student-auth.service.ts", ["331"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarService.ts", ["332"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\studentService.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminCommentSection.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\CommentSection.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\FacebookImageGallery.tsx", ["333"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendar.ts", ["334"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\config\\constants.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useAnnouncements.ts", ["335", "336", "337"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\CalendarEventModal.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementViewDialog.tsx", ["338", "339", "340"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementModal.tsx", ["341"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminHeader.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminSidebar.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentHeader.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentSidebar.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ProtectedRoute.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\PublicRoute.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\ErrorBoundary.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\index.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\index.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\api.service.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useComments.ts", ["342", "343", "344"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendarImageUpload.ts", ["345", "346", "347", "348", "349", "350"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useMultipleImageUpload.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\MultipleImageUpload.tsx", ["351", "352"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CalendarImageUpload.tsx", ["353", "354", "355"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CascadingCategoryDropdown.tsx", ["356"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\formUtils.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\NotificationBell.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\announcementService.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\commentService.ts", ["357"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\auth.service.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationService.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminLogin\\AdminLogin.tsx", ["358", "359", "360"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminRegister\\AdminRegister.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\index.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\StudentLogin.tsx", ["361", "362", "363"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ImageLightbox.tsx", ["364"], [], {"ruleId": "365", "severity": 1, "message": "366", "line": 3, "column": 10, "nodeType": "367", "messageId": "368", "endLine": 3, "endColumn": 22}, {"ruleId": "365", "severity": 1, "message": "369", "line": 2, "column": 28, "nodeType": "367", "messageId": "368", "endLine": 2, "endColumn": 38}, {"ruleId": "365", "severity": 1, "message": "370", "line": 6, "column": 55, "nodeType": "367", "messageId": "368", "endLine": 6, "endColumn": 58}, {"ruleId": "365", "severity": 1, "message": "371", "line": 6, "column": 60, "nodeType": "367", "messageId": "368", "endLine": 6, "endColumn": 66}, {"ruleId": "372", "severity": 1, "message": "373", "line": 182, "column": 6, "nodeType": "374", "endLine": 182, "endColumn": 57, "suggestions": "375"}, {"ruleId": "372", "severity": 1, "message": "376", "line": 182, "column": 7, "nodeType": "377", "endLine": 182, "endColumn": 32}, {"ruleId": "372", "severity": 1, "message": "376", "line": 182, "column": 34, "nodeType": "377", "endLine": 182, "endColumn": 56}, {"ruleId": "372", "severity": 1, "message": "378", "line": 94, "column": 6, "nodeType": "374", "endLine": 94, "endColumn": 17, "suggestions": "379"}, {"ruleId": "365", "severity": 1, "message": "380", "line": 420, "column": 10, "nodeType": "367", "messageId": "368", "endLine": 420, "endColumn": 20}, {"ruleId": "365", "severity": 1, "message": "381", "line": 421, "column": 10, "nodeType": "367", "messageId": "368", "endLine": 421, "endColumn": 27}, {"ruleId": "365", "severity": 1, "message": "382", "line": 591, "column": 9, "nodeType": "367", "messageId": "368", "endLine": 591, "endColumn": 28}, {"ruleId": "365", "severity": 1, "message": "383", "line": 663, "column": 9, "nodeType": "367", "messageId": "368", "endLine": 663, "endColumn": 24}, {"ruleId": "365", "severity": 1, "message": "384", "line": 2, "column": 57, "nodeType": "367", "messageId": "368", "endLine": 2, "endColumn": 73}, {"ruleId": "365", "severity": 1, "message": "385", "line": 17, "column": 10, "nodeType": "367", "messageId": "368", "endLine": 17, "endColumn": 23}, {"ruleId": "365", "severity": 1, "message": "386", "line": 53, "column": 19, "nodeType": "367", "messageId": "368", "endLine": 53, "endColumn": 29}, {"ruleId": "365", "severity": 1, "message": "387", "line": 83, "column": 5, "nodeType": "367", "messageId": "368", "endLine": 83, "endColumn": 10}, {"ruleId": "372", "severity": 1, "message": "388", "line": 162, "column": 6, "nodeType": "374", "endLine": 162, "endColumn": 82, "suggestions": "389"}, {"ruleId": "365", "severity": 1, "message": "390", "line": 3, "column": 10, "nodeType": "367", "messageId": "368", "endLine": 3, "endColumn": 26}, {"ruleId": "365", "severity": 1, "message": "391", "line": 4, "column": 31, "nodeType": "367", "messageId": "368", "endLine": 4, "endColumn": 46}, {"ruleId": "365", "severity": 1, "message": "392", "line": 7, "column": 15, "nodeType": "367", "messageId": "368", "endLine": 7, "endColumn": 27}, {"ruleId": "372", "severity": 1, "message": "378", "line": 92, "column": 6, "nodeType": "374", "endLine": 92, "endColumn": 17, "suggestions": "393"}, {"ruleId": "365", "severity": 1, "message": "383", "line": 663, "column": 9, "nodeType": "367", "messageId": "368", "endLine": 663, "endColumn": 24}, {"ruleId": "365", "severity": 1, "message": "394", "line": 1015, "column": 29, "nodeType": "367", "messageId": "368", "endLine": 1015, "endColumn": 42}, {"ruleId": "372", "severity": 1, "message": "395", "line": 39, "column": 6, "nodeType": "374", "endLine": 39, "endColumn": 16, "suggestions": "396"}, {"ruleId": "365", "severity": 1, "message": "397", "line": 35, "column": 32, "nodeType": "367", "messageId": "368", "endLine": 35, "endColumn": 33}, {"ruleId": "365", "severity": 1, "message": "398", "line": 3, "column": 25, "nodeType": "367", "messageId": "368", "endLine": 3, "endColumn": 37}, {"ruleId": "372", "severity": 1, "message": "378", "line": 64, "column": 6, "nodeType": "374", "endLine": 64, "endColumn": 17, "suggestions": "399"}, {"ruleId": "365", "severity": 1, "message": "400", "line": 7, "column": 3, "nodeType": "367", "messageId": "368", "endLine": 7, "endColumn": 15}, {"ruleId": "365", "severity": 1, "message": "401", "line": 3, "column": 15, "nodeType": "367", "messageId": "368", "endLine": 3, "endColumn": 26}, {"ruleId": "372", "severity": 1, "message": "402", "line": 57, "column": 6, "nodeType": "374", "endLine": 57, "endColumn": 48, "suggestions": "403"}, {"ruleId": "372", "severity": 1, "message": "404", "line": 57, "column": 7, "nodeType": "377", "endLine": 57, "endColumn": 30}, {"ruleId": "372", "severity": 1, "message": "378", "line": 65, "column": 6, "nodeType": "374", "endLine": 65, "endColumn": 17, "suggestions": "405"}, {"ruleId": "406", "severity": 1, "message": "407", "line": 248, "column": 11, "nodeType": "408", "endLine": 262, "endColumn": 13}, {"ruleId": "406", "severity": 1, "message": "407", "line": 332, "column": 19, "nodeType": "408", "endLine": 346, "endColumn": 21}, {"ruleId": "372", "severity": 1, "message": "409", "line": 166, "column": 6, "nodeType": "374", "endLine": 166, "endColumn": 113, "suggestions": "410"}, {"ruleId": "365", "severity": 1, "message": "411", "line": 7, "column": 3, "nodeType": "367", "messageId": "368", "endLine": 7, "endColumn": 17}, {"ruleId": "365", "severity": 1, "message": "412", "line": 8, "column": 3, "nodeType": "367", "messageId": "368", "endLine": 8, "endColumn": 28}, {"ruleId": "413", "severity": 1, "message": "414", "line": 273, "column": 44, "nodeType": "415", "messageId": "416", "endLine": 273, "endColumn": 98}, {"ruleId": "372", "severity": 1, "message": "417", "line": 128, "column": 6, "nodeType": "374", "endLine": 128, "endColumn": 18, "suggestions": "418"}, {"ruleId": "372", "severity": 1, "message": "419", "line": 173, "column": 6, "nodeType": "374", "endLine": 173, "endColumn": 33, "suggestions": "420"}, {"ruleId": "372", "severity": 1, "message": "419", "line": 204, "column": 6, "nodeType": "374", "endLine": 204, "endColumn": 21, "suggestions": "421"}, {"ruleId": "372", "severity": 1, "message": "419", "line": 239, "column": 6, "nodeType": "374", "endLine": 239, "endColumn": 33, "suggestions": "422"}, {"ruleId": "372", "severity": 1, "message": "423", "line": 299, "column": 6, "nodeType": "374", "endLine": 299, "endColumn": 22, "suggestions": "424"}, {"ruleId": "372", "severity": 1, "message": "425", "line": 306, "column": 6, "nodeType": "374", "endLine": 306, "endColumn": 18, "suggestions": "426"}, {"ruleId": "372", "severity": 1, "message": "378", "line": 100, "column": 6, "nodeType": "374", "endLine": 100, "endColumn": 17, "suggestions": "427"}, {"ruleId": "372", "severity": 1, "message": "428", "line": 375, "column": 6, "nodeType": "374", "endLine": 375, "endColumn": 8, "suggestions": "429"}, {"ruleId": "372", "severity": 1, "message": "378", "line": 66, "column": 6, "nodeType": "374", "endLine": 66, "endColumn": 17, "suggestions": "430"}, {"ruleId": "365", "severity": 1, "message": "431", "line": 404, "column": 9, "nodeType": "367", "messageId": "368", "endLine": 404, "endColumn": 24}, {"ruleId": "372", "severity": 1, "message": "432", "line": 423, "column": 6, "nodeType": "374", "endLine": 423, "endColumn": 8, "suggestions": "433"}, {"ruleId": "434", "severity": 1, "message": "435", "line": 249, "column": 7, "nodeType": "408", "endLine": 263, "endColumn": 8}, {"ruleId": "365", "severity": 1, "message": "436", "line": 4, "column": 27, "nodeType": "367", "messageId": "368", "endLine": 4, "endColumn": 39}, {"ruleId": "365", "severity": 1, "message": "437", "line": 2, "column": 10, "nodeType": "367", "messageId": "368", "endLine": 2, "endColumn": 14}, {"ruleId": "365", "severity": 1, "message": "438", "line": 90, "column": 9, "nodeType": "367", "messageId": "368", "endLine": 90, "endColumn": 33}, {"ruleId": "365", "severity": 1, "message": "439", "line": 94, "column": 9, "nodeType": "367", "messageId": "368", "endLine": 94, "endColumn": 26}, {"ruleId": "365", "severity": 1, "message": "437", "line": 2, "column": 10, "nodeType": "367", "messageId": "368", "endLine": 2, "endColumn": 14}, {"ruleId": "365", "severity": 1, "message": "438", "line": 105, "column": 9, "nodeType": "367", "messageId": "368", "endLine": 105, "endColumn": 33}, {"ruleId": "365", "severity": 1, "message": "440", "line": 110, "column": 9, "nodeType": "367", "messageId": "368", "endLine": 110, "endColumn": 16}, {"ruleId": "372", "severity": 1, "message": "441", "line": 54, "column": 6, "nodeType": "374", "endLine": 54, "endColumn": 23, "suggestions": "442"}, "@typescript-eslint/no-unused-vars", "'AuthProvider' is defined but never used.", "Identifier", "unusedVar", "'ToastProps' is defined but never used.", "'Eye' is defined but never used.", "'EyeOff' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useMemo has a missing dependency: 'currentDate'. Either include it or remove the dependency array.", "ArrayExpression", ["443"], "React Hook useMemo has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", "React Hook useEffect has a missing dependency: 'imageUrl'. Either include it or remove the dependency array.", ["444"], "'newComment' is assigned a value but never used.", "'submittingComment' is assigned a value but never used.", "'handleCommentSubmit' is assigned a value but never used.", "'combinedContent' is assigned a value but never used.", "'StudentsResponse' is defined but never used.", "'totalStudents' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "'error' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'filters' and 'updateFilters'. Either include them or remove the dependency array.", ["445"], "'useAnnouncements' is defined but never used.", "'calendarService' is defined but never used.", "'Announcement' is defined but never used.", ["446"], "'categoryStyle' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", ["447"], "'T' is defined but never used.", "'API_BASE_URL' is defined but never used.", ["448"], "'EventFilters' is defined but never used.", "'Subcategory' is defined but never used.", "React Hook useCallback has missing dependencies: 'filters' and 'service'. Either include them or remove the dependency array.", ["449"], "React Hook useCallback has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", ["450"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "React Hook useEffect has missing dependencies: 'announcement' and 'refreshImages'. Either include them or remove the dependency array.", ["451"], "'CommentFilters' is defined but never used.", "'PaginatedCommentsResponse' is defined but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentComment'.", "ArrowFunctionExpression", "unsafeRefs", "React Hook useCallback has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["452"], "React Hook useCallback has missing dependencies: 'onError' and 'onSuccess'. Either include them or remove the dependency array. If 'onSuccess' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["453"], ["454"], ["455"], "React Hook useCallback has a missing dependency: 'refreshImages'. Either include it or remove the dependency array.", ["456"], "React Hook useEffect has a missing dependency: 'refreshImages'. Either include it or remove the dependency array.", ["457"], ["458"], "React Hook useEffect has a missing dependency: 'images'. Either include it or remove the dependency array.", ["459"], ["460"], "'setPrimaryImage' is assigned a value but never used.", "React Hook React.useEffect has a missing dependency: 'images'. Either include it or remove the dependency array.", ["461"], "jsx-a11y/role-supports-aria-props", "The attribute aria-required is not supported by the role button.", "'ReactionType' is defined but never used.", "'Link' is defined but never used.", "'togglePasswordVisibility' is assigned a value but never used.", "'handleForceLogout' is assigned a value but never used.", "'isEmail' is assigned a value but never used.", "React Hook useCallback has missing dependencies: 'goToNext' and 'goToPrevious'. Either include them or remove the dependency array.", ["462"], {"desc": "463", "fix": "464"}, {"desc": "465", "fix": "466"}, {"desc": "467", "fix": "468"}, {"desc": "465", "fix": "469"}, {"desc": "470", "fix": "471"}, {"desc": "465", "fix": "472"}, {"desc": "473", "fix": "474"}, {"desc": "465", "fix": "475"}, {"desc": "476", "fix": "477"}, {"desc": "478", "fix": "479"}, {"desc": "480", "fix": "481"}, {"desc": "482", "fix": "483"}, {"desc": "480", "fix": "484"}, {"desc": "485", "fix": "486"}, {"desc": "487", "fix": "488"}, {"desc": "465", "fix": "489"}, {"desc": "490", "fix": "491"}, {"desc": "465", "fix": "492"}, {"desc": "490", "fix": "493"}, {"desc": "494", "fix": "495"}, "Update the dependencies array to be: [currentDate]", {"range": "496", "text": "497"}, "Update the dependencies array to be: [imagePath, imageUrl]", {"range": "498", "text": "499"}, "Update the dependencies array to be: [debouncedSearchTerm, filters, selectedCategoryId, selectedStatus, updateFilters, user.grade_level]", {"range": "500", "text": "501"}, {"range": "502", "text": "499"}, "Update the dependencies array to be: [duration, handleClose]", {"range": "503", "text": "504"}, {"range": "505", "text": "499"}, "Update the dependencies array to be: [filters, service]", {"range": "506", "text": "507"}, {"range": "508", "text": "499"}, "Update the dependencies array to be: [announcement.announcement_id, isOpen, clearImageError, clearPendingDeletes, resetModalForNewAnnouncement, announcement, refreshImages]", {"range": "509", "text": "510"}, "Update the dependencies array to be: [calendarId, onError]", {"range": "511", "text": "512"}, "Update the dependencies array to be: [calendarId, onError, onSuccess, refreshImages]", {"range": "513", "text": "514"}, "Update the dependencies array to be: [onError, onSuccess, refreshImages]", {"range": "515", "text": "516"}, {"range": "517", "text": "514"}, "Update the dependencies array to be: [pendingDeletes, refreshImages]", {"range": "518", "text": "519"}, "Update the dependencies array to be: [calendarId, refreshImages]", {"range": "520", "text": "521"}, {"range": "522", "text": "499"}, "Update the dependencies array to be: [images]", {"range": "523", "text": "524"}, {"range": "525", "text": "499"}, {"range": "526", "text": "524"}, "Update the dependencies array to be: [goToNext, goToPrevious, isOpen, onClose]", {"range": "527", "text": "528"}, [6563, 6614], "[currentDate]", [2619, 2630], "[imagePath, imageUrl]", [4777, 4853], "[debouncedSearchTerm, filters, selectedCategoryId, selectedStatus, updateFilters, user.grade_level]", [2537, 2548], [900, 910], "[duration, handleClose]", [1735, 1746], [1878, 1920], "[filters, service]", [1909, 1920], [5746, 5853], "[announcement.announcement_id, isOpen, clearImageError, clearPendingDeletes, resetModalForNewAnnouncement, announcement, refreshImages]", [4049, 4061], "[calendarId, onError]", [5454, 5481], "[calendarId, onError, onSuccess, refreshImages]", [6495, 6510], "[onError, onSuccess, refreshImages]", [7671, 7698], [9871, 9887], "[pendingDeletes, refreshImages]", [10076, 10088], "[calendarId, refreshImages]", [2737, 2748], [10512, 10514], "[images]", [1825, 1836], [12149, 12151], [1329, 1346], "[goTo<PERSON><PERSON>t, goToPrevious, isOpen, onClose]"]