[{"D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\index.tsx": "1", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\reportWebVitals.ts": "2", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\App.tsx": "3", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\AdminAuthContext.tsx": "4", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\StudentAuthContext.tsx": "5", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\ToastContext.tsx": "6", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Calendar.tsx": "7", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Settings.tsx": "8", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminNewsfeed.tsx": "9", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\StudentManagement.tsx": "10", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminDashboard.tsx": "11", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\PostManagement.tsx": "12", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentSettings.tsx": "13", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentDashboard.tsx": "14", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentNewsfeed.tsx": "15", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\index.ts": "16", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminLayout.tsx": "17", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentLayout.tsx": "18", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\index.ts": "19", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\index.ts": "20", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\Toast.tsx": "21", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\admin-auth.service.ts": "22", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\student-auth.service.ts": "23", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarService.ts": "24", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\studentService.ts": "25", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminCommentSection.tsx": "26", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\CommentSection.tsx": "27", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\FacebookImageGallery.tsx": "28", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendar.ts": "29", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\config\\constants.ts": "30", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useAnnouncements.ts": "31", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\CalendarEventModal.tsx": "32", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementViewDialog.tsx": "33", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementModal.tsx": "34", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminHeader.tsx": "35", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminSidebar.tsx": "36", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentHeader.tsx": "37", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentSidebar.tsx": "38", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ProtectedRoute.tsx": "39", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\PublicRoute.tsx": "40", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\ErrorBoundary.tsx": "41", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\index.ts": "42", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\index.ts": "43", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\api.service.ts": "44", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useComments.ts": "45", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendarImageUpload.ts": "46", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useMultipleImageUpload.ts": "47", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\MultipleImageUpload.tsx": "48", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CalendarImageUpload.tsx": "49", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CascadingCategoryDropdown.tsx": "50", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\formUtils.ts": "51", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\NotificationBell.tsx": "52", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\announcementService.ts": "53", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\commentService.ts": "54", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\auth.service.ts": "55", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationService.ts": "56", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminLogin\\AdminLogin.tsx": "57", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminRegister\\AdminRegister.tsx": "58", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\index.ts": "59", "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\StudentLogin.tsx": "60"}, {"size": 554, "mtime": 1752306944742, "results": "61", "hashOfConfig": "62"}, {"size": 419, "mtime": 1751128028957, "results": "63", "hashOfConfig": "62"}, {"size": 5906, "mtime": 1752266972985, "results": "64", "hashOfConfig": "62"}, {"size": 7830, "mtime": 1752307543875, "results": "65", "hashOfConfig": "62"}, {"size": 6136, "mtime": 1751378085426, "results": "66", "hashOfConfig": "62"}, {"size": 3769, "mtime": 1751807733289, "results": "67", "hashOfConfig": "62"}, {"size": 47756, "mtime": 1752317028223, "results": "68", "hashOfConfig": "62"}, {"size": 15866, "mtime": 1751480707238, "results": "69", "hashOfConfig": "62"}, {"size": 81819, "mtime": 1752328494056, "results": "70", "hashOfConfig": "62"}, {"size": 62476, "mtime": 1752106868965, "results": "71", "hashOfConfig": "62"}, {"size": 7447, "mtime": 1752271830297, "results": "72", "hashOfConfig": "62"}, {"size": 45339, "mtime": 1752316796748, "results": "73", "hashOfConfig": "62"}, {"size": 15127, "mtime": 1751481859621, "results": "74", "hashOfConfig": "62"}, {"size": 5269, "mtime": 1751481708021, "results": "75", "hashOfConfig": "62"}, {"size": 72382, "mtime": 1752328273713, "results": "76", "hashOfConfig": "62"}, {"size": 56, "mtime": 1751129203253, "results": "77", "hashOfConfig": "62"}, {"size": 1342, "mtime": 1751155291295, "results": "78", "hashOfConfig": "62"}, {"size": 1688, "mtime": 1751208621481, "results": "79", "hashOfConfig": "62"}, {"size": 232, "mtime": 1751541103375, "results": "80", "hashOfConfig": "62"}, {"size": 103, "mtime": 1751140878593, "results": "81", "hashOfConfig": "62"}, {"size": 3996, "mtime": 1751807709103, "results": "82", "hashOfConfig": "62"}, {"size": 10798, "mtime": 1752109432199, "results": "83", "hashOfConfig": "62"}, {"size": 8102, "mtime": 1752100520664, "results": "84", "hashOfConfig": "62"}, {"size": 13886, "mtime": 1752305313186, "results": "85", "hashOfConfig": "62"}, {"size": 6186, "mtime": 1751790575203, "results": "86", "hashOfConfig": "62"}, {"size": 19731, "mtime": 1752266378135, "results": "87", "hashOfConfig": "62"}, {"size": 13372, "mtime": 1751482206518, "results": "88", "hashOfConfig": "62"}, {"size": 8585, "mtime": 1752311807991, "results": "89", "hashOfConfig": "62"}, {"size": 8980, "mtime": 1752306585350, "results": "90", "hashOfConfig": "62"}, {"size": 5764, "mtime": 1752149847136, "results": "91", "hashOfConfig": "62"}, {"size": 10792, "mtime": 1752109432212, "results": "92", "hashOfConfig": "62"}, {"size": 22941, "mtime": 1752310630400, "results": "93", "hashOfConfig": "62"}, {"size": 23944, "mtime": 1752113263732, "results": "94", "hashOfConfig": "62"}, {"size": 25874, "mtime": 1752150485699, "results": "95", "hashOfConfig": "62"}, {"size": 11500, "mtime": 1752265827596, "results": "96", "hashOfConfig": "62"}, {"size": 6146, "mtime": 1752267324453, "results": "97", "hashOfConfig": "62"}, {"size": 10202, "mtime": 1751482014980, "results": "98", "hashOfConfig": "62"}, {"size": 5776, "mtime": 1751482122441, "results": "99", "hashOfConfig": "62"}, {"size": 4237, "mtime": 1751374891872, "results": "100", "hashOfConfig": "62"}, {"size": 2236, "mtime": 1751374983369, "results": "101", "hashOfConfig": "62"}, {"size": 2063, "mtime": 1751140856772, "results": "102", "hashOfConfig": "62"}, {"size": 616, "mtime": 1751483637613, "results": "103", "hashOfConfig": "62"}, {"size": 230, "mtime": 1751371669679, "results": "104", "hashOfConfig": "62"}, {"size": 6431, "mtime": 1752085386246, "results": "105", "hashOfConfig": "62"}, {"size": 8725, "mtime": 1751171509520, "results": "106", "hashOfConfig": "62"}, {"size": 10510, "mtime": 1752310981659, "results": "107", "hashOfConfig": "62"}, {"size": 10877, "mtime": 1752092600455, "results": "108", "hashOfConfig": "62"}, {"size": 20052, "mtime": 1752311857431, "results": "109", "hashOfConfig": "62"}, {"size": 17383, "mtime": 1752311647059, "results": "110", "hashOfConfig": "62"}, {"size": 13056, "mtime": 1752091667929, "results": "111", "hashOfConfig": "62"}, {"size": 4490, "mtime": 1752089541913, "results": "112", "hashOfConfig": "62"}, {"size": 11279, "mtime": 1751536473578, "results": "113", "hashOfConfig": "62"}, {"size": 15732, "mtime": 1752118360279, "results": "114", "hashOfConfig": "62"}, {"size": 8568, "mtime": 1751168806926, "results": "115", "hashOfConfig": "62"}, {"size": 10034, "mtime": 1752100506540, "results": "116", "hashOfConfig": "62"}, {"size": 10252, "mtime": 1751483425775, "results": "117", "hashOfConfig": "62"}, {"size": 9958, "mtime": 1751375132323, "results": "118", "hashOfConfig": "62"}, {"size": 15760, "mtime": 1752103072849, "results": "119", "hashOfConfig": "62"}, {"size": 42, "mtime": 1751129053363, "results": "120", "hashOfConfig": "62"}, {"size": 9297, "mtime": 1751476824841, "results": "121", "hashOfConfig": "62"}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1oyp4vo", {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "137", "messages": "138", "suppressedMessages": "139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "140", "messages": "141", "suppressedMessages": "142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "143", "messages": "144", "suppressedMessages": "145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "146", "messages": "147", "suppressedMessages": "148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "149", "messages": "150", "suppressedMessages": "151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "152", "messages": "153", "suppressedMessages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "155", "messages": "156", "suppressedMessages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "158", "messages": "159", "suppressedMessages": "160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "161", "messages": "162", "suppressedMessages": "163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\index.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\reportWebVitals.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\App.tsx", ["302"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\AdminAuthContext.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\StudentAuthContext.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\contexts\\ToastContext.tsx", ["303"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Calendar.tsx", ["304", "305", "306", "307", "308"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\Settings.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminNewsfeed.tsx", ["309", "310", "311", "312", "313"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\StudentManagement.tsx", ["314", "315"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\AdminDashboard.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\admin\\PostManagement.tsx", ["316", "317", "318"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentSettings.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentDashboard.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\student\\StudentNewsfeed.tsx", ["319", "320", "321", "322", "323", "324"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\index.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminLayout.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentLayout.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\index.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\index.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\Toast.tsx", ["325"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\admin-auth.service.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\student-auth.service.ts", ["326"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\calendarService.ts", ["327"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\studentService.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\AdminCommentSection.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\CommentSection.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\FacebookImageGallery.tsx", ["328"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendar.ts", ["329"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\config\\constants.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useAnnouncements.ts", ["330", "331", "332"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\CalendarEventModal.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementViewDialog.tsx", ["333", "334", "335"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\modals\\AnnouncementModal.tsx", ["336"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminHeader.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\layout\\AdminSidebar.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentHeader.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\student\\layout\\StudentSidebar.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\ProtectedRoute.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\PublicRoute.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\ErrorBoundary\\ErrorBoundary.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\index.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\index.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\api.service.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useComments.ts", ["337", "338", "339"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useCalendarImageUpload.ts", ["340", "341", "342", "343", "344", "345"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\hooks\\useMultipleImageUpload.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\MultipleImageUpload.tsx", ["346", "347"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\CalendarImageUpload.tsx", ["348", "349", "350"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\common\\CascadingCategoryDropdown.tsx", ["351"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\utils\\formUtils.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\components\\admin\\NotificationBell.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\announcementService.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\commentService.ts", ["352"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\auth.service.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\services\\notificationService.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminLogin\\AdminLogin.tsx", ["353", "354", "355"], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\AdminRegister\\AdminRegister.tsx", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\index.ts", [], [], "D:\\capstone-e-bulletin-reactjs-proj\\FRONT-VCBA-E-BULLETIN-BOARD\\src\\pages\\auth\\StudentLogin\\StudentLogin.tsx", ["356", "357", "358"], [], {"ruleId": "359", "severity": 1, "message": "360", "line": 3, "column": 10, "nodeType": "361", "messageId": "362", "endLine": 3, "endColumn": 22}, {"ruleId": "359", "severity": 1, "message": "363", "line": 2, "column": 28, "nodeType": "361", "messageId": "362", "endLine": 2, "endColumn": 38}, {"ruleId": "359", "severity": 1, "message": "364", "line": 6, "column": 55, "nodeType": "361", "messageId": "362", "endLine": 6, "endColumn": 58}, {"ruleId": "359", "severity": 1, "message": "365", "line": 6, "column": 60, "nodeType": "361", "messageId": "362", "endLine": 6, "endColumn": 66}, {"ruleId": "366", "severity": 1, "message": "367", "line": 182, "column": 6, "nodeType": "368", "endLine": 182, "endColumn": 57, "suggestions": "369"}, {"ruleId": "366", "severity": 1, "message": "370", "line": 182, "column": 7, "nodeType": "371", "endLine": 182, "endColumn": 32}, {"ruleId": "366", "severity": 1, "message": "370", "line": 182, "column": 34, "nodeType": "371", "endLine": 182, "endColumn": 56}, {"ruleId": "366", "severity": 1, "message": "372", "line": 93, "column": 6, "nodeType": "368", "endLine": 93, "endColumn": 17, "suggestions": "373"}, {"ruleId": "359", "severity": 1, "message": "374", "line": 411, "column": 10, "nodeType": "361", "messageId": "362", "endLine": 411, "endColumn": 20}, {"ruleId": "359", "severity": 1, "message": "375", "line": 412, "column": 10, "nodeType": "361", "messageId": "362", "endLine": 412, "endColumn": 27}, {"ruleId": "359", "severity": 1, "message": "376", "line": 577, "column": 9, "nodeType": "361", "messageId": "362", "endLine": 577, "endColumn": 28}, {"ruleId": "359", "severity": 1, "message": "377", "line": 649, "column": 9, "nodeType": "361", "messageId": "362", "endLine": 649, "endColumn": 24}, {"ruleId": "359", "severity": 1, "message": "378", "line": 2, "column": 57, "nodeType": "361", "messageId": "362", "endLine": 2, "endColumn": 73}, {"ruleId": "359", "severity": 1, "message": "379", "line": 17, "column": 10, "nodeType": "361", "messageId": "362", "endLine": 17, "endColumn": 23}, {"ruleId": "359", "severity": 1, "message": "380", "line": 53, "column": 19, "nodeType": "361", "messageId": "362", "endLine": 53, "endColumn": 29}, {"ruleId": "359", "severity": 1, "message": "381", "line": 83, "column": 5, "nodeType": "361", "messageId": "362", "endLine": 83, "endColumn": 10}, {"ruleId": "366", "severity": 1, "message": "382", "line": 162, "column": 6, "nodeType": "368", "endLine": 162, "endColumn": 82, "suggestions": "383"}, {"ruleId": "359", "severity": 1, "message": "384", "line": 3, "column": 10, "nodeType": "361", "messageId": "362", "endLine": 3, "endColumn": 26}, {"ruleId": "359", "severity": 1, "message": "385", "line": 4, "column": 31, "nodeType": "361", "messageId": "362", "endLine": 4, "endColumn": 46}, {"ruleId": "359", "severity": 1, "message": "386", "line": 6, "column": 15, "nodeType": "361", "messageId": "362", "endLine": 6, "endColumn": 27}, {"ruleId": "366", "severity": 1, "message": "372", "line": 91, "column": 6, "nodeType": "368", "endLine": 91, "endColumn": 17, "suggestions": "387"}, {"ruleId": "359", "severity": 1, "message": "377", "line": 650, "column": 9, "nodeType": "361", "messageId": "362", "endLine": 650, "endColumn": 24}, {"ruleId": "359", "severity": 1, "message": "388", "line": 1002, "column": 29, "nodeType": "361", "messageId": "362", "endLine": 1002, "endColumn": 42}, {"ruleId": "366", "severity": 1, "message": "389", "line": 39, "column": 6, "nodeType": "368", "endLine": 39, "endColumn": 16, "suggestions": "390"}, {"ruleId": "359", "severity": 1, "message": "391", "line": 35, "column": 32, "nodeType": "361", "messageId": "362", "endLine": 35, "endColumn": 33}, {"ruleId": "359", "severity": 1, "message": "392", "line": 3, "column": 25, "nodeType": "361", "messageId": "362", "endLine": 3, "endColumn": 37}, {"ruleId": "366", "severity": 1, "message": "372", "line": 64, "column": 6, "nodeType": "368", "endLine": 64, "endColumn": 17, "suggestions": "393"}, {"ruleId": "359", "severity": 1, "message": "394", "line": 7, "column": 3, "nodeType": "361", "messageId": "362", "endLine": 7, "endColumn": 15}, {"ruleId": "359", "severity": 1, "message": "395", "line": 3, "column": 15, "nodeType": "361", "messageId": "362", "endLine": 3, "endColumn": 26}, {"ruleId": "366", "severity": 1, "message": "396", "line": 57, "column": 6, "nodeType": "368", "endLine": 57, "endColumn": 48, "suggestions": "397"}, {"ruleId": "366", "severity": 1, "message": "398", "line": 57, "column": 7, "nodeType": "371", "endLine": 57, "endColumn": 30}, {"ruleId": "366", "severity": 1, "message": "372", "line": 65, "column": 6, "nodeType": "368", "endLine": 65, "endColumn": 17, "suggestions": "399"}, {"ruleId": "400", "severity": 1, "message": "401", "line": 248, "column": 11, "nodeType": "402", "endLine": 262, "endColumn": 13}, {"ruleId": "400", "severity": 1, "message": "401", "line": 332, "column": 19, "nodeType": "402", "endLine": 346, "endColumn": 21}, {"ruleId": "366", "severity": 1, "message": "403", "line": 166, "column": 6, "nodeType": "368", "endLine": 166, "endColumn": 113, "suggestions": "404"}, {"ruleId": "359", "severity": 1, "message": "405", "line": 7, "column": 3, "nodeType": "361", "messageId": "362", "endLine": 7, "endColumn": 17}, {"ruleId": "359", "severity": 1, "message": "406", "line": 8, "column": 3, "nodeType": "361", "messageId": "362", "endLine": 8, "endColumn": 28}, {"ruleId": "407", "severity": 1, "message": "408", "line": 273, "column": 44, "nodeType": "409", "messageId": "410", "endLine": 273, "endColumn": 98}, {"ruleId": "366", "severity": 1, "message": "411", "line": 128, "column": 6, "nodeType": "368", "endLine": 128, "endColumn": 18, "suggestions": "412"}, {"ruleId": "366", "severity": 1, "message": "413", "line": 173, "column": 6, "nodeType": "368", "endLine": 173, "endColumn": 33, "suggestions": "414"}, {"ruleId": "366", "severity": 1, "message": "413", "line": 204, "column": 6, "nodeType": "368", "endLine": 204, "endColumn": 21, "suggestions": "415"}, {"ruleId": "366", "severity": 1, "message": "413", "line": 239, "column": 6, "nodeType": "368", "endLine": 239, "endColumn": 33, "suggestions": "416"}, {"ruleId": "366", "severity": 1, "message": "417", "line": 299, "column": 6, "nodeType": "368", "endLine": 299, "endColumn": 22, "suggestions": "418"}, {"ruleId": "366", "severity": 1, "message": "419", "line": 306, "column": 6, "nodeType": "368", "endLine": 306, "endColumn": 18, "suggestions": "420"}, {"ruleId": "366", "severity": 1, "message": "372", "line": 100, "column": 6, "nodeType": "368", "endLine": 100, "endColumn": 17, "suggestions": "421"}, {"ruleId": "366", "severity": 1, "message": "422", "line": 375, "column": 6, "nodeType": "368", "endLine": 375, "endColumn": 8, "suggestions": "423"}, {"ruleId": "366", "severity": 1, "message": "372", "line": 66, "column": 6, "nodeType": "368", "endLine": 66, "endColumn": 17, "suggestions": "424"}, {"ruleId": "359", "severity": 1, "message": "425", "line": 404, "column": 9, "nodeType": "361", "messageId": "362", "endLine": 404, "endColumn": 24}, {"ruleId": "366", "severity": 1, "message": "426", "line": 423, "column": 6, "nodeType": "368", "endLine": 423, "endColumn": 8, "suggestions": "427"}, {"ruleId": "428", "severity": 1, "message": "429", "line": 249, "column": 7, "nodeType": "402", "endLine": 263, "endColumn": 8}, {"ruleId": "359", "severity": 1, "message": "430", "line": 4, "column": 27, "nodeType": "361", "messageId": "362", "endLine": 4, "endColumn": 39}, {"ruleId": "359", "severity": 1, "message": "431", "line": 2, "column": 10, "nodeType": "361", "messageId": "362", "endLine": 2, "endColumn": 14}, {"ruleId": "359", "severity": 1, "message": "432", "line": 90, "column": 9, "nodeType": "361", "messageId": "362", "endLine": 90, "endColumn": 33}, {"ruleId": "359", "severity": 1, "message": "433", "line": 94, "column": 9, "nodeType": "361", "messageId": "362", "endLine": 94, "endColumn": 26}, {"ruleId": "359", "severity": 1, "message": "431", "line": 2, "column": 10, "nodeType": "361", "messageId": "362", "endLine": 2, "endColumn": 14}, {"ruleId": "359", "severity": 1, "message": "432", "line": 105, "column": 9, "nodeType": "361", "messageId": "362", "endLine": 105, "endColumn": 33}, {"ruleId": "359", "severity": 1, "message": "434", "line": 110, "column": 9, "nodeType": "361", "messageId": "362", "endLine": 110, "endColumn": 16}, "@typescript-eslint/no-unused-vars", "'AuthProvider' is defined but never used.", "Identifier", "unusedVar", "'ToastProps' is defined but never used.", "'Eye' is defined but never used.", "'EyeOff' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useMemo has a missing dependency: 'currentDate'. Either include it or remove the dependency array.", "ArrayExpression", ["435"], "React Hook useMemo has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", "CallExpression", "React Hook useEffect has a missing dependency: 'imageUrl'. Either include it or remove the dependency array.", ["436"], "'newComment' is assigned a value but never used.", "'submittingComment' is assigned a value but never used.", "'handleCommentSubmit' is assigned a value but never used.", "'combinedContent' is assigned a value but never used.", "'StudentsResponse' is defined but never used.", "'totalStudents' is assigned a value but never used.", "'setFilters' is assigned a value but never used.", "'error' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'filters' and 'updateFilters'. Either include them or remove the dependency array.", ["437"], "'useAnnouncements' is defined but never used.", "'calendarService' is defined but never used.", "'Announcement' is defined but never used.", ["438"], "'categoryStyle' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", ["439"], "'T' is defined but never used.", "'API_BASE_URL' is defined but never used.", ["440"], "'EventFilters' is defined but never used.", "'Subcategory' is defined but never used.", "React Hook useCallback has missing dependencies: 'filters' and 'service'. Either include them or remove the dependency array.", ["441"], "React Hook useCallback has a complex expression in the dependency array. Extract it to a separate variable so it can be statically checked.", ["442"], "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "React Hook useEffect has missing dependencies: 'announcement' and 'refreshImages'. Either include them or remove the dependency array.", ["443"], "'CommentFilters' is defined but never used.", "'PaginatedCommentsResponse' is defined but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'currentComment'.", "ArrowFunctionExpression", "unsafeRefs", "React Hook useCallback has a missing dependency: 'onError'. Either include it or remove the dependency array. If 'onError' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["444"], "React Hook useCallback has missing dependencies: 'onError' and 'onSuccess'. Either include them or remove the dependency array. If 'onSuccess' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["445"], ["446"], ["447"], "React Hook useCallback has a missing dependency: 'refreshImages'. Either include it or remove the dependency array.", ["448"], "React Hook useEffect has a missing dependency: 'refreshImages'. Either include it or remove the dependency array.", ["449"], ["450"], "React Hook useEffect has a missing dependency: 'images'. Either include it or remove the dependency array.", ["451"], ["452"], "'setPrimaryImage' is assigned a value but never used.", "React Hook React.useEffect has a missing dependency: 'images'. Either include it or remove the dependency array.", ["453"], "jsx-a11y/role-supports-aria-props", "The attribute aria-required is not supported by the role button.", "'ReactionType' is defined but never used.", "'Link' is defined but never used.", "'togglePasswordVisibility' is assigned a value but never used.", "'handleForceLogout' is assigned a value but never used.", "'isEmail' is assigned a value but never used.", {"desc": "454", "fix": "455"}, {"desc": "456", "fix": "457"}, {"desc": "458", "fix": "459"}, {"desc": "456", "fix": "460"}, {"desc": "461", "fix": "462"}, {"desc": "456", "fix": "463"}, {"desc": "464", "fix": "465"}, {"desc": "456", "fix": "466"}, {"desc": "467", "fix": "468"}, {"desc": "469", "fix": "470"}, {"desc": "471", "fix": "472"}, {"desc": "473", "fix": "474"}, {"desc": "471", "fix": "475"}, {"desc": "476", "fix": "477"}, {"desc": "478", "fix": "479"}, {"desc": "456", "fix": "480"}, {"desc": "481", "fix": "482"}, {"desc": "456", "fix": "483"}, {"desc": "481", "fix": "484"}, "Update the dependencies array to be: [currentDate]", {"range": "485", "text": "486"}, "Update the dependencies array to be: [imagePath, imageUrl]", {"range": "487", "text": "488"}, "Update the dependencies array to be: [debouncedSearchTerm, filters, selectedCategoryId, selectedStatus, updateFilters, user.grade_level]", {"range": "489", "text": "490"}, {"range": "491", "text": "488"}, "Update the dependencies array to be: [duration, handleClose]", {"range": "492", "text": "493"}, {"range": "494", "text": "488"}, "Update the dependencies array to be: [filters, service]", {"range": "495", "text": "496"}, {"range": "497", "text": "488"}, "Update the dependencies array to be: [announcement.announcement_id, isOpen, clearImageError, clearPendingDeletes, resetModalForNewAnnouncement, announcement, refreshImages]", {"range": "498", "text": "499"}, "Update the dependencies array to be: [calendarId, onError]", {"range": "500", "text": "501"}, "Update the dependencies array to be: [calendarId, onError, onSuccess, refreshImages]", {"range": "502", "text": "503"}, "Update the dependencies array to be: [onError, onSuccess, refreshImages]", {"range": "504", "text": "505"}, {"range": "506", "text": "503"}, "Update the dependencies array to be: [pendingDeletes, refreshImages]", {"range": "507", "text": "508"}, "Update the dependencies array to be: [calendarId, refreshImages]", {"range": "509", "text": "510"}, {"range": "511", "text": "488"}, "Update the dependencies array to be: [images]", {"range": "512", "text": "513"}, {"range": "514", "text": "488"}, {"range": "515", "text": "513"}, [6563, 6614], "[currentDate]", [2552, 2563], "[imagePath, imageUrl]", [4777, 4853], "[debouncedSearchTerm, filters, selectedCategoryId, selectedStatus, updateFilters, user.grade_level]", [2470, 2481], [900, 910], "[duration, handleClose]", [1735, 1746], [1878, 1920], "[filters, service]", [1909, 1920], [5746, 5853], "[announcement.announcement_id, isOpen, clearImageError, clearPendingDeletes, resetModalForNewAnnouncement, announcement, refreshImages]", [4049, 4061], "[calendarId, onError]", [5454, 5481], "[calendarId, onError, onSuccess, refreshImages]", [6495, 6510], "[onError, onSuccess, refreshImages]", [7671, 7698], [9871, 9887], "[pendingDeletes, refreshImages]", [10076, 10088], "[calendarId, refreshImages]", [2737, 2748], [10512, 10514], "[images]", [1825, 1836], [12149, 12151]]