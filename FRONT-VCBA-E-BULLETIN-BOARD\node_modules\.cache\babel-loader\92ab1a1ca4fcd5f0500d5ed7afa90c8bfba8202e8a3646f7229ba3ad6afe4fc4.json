{"ast": null, "code": "var _jsxFileName = \"D:\\\\capstone-e-bulletin-reactjs-proj\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\auth\\\\StudentLogin\\\\StudentLogin.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useStudentAuth } from '../../../contexts/StudentAuthContext';\nimport './StudentLogin.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StudentLogin = () => {\n  _s();\n  var _location$state, _location$state$from;\n  const navigate = useNavigate();\n  const location = useLocation();\n  const {\n    login,\n    error,\n    isLoading,\n    clearError\n  } = useStudentAuth();\n  const [formData, setFormData] = useState({\n    identifier: '',\n    password: '',\n    remember: false\n  });\n  const [formErrors, setFormErrors] = useState({});\n  const [showPassword, setShowPassword] = useState(false);\n\n  // Get the intended destination or default to student newsfeed (landing page)\n  const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || '/student/newsfeed';\n\n  // Handle input changes\n  const handleInputChange = e => {\n    const {\n      name,\n      value,\n      type,\n      checked\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n\n    // Clear field error when user starts typing\n    if (formErrors[name]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: undefined\n      }));\n    }\n\n    // Clear global error\n    if (error) {\n      clearError();\n    }\n  };\n\n  // Validate form\n  const validateForm = () => {\n    const errors = {};\n\n    // Identifier validation (email or student number)\n    if (!formData.identifier.trim()) {\n      errors.identifier = 'Email or student number is required';\n    }\n\n    // Password validation\n    if (!formData.password) {\n      errors.password = 'Password is required';\n    }\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  // Handle form submission\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateForm()) {\n      return;\n    }\n    try {\n      console.log('🔐 StudentLogin - Attempting login with:', {\n        identifier: formData.identifier,\n        userType: 'student',\n        redirectTo: from\n      });\n      await login({\n        email: formData.identifier,\n        // Backend handles both email and student number\n        password: formData.password,\n        userType: 'student'\n      });\n      console.log('✅ StudentLogin - Login successful, redirecting to:', from);\n      // Redirect to intended destination\n      navigate(from, {\n        replace: true\n      });\n    } catch (error) {\n      // Error is handled by the auth context\n      console.error('❌ StudentLogin - Login failed:', error);\n    }\n  };\n\n  // Toggle password visibility\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n\n  // Determine if input looks like email or student number\n  const isEmail = formData.identifier.includes('@');\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"student-login\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"student-login__container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"student-login__form-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"student-login__form-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"student-login__form-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/logo/vcba1.png\",\n              alt: \"VCBA Logo\",\n              className: \"student-login__form-logo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"student-login__form-title\",\n              children: \"STUDENT LOGIN\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"student-login__form-subtitle\",\n              children: \"Villamor College of Business and Arts, Inc.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"student-login__error\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              width: \"20\",\n              height: \"20\",\n              viewBox: \"0 0 24 24\",\n              fill: \"none\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                d: \"M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\",\n                stroke: \"currentColor\",\n                strokeWidth: \"2\",\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 17\n            }, this), error]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"student-login__form\",\n            noValidate: true,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"student-login__form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"identifier\",\n                className: \"student-login__label\",\n                children: \"Email or Student Number\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 142,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                id: \"identifier\",\n                name: \"identifier\",\n                value: formData.identifier,\n                onChange: handleInputChange,\n                placeholder: \"Enter your email or student number\",\n                className: `student-login__input ${formErrors.identifier ? 'error' : ''}`,\n                disabled: isLoading,\n                autoComplete: \"username\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 143,\n                columnNumber: 17\n              }, this), formErrors.identifier && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"student-login__error-text\",\n                children: formErrors.identifier\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 43\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"student-login__form-group\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"password\",\n                className: \"student-login__label\",\n                children: \"Password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 159,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: showPassword ? 'text' : 'password',\n                id: \"password\",\n                name: \"password\",\n                value: formData.password,\n                onChange: handleInputChange,\n                placeholder: \"Enter your password\",\n                className: `student-login__input ${formErrors.password ? 'error' : ''}`,\n                disabled: isLoading,\n                autoComplete: \"current-password\",\n                required: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this), formErrors.password && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"student-login__error-text\",\n                children: formErrors.password\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 41\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"student-login__remember\",\n              children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"checkbox\",\n                id: \"remember\",\n                name: \"remember\",\n                checked: formData.remember,\n                onChange: handleInputChange,\n                className: \"student-login__checkbox\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"remember\",\n                className: \"student-login__remember-label\",\n                children: \"Remember me\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              className: \"student-login__submit-btn\",\n              disabled: isLoading,\n              children: isLoading ? 'Signing in...' : 'Sign In'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"student-login__info-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"student-login__info-content\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"student-login__school-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"/logo/ebb1.png\",\n              alt: \"E-Bulletin Board Logo\",\n              className: \"student-login__school-logo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"student-login__school-name\",\n              children: \"VCBA E-BULLETIN BOARD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"student-login__school-description\",\n              children: \"Villamor College of Business and Arts, Inc.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"student-login__features\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"student-login__feature\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"student-login__feature-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"/icons/megaphone.png\",\n                    alt: \"Categorized Contents\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 225,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 224,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"student-login__feature-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Categorized Contents\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 228,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Organized announcements by departments, clubs, events, and more\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 229,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 223,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"student-login__feature\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"student-login__feature-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"/icons/message.png\",\n                    alt: \"Centralized Platform\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 235,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"student-login__feature-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"Centralized Platform\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 238,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"All school announcements in one place \\u2014 accessible anytime, anywhere\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"student-login__feature\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"student-login__feature-icon\",\n                  children: /*#__PURE__*/_jsxDEV(\"img\", {\n                    src: \"/icons/heart.png\",\n                    alt: \"User-Friendly Environment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 245,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"student-login__feature-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"User-Friendly Environment\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"Simple design with smooth navigation and accessibility support\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 205,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 113,\n    columnNumber: 5\n  }, this);\n};\n_s(StudentLogin, \"B7mBKNbN85CHB/53Ikg1oDi1MNA=\", false, function () {\n  return [useNavigate, useLocation, useStudentAuth];\n});\n_c = StudentLogin;\nexport default StudentLogin;\nvar _c;\n$RefreshReg$(_c, \"StudentLogin\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "useLocation", "useStudentAuth", "jsxDEV", "_jsxDEV", "StudentLogin", "_s", "_location$state", "_location$state$from", "navigate", "location", "login", "error", "isLoading", "clearError", "formData", "setFormData", "identifier", "password", "remember", "formErrors", "setFormErrors", "showPassword", "setShowPassword", "from", "state", "pathname", "handleInputChange", "e", "name", "value", "type", "checked", "target", "prev", "undefined", "validateForm", "errors", "trim", "Object", "keys", "length", "handleSubmit", "preventDefault", "console", "log", "userType", "redirectTo", "email", "replace", "togglePasswordVisibility", "isEmail", "includes", "className", "children", "src", "alt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "viewBox", "fill", "d", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "onSubmit", "noValidate", "htmlFor", "id", "onChange", "placeholder", "disabled", "autoComplete", "required", "_c", "$RefreshReg$"], "sources": ["D:/capstone-e-bulletin-reactjs-proj/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/auth/StudentLogin/StudentLogin.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { useStudentAuth } from '../../../contexts/StudentAuthContext';\nimport './StudentLogin.css';\n\ninterface FormData {\n  identifier: string; // Can be email or student number\n  password: string;\n  remember: boolean;\n}\n\ninterface FormErrors {\n  identifier?: string;\n  password?: string;\n}\n\nconst StudentLogin: React.FC = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const { login, error, isLoading, clearError } = useStudentAuth();\n\n  const [formData, setFormData] = useState<FormData>({\n    identifier: '',\n    password: '',\n    remember: false,\n  });\n\n  const [formErrors, setFormErrors] = useState<FormErrors>({});\n  const [showPassword, setShowPassword] = useState(false);\n\n  // Get the intended destination or default to student newsfeed (landing page)\n  const from = (location.state as any)?.from?.pathname || '/student/newsfeed';\n\n  // Handle input changes\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value, type, checked } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value,\n    }));\n\n    // Clear field error when user starts typing\n    if (formErrors[name as keyof FormErrors]) {\n      setFormErrors(prev => ({\n        ...prev,\n        [name]: undefined,\n      }));\n    }\n\n    // Clear global error\n    if (error) {\n      clearError();\n    }\n  };\n\n  // Validate form\n  const validateForm = (): boolean => {\n    const errors: FormErrors = {};\n\n    // Identifier validation (email or student number)\n    if (!formData.identifier.trim()) {\n      errors.identifier = 'Email or student number is required';\n    }\n\n    // Password validation\n    if (!formData.password) {\n      errors.password = 'Password is required';\n    }\n\n    setFormErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  // Handle form submission\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    try {\n      console.log('🔐 StudentLogin - Attempting login with:', {\n        identifier: formData.identifier,\n        userType: 'student',\n        redirectTo: from\n      });\n\n      await login({\n        email: formData.identifier, // Backend handles both email and student number\n        password: formData.password,\n        userType: 'student',\n      });\n\n      console.log('✅ StudentLogin - Login successful, redirecting to:', from);\n      // Redirect to intended destination\n      navigate(from, { replace: true });\n    } catch (error) {\n      // Error is handled by the auth context\n      console.error('❌ StudentLogin - Login failed:', error);\n    }\n  };\n\n  // Toggle password visibility\n  const togglePasswordVisibility = () => {\n    setShowPassword(!showPassword);\n  };\n\n  // Determine if input looks like email or student number\n  const isEmail = formData.identifier.includes('@');\n\n  return (\n    <div className=\"student-login\">\n      <div className=\"student-login__container\">\n        {/* Left Panel - Login Form */}\n        <div className=\"student-login__form-section\">\n          <div className=\"student-login__form-container\">\n            {/* Login Form Header */}\n            <div className=\"student-login__form-header\">\n              <img\n                src=\"/logo/vcba1.png\"\n                alt=\"VCBA Logo\"\n                className=\"student-login__form-logo\"\n              />\n              <h1 className=\"student-login__form-title\">STUDENT LOGIN</h1>\n              <p className=\"student-login__form-subtitle\">Villamor College of Business and Arts, Inc.</p>\n            </div>\n\n            {/* Error Display */}\n            {error && (\n              <div className=\"student-login__error\">\n                <svg width=\"20\" height=\"20\" viewBox=\"0 0 24 24\" fill=\"none\">\n                  <path d=\"M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z\" stroke=\"currentColor\" strokeWidth=\"2\" strokeLinecap=\"round\" strokeLinejoin=\"round\"/>\n                </svg>\n                {error}\n              </div>\n            )}\n\n            {/* Login Form */}\n            <form onSubmit={handleSubmit} className=\"student-login__form\" noValidate>\n              <div className=\"student-login__form-group\">\n                <label htmlFor=\"identifier\" className=\"student-login__label\">Email or Student Number</label>\n                <input\n                  type=\"text\"\n                  id=\"identifier\"\n                  name=\"identifier\"\n                  value={formData.identifier}\n                  onChange={handleInputChange}\n                  placeholder=\"Enter your email or student number\"\n                  className={`student-login__input ${formErrors.identifier ? 'error' : ''}`}\n                  disabled={isLoading}\n                  autoComplete=\"username\"\n                  required\n                />\n                {formErrors.identifier && <span className=\"student-login__error-text\">{formErrors.identifier}</span>}\n              </div>\n\n              <div className=\"student-login__form-group\">\n                <label htmlFor=\"password\" className=\"student-login__label\">Password</label>\n                <input\n                  type={showPassword ? 'text' : 'password'}\n                  id=\"password\"\n                  name=\"password\"\n                  value={formData.password}\n                  onChange={handleInputChange}\n                  placeholder=\"Enter your password\"\n                  className={`student-login__input ${formErrors.password ? 'error' : ''}`}\n                  disabled={isLoading}\n                  autoComplete=\"current-password\"\n                  required\n                />\n                {formErrors.password && <span className=\"student-login__error-text\">{formErrors.password}</span>}\n              </div>\n\n              <div className=\"student-login__remember\">\n                <input\n                  type=\"checkbox\"\n                  id=\"remember\"\n                  name=\"remember\"\n                  checked={formData.remember}\n                  onChange={handleInputChange}\n                  className=\"student-login__checkbox\"\n                />\n                <label htmlFor=\"remember\" className=\"student-login__remember-label\">Remember me</label>\n              </div>\n\n              <button\n                type=\"submit\"\n                className=\"student-login__submit-btn\"\n                disabled={isLoading}\n              >\n                {isLoading ? 'Signing in...' : 'Sign In'}\n              </button>\n            </form>\n\n            {/* Admin Link */}\n            {/* <div className=\"student-login__signup-link\">\n              Need admin access? <Link to=\"/admin/login\">Admin Login</Link>\n            </div> */}\n          </div>\n        </div>\n\n        {/* Right Panel - Information Section */}\n        <div className=\"student-login__info-section\">\n          <div className=\"student-login__info-content\">\n\n            {/* School Information */}\n            <div className=\"student-login__school-info\">\n              <img\n                src=\"/logo/ebb1.png\"\n                alt=\"E-Bulletin Board Logo\"\n                className=\"student-login__school-logo\"\n              />\n              <h3 className=\"student-login__school-name\">\n                VCBA E-BULLETIN BOARD\n              </h3>\n              <p className=\"student-login__school-description\">\n                Villamor College of Business and Arts, Inc.\n              </p>\n\n              {/* Features */}\n              <div className=\"student-login__features\">\n                <div className=\"student-login__feature\">\n                  <div className=\"student-login__feature-icon\">\n                    <img src=\"/icons/megaphone.png\" alt=\"Categorized Contents\" />\n                  </div>\n                  <div className=\"student-login__feature-content\">\n                    <h4>Categorized Contents</h4>\n                    <p>Organized announcements by departments, clubs, events, and more</p>\n                  </div>\n                </div>\n\n                <div className=\"student-login__feature\">\n                  <div className=\"student-login__feature-icon\">\n                    <img src=\"/icons/message.png\" alt=\"Centralized Platform\" />\n                  </div>\n                  <div className=\"student-login__feature-content\">\n                    <h4>Centralized Platform</h4>\n                    <p>All school announcements in one place — accessible anytime, anywhere</p>\n                  </div>\n                </div>\n\n                <div className=\"student-login__feature\">\n                  <div className=\"student-login__feature-icon\">\n                    <img src=\"/icons/heart.png\" alt=\"User-Friendly Environment\" />\n                  </div>\n                  <div className=\"student-login__feature-content\">\n                    <h4>User-Friendly Environment</h4>\n                    <p>Simple design with smooth navigation and accessibility support</p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default StudentLogin;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAAeC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,cAAc,QAAQ,sCAAsC;AACrE,OAAO,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAa5B,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,oBAAA;EACnC,MAAMC,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAMU,QAAQ,GAAGT,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEU,KAAK;IAAEC,KAAK;IAAEC,SAAS;IAAEC;EAAW,CAAC,GAAGZ,cAAc,CAAC,CAAC;EAEhE,MAAM,CAACa,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAW;IACjDkB,UAAU,EAAE,EAAE;IACdC,QAAQ,EAAE,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAa,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAMyB,IAAI,GAAG,EAAAjB,eAAA,GAACG,QAAQ,CAACe,KAAK,cAAAlB,eAAA,wBAAAC,oBAAA,GAAfD,eAAA,CAAyBiB,IAAI,cAAAhB,oBAAA,uBAA7BA,oBAAA,CAA+BkB,QAAQ,KAAI,mBAAmB;;EAE3E;EACA,MAAMC,iBAAiB,GAAIC,CAAsC,IAAK;IACpE,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC,IAAI;MAAEC;IAAQ,CAAC,GAAGJ,CAAC,CAACK,MAAM;IAC/CjB,WAAW,CAACkB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACL,IAAI,GAAGE,IAAI,KAAK,UAAU,GAAGC,OAAO,GAAGF;IAC1C,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIV,UAAU,CAACS,IAAI,CAAqB,EAAE;MACxCR,aAAa,CAACa,IAAI,KAAK;QACrB,GAAGA,IAAI;QACP,CAACL,IAAI,GAAGM;MACV,CAAC,CAAC,CAAC;IACL;;IAEA;IACA,IAAIvB,KAAK,EAAE;MACTE,UAAU,CAAC,CAAC;IACd;EACF,CAAC;;EAED;EACA,MAAMsB,YAAY,GAAGA,CAAA,KAAe;IAClC,MAAMC,MAAkB,GAAG,CAAC,CAAC;;IAE7B;IACA,IAAI,CAACtB,QAAQ,CAACE,UAAU,CAACqB,IAAI,CAAC,CAAC,EAAE;MAC/BD,MAAM,CAACpB,UAAU,GAAG,qCAAqC;IAC3D;;IAEA;IACA,IAAI,CAACF,QAAQ,CAACG,QAAQ,EAAE;MACtBmB,MAAM,CAACnB,QAAQ,GAAG,sBAAsB;IAC1C;IAEAG,aAAa,CAACgB,MAAM,CAAC;IACrB,OAAOE,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,MAAM,KAAK,CAAC;EACzC,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAOd,CAAkB,IAAK;IACjDA,CAAC,CAACe,cAAc,CAAC,CAAC;IAElB,IAAI,CAACP,YAAY,CAAC,CAAC,EAAE;MACnB;IACF;IAEA,IAAI;MACFQ,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE;QACtD5B,UAAU,EAAEF,QAAQ,CAACE,UAAU;QAC/B6B,QAAQ,EAAE,SAAS;QACnBC,UAAU,EAAEvB;MACd,CAAC,CAAC;MAEF,MAAMb,KAAK,CAAC;QACVqC,KAAK,EAAEjC,QAAQ,CAACE,UAAU;QAAE;QAC5BC,QAAQ,EAAEH,QAAQ,CAACG,QAAQ;QAC3B4B,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEFF,OAAO,CAACC,GAAG,CAAC,oDAAoD,EAAErB,IAAI,CAAC;MACvE;MACAf,QAAQ,CAACe,IAAI,EAAE;QAAEyB,OAAO,EAAE;MAAK,CAAC,CAAC;IACnC,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACd;MACAgC,OAAO,CAAChC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMsC,wBAAwB,GAAGA,CAAA,KAAM;IACrC3B,eAAe,CAAC,CAACD,YAAY,CAAC;EAChC,CAAC;;EAED;EACA,MAAM6B,OAAO,GAAGpC,QAAQ,CAACE,UAAU,CAACmC,QAAQ,CAAC,GAAG,CAAC;EAEjD,oBACEhD,OAAA;IAAKiD,SAAS,EAAC,eAAe;IAAAC,QAAA,eAC5BlD,OAAA;MAAKiD,SAAS,EAAC,0BAA0B;MAAAC,QAAA,gBAEvClD,OAAA;QAAKiD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1ClD,OAAA;UAAKiD,SAAS,EAAC,+BAA+B;UAAAC,QAAA,gBAE5ClD,OAAA;YAAKiD,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzClD,OAAA;cACEmD,GAAG,EAAC,iBAAiB;cACrBC,GAAG,EAAC,WAAW;cACfH,SAAS,EAAC;YAA0B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eACFxD,OAAA;cAAIiD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAC;YAAa;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC5DxD,OAAA;cAAGiD,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAA2C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxF,CAAC,EAGLhD,KAAK,iBACJR,OAAA;YAAKiD,SAAS,EAAC,sBAAsB;YAAAC,QAAA,gBACnClD,OAAA;cAAKyD,KAAK,EAAC,IAAI;cAACC,MAAM,EAAC,IAAI;cAACC,OAAO,EAAC,WAAW;cAACC,IAAI,EAAC,MAAM;cAAAV,QAAA,eACzDlD,OAAA;gBAAM6D,CAAC,EAAC,uIAAuI;gBAACC,MAAM,EAAC,cAAc;gBAACC,WAAW,EAAC,GAAG;gBAACC,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC;cAAO;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjO,CAAC,EACLhD,KAAK;UAAA;YAAA6C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,eAGDxD,OAAA;YAAMkE,QAAQ,EAAE5B,YAAa;YAACW,SAAS,EAAC,qBAAqB;YAACkB,UAAU;YAAAjB,QAAA,gBACtElD,OAAA;cAAKiD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxClD,OAAA;gBAAOoE,OAAO,EAAC,YAAY;gBAACnB,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAAuB;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC5FxD,OAAA;gBACE2B,IAAI,EAAC,MAAM;gBACX0C,EAAE,EAAC,YAAY;gBACf5C,IAAI,EAAC,YAAY;gBACjBC,KAAK,EAAEf,QAAQ,CAACE,UAAW;gBAC3ByD,QAAQ,EAAE/C,iBAAkB;gBAC5BgD,WAAW,EAAC,oCAAoC;gBAChDtB,SAAS,EAAE,wBAAwBjC,UAAU,CAACH,UAAU,GAAG,OAAO,GAAG,EAAE,EAAG;gBAC1E2D,QAAQ,EAAE/D,SAAU;gBACpBgE,YAAY,EAAC,UAAU;gBACvBC,QAAQ;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDxC,UAAU,CAACH,UAAU,iBAAIb,OAAA;gBAAMiD,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAElC,UAAU,CAACH;cAAU;gBAAAwC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjG,CAAC,eAENxD,OAAA;cAAKiD,SAAS,EAAC,2BAA2B;cAAAC,QAAA,gBACxClD,OAAA;gBAAOoE,OAAO,EAAC,UAAU;gBAACnB,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eAC3ExD,OAAA;gBACE2B,IAAI,EAAET,YAAY,GAAG,MAAM,GAAG,UAAW;gBACzCmD,EAAE,EAAC,UAAU;gBACb5C,IAAI,EAAC,UAAU;gBACfC,KAAK,EAAEf,QAAQ,CAACG,QAAS;gBACzBwD,QAAQ,EAAE/C,iBAAkB;gBAC5BgD,WAAW,EAAC,qBAAqB;gBACjCtB,SAAS,EAAE,wBAAwBjC,UAAU,CAACF,QAAQ,GAAG,OAAO,GAAG,EAAE,EAAG;gBACxE0D,QAAQ,EAAE/D,SAAU;gBACpBgE,YAAY,EAAC,kBAAkB;gBAC/BC,QAAQ;cAAA;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT,CAAC,EACDxC,UAAU,CAACF,QAAQ,iBAAId,OAAA;gBAAMiD,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EAAElC,UAAU,CAACF;cAAQ;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7F,CAAC,eAENxD,OAAA;cAAKiD,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtClD,OAAA;gBACE2B,IAAI,EAAC,UAAU;gBACf0C,EAAE,EAAC,UAAU;gBACb5C,IAAI,EAAC,UAAU;gBACfG,OAAO,EAAEjB,QAAQ,CAACI,QAAS;gBAC3BuD,QAAQ,EAAE/C,iBAAkB;gBAC5B0B,SAAS,EAAC;cAAyB;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC,CAAC,eACFxD,OAAA;gBAAOoE,OAAO,EAAC,UAAU;gBAACnB,SAAS,EAAC,+BAA+B;gBAAAC,QAAA,EAAC;cAAW;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpF,CAAC,eAENxD,OAAA;cACE2B,IAAI,EAAC,QAAQ;cACbsB,SAAS,EAAC,2BAA2B;cACrCuB,QAAQ,EAAE/D,SAAU;cAAAyC,QAAA,EAEnBzC,SAAS,GAAG,eAAe,GAAG;YAAS;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxD,OAAA;QAAKiD,SAAS,EAAC,6BAA6B;QAAAC,QAAA,eAC1ClD,OAAA;UAAKiD,SAAS,EAAC,6BAA6B;UAAAC,QAAA,eAG1ClD,OAAA;YAAKiD,SAAS,EAAC,4BAA4B;YAAAC,QAAA,gBACzClD,OAAA;cACEmD,GAAG,EAAC,gBAAgB;cACpBC,GAAG,EAAC,uBAAuB;cAC3BH,SAAS,EAAC;YAA4B;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC,CAAC,eACFxD,OAAA;cAAIiD,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAE3C;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLxD,OAAA;cAAGiD,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAEjD;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAGJxD,OAAA;cAAKiD,SAAS,EAAC,yBAAyB;cAAAC,QAAA,gBACtClD,OAAA;gBAAKiD,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrClD,OAAA;kBAAKiD,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1ClD,OAAA;oBAAKmD,GAAG,EAAC,sBAAsB;oBAACC,GAAG,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1D,CAAC,eACNxD,OAAA;kBAAKiD,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7ClD,OAAA;oBAAAkD,QAAA,EAAI;kBAAoB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7BxD,OAAA;oBAAAkD,QAAA,EAAG;kBAA+D;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENxD,OAAA;gBAAKiD,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrClD,OAAA;kBAAKiD,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1ClD,OAAA;oBAAKmD,GAAG,EAAC,oBAAoB;oBAACC,GAAG,EAAC;kBAAsB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxD,CAAC,eACNxD,OAAA;kBAAKiD,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7ClD,OAAA;oBAAAkD,QAAA,EAAI;kBAAoB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC7BxD,OAAA;oBAAAkD,QAAA,EAAG;kBAAoE;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENxD,OAAA;gBAAKiD,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,gBACrClD,OAAA;kBAAKiD,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,eAC1ClD,OAAA;oBAAKmD,GAAG,EAAC,kBAAkB;oBAACC,GAAG,EAAC;kBAA2B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACNxD,OAAA;kBAAKiD,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7ClD,OAAA;oBAAAkD,QAAA,EAAI;kBAAyB;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClCxD,OAAA;oBAAAkD,QAAA,EAAG;kBAA8D;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtD,EAAA,CAlPID,YAAsB;EAAA,QACTL,WAAW,EACXC,WAAW,EACoBC,cAAc;AAAA;AAAA6E,EAAA,GAH1D1E,YAAsB;AAoP5B,eAAeA,YAAY;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}