{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 22v-9\",\n  key: \"x3hkom\"\n}], [\"path\", {\n  d: \"M15.17 2.21a1.67 1.67 0 0 1 1.63 0L21 4.57a1.93 1.93 0 0 1 0 3.36L8.82 14.79a1.655 1.655 0 0 1-1.64 0L3 12.43a1.93 1.93 0 0 1 0-3.36z\",\n  key: \"2ntwy6\"\n}], [\"path\", {\n  d: \"M20 13v3.87a2.06 2.06 0 0 1-1.11 1.83l-6 3.08a1.93 1.93 0 0 1-1.78 0l-6-3.08A2.06 2.06 0 0 1 4 16.87V13\",\n  key: \"1pmm1c\"\n}], [\"path\", {\n  d: \"M21 12.43a1.93 1.93 0 0 0 0-3.36L8.83 2.2a1.64 1.64 0 0 0-1.63 0L3 4.57a1.93 1.93 0 0 0 0 3.36l12.18 6.86a1.636 1.636 0 0 0 1.63 0z\",\n  key: \"12ttoo\"\n}]];\nconst PackageOpen = createLucideIcon(\"package-open\", __iconNode);\nexport { __iconNode, PackageOpen as default };\n//# sourceMappingURL=package-open.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}