{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"rect\", {\n  width: \"16\",\n  height: \"20\",\n  x: \"4\",\n  y: \"2\",\n  rx: \"2\",\n  key: \"1nb95v\"\n}], [\"path\", {\n  d: \"M12 6h.01\",\n  key: \"1vi96p\"\n}], [\"circle\", {\n  cx: \"12\",\n  cy: \"14\",\n  r: \"4\",\n  key: \"1jruaj\"\n}], [\"path\", {\n  d: \"M12 14h.01\",\n  key: \"1etili\"\n}]];\nconst Speaker = createLucideIcon(\"speaker\", __iconNode);\nexport { __iconNode, Speaker as default };\n//# sourceMappingURL=speaker.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}