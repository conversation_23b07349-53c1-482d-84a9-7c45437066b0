{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 22V12a10 10 0 1 1 20 0v10\",\n  key: \"o0fyp0\"\n}], [\"path\", {\n  d: \"M15 6.8v1.4a3 2.8 0 1 1-6 0V6.8\",\n  key: \"m8q3n9\"\n}], [\"path\", {\n  d: \"M10 15h.01\",\n  key: \"44in9x\"\n}], [\"path\", {\n  d: \"M14 15h.01\",\n  key: \"5mohn5\"\n}], [\"path\", {\n  d: \"M10 19a4 4 0 0 1-4-4v-3a6 6 0 1 1 12 0v3a4 4 0 0 1-4 4Z\",\n  key: \"hckbmu\"\n}], [\"path\", {\n  d: \"m9 19-2 3\",\n  key: \"iij7hm\"\n}], [\"path\", {\n  d: \"m15 19 2 3\",\n  key: \"npx8sa\"\n}]];\nconst TrainFrontTunnel = createLucideIcon(\"train-front-tunnel\", __iconNode);\nexport { __iconNode, TrainFrontTunnel as default };\n//# sourceMappingURL=train-front-tunnel.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}