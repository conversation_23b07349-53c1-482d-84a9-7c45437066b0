{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M16 9a5 5 0 0 1 .95 2.293\",\n  key: \"1fgyg8\"\n}], [\"path\", {\n  d: \"M19.364 5.636a9 9 0 0 1 1.889 9.96\",\n  key: \"l3zxae\"\n}], [\"path\", {\n  d: \"m2 2 20 20\",\n  key: \"1ooewy\"\n}], [\"path\", {\n  d: \"m7 7-.587.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298V11\",\n  key: \"1gbwow\"\n}], [\"path\", {\n  d: \"M9.828 4.172A.686.686 0 0 1 11 4.657v.686\",\n  key: \"s2je0y\"\n}]];\nconst VolumeOff = createLucideIcon(\"volume-off\", __iconNode);\nexport { __iconNode, VolumeOff as default };\n//# sourceMappingURL=volume-off.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}