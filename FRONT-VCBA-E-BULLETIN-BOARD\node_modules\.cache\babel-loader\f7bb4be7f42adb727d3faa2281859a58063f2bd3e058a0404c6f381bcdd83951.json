{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 10V2\",\n  key: \"16sf7g\"\n}], [\"path\", {\n  d: \"m4.93 10.93 1.41 1.41\",\n  key: \"2a7f42\"\n}], [\"path\", {\n  d: \"M2 18h2\",\n  key: \"j10viu\"\n}], [\"path\", {\n  d: \"M20 18h2\",\n  key: \"wocana\"\n}], [\"path\", {\n  d: \"m19.07 10.93-1.41 1.41\",\n  key: \"15zs5n\"\n}], [\"path\", {\n  d: \"M22 22H2\",\n  key: \"19qnx5\"\n}], [\"path\", {\n  d: \"m16 6-4 4-4-4\",\n  key: \"6wukr\"\n}], [\"path\", {\n  d: \"M16 18a4 4 0 0 0-8 0\",\n  key: \"1lzouq\"\n}]];\nconst Sunset = createLucideIcon(\"sunset\", __iconNode);\nexport { __iconNode, Sunset as default };\n//# sourceMappingURL=sunset.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}