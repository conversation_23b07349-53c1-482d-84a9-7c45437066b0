{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 7V4h16v3\",\n  key: \"9msm58\"\n}], [\"path\", {\n  d: \"M5 20h6\",\n  key: \"1h6pxn\"\n}], [\"path\", {\n  d: \"M13 4 8 20\",\n  key: \"kqq6aj\"\n}], [\"path\", {\n  d: \"m15 15 5 5\",\n  key: \"me55sn\"\n}], [\"path\", {\n  d: \"m20 15-5 5\",\n  key: \"11p7ol\"\n}]];\nconst RemoveFormatting = createLucideIcon(\"remove-formatting\", __iconNode);\nexport { __iconNode, RemoveFormatting as default };\n//# sourceMappingURL=remove-formatting.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}