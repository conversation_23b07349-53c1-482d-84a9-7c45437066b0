{"ast": null, "code": "var _jsxFileName = \"D:\\\\capstone-e-bulletin-reactjs-proj\\\\FRONT-VCBA-E-BULLETIN-BOARD\\\\src\\\\pages\\\\student\\\\StudentNewsfeed.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useCategories } from '../../hooks/useAnnouncements';\nimport { announcementService } from '../../services';\nimport CommentSection from '../../components/student/CommentSection';\nimport ImageLightbox from '../../components/common/ImageLightbox';\nimport { getImageUrl, API_BASE_URL } from '../../config/constants';\nimport { Home, Search, Pin, Calendar, MessageSquare, Heart, Filter, MapPin, BookOpen, Users, PartyPopper, AlertTriangle, Clock, Trophy, Briefcase, GraduationCap, Flag, Coffee, Plane } from 'lucide-react';\n\n// Custom hook for CORS-safe image loading\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst useImageLoader = imagePath => {\n  _s();\n  const [imageUrl, setImageUrl] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n        console.log('🔄 Fetching image via CORS-safe method:', fullUrl);\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Origin': window.location.origin\n          },\n          mode: 'cors'\n        });\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n        setImageUrl(objectUrl);\n        console.log('✅ Image loaded successfully via fetch');\n      } catch (err) {\n        console.error('❌ Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadImage();\n\n    // Cleanup object URL on unmount\n    return () => {\n      if (imageUrl && imageUrl.startsWith('blob:')) {\n        URL.revokeObjectURL(imageUrl);\n      }\n    };\n  }, [imagePath]);\n  return {\n    imageUrl,\n    loading,\n    error\n  };\n};\n\n// Reusable CORS-safe image component\n_s(useImageLoader, \"RmtsSrtaIxi3XNLTaMW1wv0GUMw=\");\nconst ImageDisplay = ({\n  imagePath,\n  alt,\n  style,\n  className,\n  onLoad,\n  onMouseEnter,\n  onMouseLeave\n}) => {\n  _s2();\n  const {\n    imageUrl,\n    loading,\n    error\n  } = useImageLoader(imagePath);\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b'\n      },\n      className: className,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.5rem',\n            fontSize: '1.5rem'\n          },\n          children: \"\\u23F3\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem'\n          },\n          children: \"Loading...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 121,\n      columnNumber: 7\n    }, this);\n  }\n  if (error || !imageUrl) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b',\n        border: '2px dashed #cbd5e1'\n      },\n      className: className,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '0.5rem',\n            fontSize: '1.5rem'\n          },\n          children: \"\\uD83D\\uDDBC\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.875rem',\n            fontWeight: '500'\n          },\n          children: \"Image unavailable\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '0.75rem',\n            marginTop: '0.25rem',\n            color: '#9ca3af'\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"img\", {\n    src: imageUrl,\n    alt: alt,\n    style: style,\n    className: className,\n    onLoad: e => {\n      console.log('✅ Image rendered successfully via CORS-safe method');\n      onLoad === null || onLoad === void 0 ? void 0 : onLoad(e);\n    },\n    onMouseEnter: onMouseEnter,\n    onMouseLeave: onMouseLeave\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 162,\n    columnNumber: 5\n  }, this);\n};\n\n// Facebook-style image gallery component\n_s2(ImageDisplay, \"PvWnh8aQTIWDcUxVyGaJg2nxP24=\", false, function () {\n  return [useImageLoader];\n});\n_c = ImageDisplay;\nconst FacebookImageGallery = ({\n  images,\n  altPrefix,\n  maxVisible = 4,\n  onImageClick\n}) => {\n  if (!images || images.length === 0) return null;\n  const visibleImages = images.slice(0, maxVisible);\n  const remainingCount = images.length - maxVisible;\n  const getImageStyle = (index, total) => {\n    const baseStyle = {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover',\n      cursor: 'pointer',\n      transition: 'transform 0.2s ease, filter 0.2s ease',\n      borderRadius: index === 0 && total === 1 ? '12px' : '8px'\n    };\n    return baseStyle;\n  };\n  const getContainerStyle = (index, total) => {\n    const baseStyle = {\n      position: 'relative',\n      overflow: 'hidden',\n      backgroundColor: '#f3f4f6'\n    };\n    if (total === 1) {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '400px',\n        borderRadius: '12px'\n      };\n    }\n    if (total === 2) {\n      return {\n        ...baseStyle,\n        width: '50%',\n        height: '300px',\n        borderRadius: '8px'\n      };\n    }\n    if (total === 3) {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '60%',\n          height: '300px',\n          borderRadius: '8px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '100%',\n          height: '148px',\n          borderRadius: '8px'\n        };\n      }\n    }\n\n    // 4+ images\n    if (index === 0) {\n      return {\n        ...baseStyle,\n        width: '60%',\n        height: '300px',\n        borderRadius: '8px'\n      };\n    } else {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '96px',\n        borderRadius: '8px'\n      };\n    }\n  };\n  const renderOverlay = (index, count) => {\n    if (index === maxVisible - 1 && count > 0) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.6)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontSize: '1.5rem',\n          fontWeight: '600',\n          borderRadius: '8px'\n        },\n        children: [\"+\", count]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this);\n    }\n    return null;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1rem'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: getContainerStyle(0, visibleImages.length),\n      children: [/*#__PURE__*/_jsxDEV(ImageDisplay, {\n        imagePath: visibleImages[0],\n        alt: `${altPrefix} - Image 1`,\n        style: getImageStyle(0, visibleImages.length),\n        onMouseEnter: e => {\n          e.currentTarget.style.transform = 'scale(1.02)';\n        },\n        onMouseLeave: e => {\n          e.currentTarget.style.transform = 'scale(1)';\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          cursor: 'pointer'\n        },\n        onClick: () => onImageClick(0)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this), visibleImages.length > 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        flexDirection: 'column',\n        gap: '4px',\n        width: visibleImages.length === 2 ? '50%' : '40%'\n      },\n      children: visibleImages.slice(1).map((image, idx) => {\n        const actualIndex = idx + 1;\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          style: getContainerStyle(actualIndex, visibleImages.length),\n          children: [/*#__PURE__*/_jsxDEV(ImageDisplay, {\n            imagePath: image,\n            alt: `${altPrefix} - Image ${actualIndex + 1}`,\n            style: getImageStyle(actualIndex, visibleImages.length),\n            onMouseEnter: e => {\n              e.currentTarget.style.transform = 'scale(1.02)';\n            },\n            onMouseLeave: e => {\n              e.currentTarget.style.transform = 'scale(1)';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 17\n          }, this), renderOverlay(actualIndex, remainingCount), onImageClick && /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            },\n            onClick: () => onImageClick(actualIndex)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 19\n          }, this)]\n        }, actualIndex, true, {\n          fileName: _jsxFileName,\n          lineNumber: 341,\n          columnNumber: 15\n        }, this);\n      })\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 296,\n    columnNumber: 5\n  }, this);\n};\n_c2 = FacebookImageGallery;\nconst StudentNewsfeed = () => {\n  _s3();\n  const navigate = useNavigate();\n\n  // Category styling function\n  const getCategoryStyle = categoryName => {\n    const styles = {\n      'ACADEMIC': {\n        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n        icon: BookOpen\n      },\n      'GENERAL': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Users\n      },\n      'EVENTS': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: PartyPopper\n      },\n      'EMERGENCY': {\n        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n        icon: AlertTriangle\n      },\n      'SPORTS': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Trophy\n      },\n      'DEADLINES': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Clock\n      }\n    };\n    return styles[categoryName] || styles['GENERAL'];\n  };\n\n  // Holiday type styling function\n  const getHolidayTypeStyle = holidayTypeName => {\n    const styles = {\n      'NATIONAL HOLIDAY': {\n        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n        icon: Flag\n      },\n      'SCHOOL EVENT': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: GraduationCap\n      },\n      'ACADEMIC BREAK': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Coffee\n      },\n      'SPORTS EVENT': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Trophy\n      },\n      'FIELD TRIP': {\n        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n        icon: Plane\n      },\n      'MEETING': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Briefcase\n      }\n    };\n    return styles[holidayTypeName] || styles['SCHOOL EVENT'];\n  };\n\n  // Filter states\n  const [filterCategory, setFilterCategory] = useState('');\n  const [filterGradeLevel, setFilterGradeLevel] = useState('');\n  const [searchTerm, setSearchTerm] = useState('');\n\n  // UI states\n  const [showComments, setShowComments] = useState(null);\n  const [newComment, setNewComment] = useState({});\n  const [submittingComment, setSubmittingComment] = useState(null);\n  const [selectedPinnedPost, setSelectedPinnedPost] = useState(null);\n\n  // Lightbox states\n  const [lightboxOpen, setLightboxOpen] = useState(false);\n  const [lightboxImages, setLightboxImages] = useState([]);\n  const [lightboxInitialIndex, setLightboxInitialIndex] = useState(0);\n\n  // Data states\n  const [calendarEvents, setCalendarEvents] = useState([]);\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState();\n  const [announcements, setAnnouncements] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState();\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState([]);\n  const {\n    categories\n  } = useCategories();\n\n  // Grade levels for dropdown (11-12)\n  const gradeLevels = Array.from({\n    length: 2\n  }, (_, i) => i + 11); // [11, 12]\n\n  // Open lightbox function\n  const openLightbox = (imageUrls, initialIndex) => {\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Fetch published announcements with images\n  const fetchPublishedAnnouncements = async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n      const response = await fetch(`${API_BASE_URL}/api/announcements?status=published&page=1&limit=50&sort_by=created_at&sort_order=DESC`);\n      const data = await response.json();\n      if (data.success && data.data) {\n        const announcementsData = data.data.announcements || [];\n\n        // The announcements already include attachments/images from the API\n        // No need to fetch images separately\n        setAnnouncements(announcementsData);\n\n        // Separate pinned announcements\n        const pinned = announcementsData.filter(ann => ann.is_pinned === 1);\n        setPinnedAnnouncements(pinned);\n      } else {\n        setError('Failed to load announcements');\n      }\n    } catch (err) {\n      console.error('Error fetching announcements:', err);\n      setError(err.message || 'Failed to load announcements');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch calendar events\n  const fetchCalendarEvents = async () => {\n    try {\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`);\n      const data = await response.json();\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(eventsData.map(async event => {\n          try {\n            const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`);\n            const imageData = await imageResponse.json();\n            if (imageData.success && imageData.data) {\n              event.images = imageData.data.attachments || [];\n            } else {\n              event.images = [];\n            }\n          } catch (imgErr) {\n            console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n            event.images = [];\n          }\n          return event;\n        }));\n        setCalendarEvents(eventsWithImages);\n      } else {\n        setCalendarError('Failed to load calendar events');\n      }\n    } catch (err) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to load calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  };\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchPublishedAnnouncements();\n    fetchCalendarEvents();\n  }, []);\n\n  // Handle like/unlike functionality\n  const handleLikeToggle = async announcement => {\n    try {\n      if (announcement.user_reaction) {\n        await announcementService.removeReaction(announcement.announcement_id);\n      } else {\n        const response = await fetch(`${API_BASE_URL}/api/announcements/${announcement.announcement_id}/reactions`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            reaction_type_id: 1,\n            notify_admin: true\n          })\n        });\n        if (!response.ok) {\n          throw new Error('Failed to add reaction');\n        }\n      }\n\n      // Refresh data\n      await fetchPublishedAnnouncements();\n    } catch (error) {\n      console.error('Error toggling like:', error);\n    }\n  };\n\n  // Handle comment submission\n  const handleCommentSubmit = async (announcementId, commentText) => {\n    if (!commentText.trim()) return;\n    try {\n      setSubmittingComment(announcementId);\n      const response = await fetch(`${API_BASE_URL}/api/announcements/${announcementId}/comments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          content: commentText,\n          commenter_name: 'Student User',\n          commenter_email: '<EMAIL>',\n          notify_admin: true\n        })\n      });\n      if (response.ok) {\n        setNewComment(prev => ({\n          ...prev,\n          [announcementId]: ''\n        }));\n        await fetchPublishedAnnouncements();\n      } else {\n        console.error('Failed to submit comment');\n      }\n    } catch (error) {\n      console.error('Error submitting comment:', error);\n    } finally {\n      setSubmittingComment(null);\n    }\n  };\n\n  // Filter announcements\n  const filteredAnnouncements = announcements.filter(announcement => {\n    const matchesSearch = !searchTerm || announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) || announcement.content.toLowerCase().includes(searchTerm.toLowerCase());\n    const matchesCategory = !filterCategory || announcement.category_id.toString() === filterCategory;\n\n    // Note: Grade level filtering would require additional backend support\n    // For now, we'll show all announcements regardless of grade level filter\n\n    return matchesSearch && matchesCategory;\n  });\n\n  // Filter calendar events with date-based filtering\n  const filteredCalendarEvents = calendarEvents.filter(event => {\n    const matchesSearch = !searchTerm || event.title.toLowerCase().includes(searchTerm.toLowerCase()) || event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase());\n\n    // Only show events that are published and on or after today's date\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    const eventDate = new Date(event.event_date);\n    eventDate.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    const isEventDateValid = eventDate >= today;\n    const isPublished = event.is_published === 1;\n    return matchesSearch && isEventDateValid && isPublished;\n  });\n\n  // Combine and sort all content by date (most recent first)\n  const displayAnnouncements = filteredAnnouncements;\n  const displayEvents = filteredCalendarEvents;\n\n  // Create combined content array for better chronological display\n  const combinedContent = [...displayAnnouncements.map(item => ({\n    ...item,\n    type: 'announcement',\n    sortDate: new Date(item.created_at)\n  })), ...displayEvents.map(item => ({\n    ...item,\n    type: 'event',\n    sortDate: new Date(item.event_date)\n  }))].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"style\", {\n      children: `\n          html {\n            scroll-behavior: smooth;\n          }\n\n          @keyframes spin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n          }\n\n          @media (max-width: 768px) {\n            .mobile-hide { display: none !important; }\n            .mobile-full { width: 100% !important; margin-left: 0 !important; }\n            .mobile-stack {\n              flex-direction: column !important;\n              gap: 0.75rem !important;\n            }\n            .mobile-grid { grid-template-columns: 1fr !important; }\n          }\n\n          @media (max-width: 480px) {\n            .mobile-small-padding { padding: 0.75rem !important; }\n            .mobile-small-text { font-size: 0.8rem !important; }\n            .mobile-compact-header {\n              padding: 0.75rem 1rem !important;\n            }\n            .mobile-compact-title {\n              font-size: 1.25rem !important;\n            }\n            .mobile-compact-search {\n              max-width: 200px !important;\n            }\n          }\n        `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 671,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        minHeight: '100vh',\n        background: 'linear-gradient(135deg, #f0f9ff 0%, #fefce8 100%)',\n        scrollBehavior: 'smooth'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mobile-full\",\n        style: {\n          flex: 1,\n          display: 'flex',\n          flexDirection: 'column',\n          minHeight: '100vh',\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"header\", {\n          style: {\n            background: 'white',\n            borderBottom: '1px solid #e5e7eb',\n            position: 'sticky',\n            top: 0,\n            zIndex: 100,\n            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '100%',\n              padding: '0 2rem',\n              height: '72px',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1.5rem',\n                minWidth: '300px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: \"/logo/vcba1.png\",\n                alt: \"VCBA Logo\",\n                style: {\n                  width: '48px',\n                  height: '48px',\n                  objectFit: 'contain'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 749,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '1.5rem',\n                    fontWeight: '700',\n                    color: '#111827',\n                    lineHeight: '1.2'\n                  },\n                  children: \"VCBA E-Bulletin Board\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 759,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '0.875rem',\n                    color: '#6b7280'\n                  },\n                  children: \"Student Newsfeed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 768,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 758,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 743,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                flex: 1,\n                maxWidth: '500px',\n                margin: '0 2rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  position: 'relative'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Search, {\n                  size: 20,\n                  style: {\n                    position: 'absolute',\n                    left: '1rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: '#9ca3af'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 785,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  placeholder: \"Search post\",\n                  value: searchTerm,\n                  onChange: e => setSearchTerm(e.target.value),\n                  style: {\n                    width: '100%',\n                    height: '44px',\n                    padding: '0 1rem 0 3rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '12px',\n                    background: '#f9fafb',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    transition: 'all 0.2s ease'\n                  },\n                  onFocus: e => {\n                    e.target.style.borderColor = '#3b82f6';\n                    e.target.style.background = 'white';\n                    e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';\n                  },\n                  onBlur: e => {\n                    e.target.style.borderColor = '#d1d5db';\n                    e.target.style.background = '#f9fafb';\n                    e.target.style.boxShadow = 'none';\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 795,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 784,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 779,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem',\n                minWidth: '400px',\n                justifyContent: 'flex-end'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  padding: '0.5rem',\n                  background: '#f9fafb',\n                  borderRadius: '12px',\n                  border: '1px solid #e5e7eb'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"select\", {\n                  value: filterCategory,\n                  onChange: e => setFilterCategory(e.target.value),\n                  style: {\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '110px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"All Categories\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 859,\n                    columnNumber: 19\n                  }, this), categories.map(category => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: category.category_id.toString(),\n                    children: category.name\n                  }, category.category_id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 861,\n                    columnNumber: 21\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 844,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n                  value: filterGradeLevel,\n                  onChange: e => setFilterGradeLevel(e.target.value),\n                  style: {\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '100px'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                    value: \"\",\n                    children: \"All Grades\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 882,\n                    columnNumber: 19\n                  }, this), gradeLevels.map(grade => /*#__PURE__*/_jsxDEV(\"option\", {\n                    value: grade.toString(),\n                    children: [\"Grade \", grade]\n                  }, grade, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 884,\n                    columnNumber: 21\n                  }, this))]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 867,\n                  columnNumber: 17\n                }, this), (searchTerm || filterCategory || filterGradeLevel) && /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => {\n                    setSearchTerm('');\n                    setFilterCategory('');\n                    setFilterGradeLevel('');\n                  },\n                  style: {\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: '#ef4444',\n                    color: 'white',\n                    fontSize: '0.875rem',\n                    fontWeight: '500',\n                    cursor: 'pointer',\n                    transition: 'all 0.2s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.background = '#dc2626';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.background = '#ef4444';\n                  },\n                  children: \"Clear\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 891,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 835,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => navigate('/student/dashboard'),\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  padding: '0.5rem 1rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '8px',\n                  background: 'white',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                },\n                onMouseEnter: e => {\n                  e.currentTarget.style.borderColor = '#3b82f6';\n                  e.currentTarget.style.color = '#3b82f6';\n                },\n                onMouseLeave: e => {\n                  e.currentTarget.style.borderColor = '#d1d5db';\n                  e.currentTarget.style.color = '#374151';\n                },\n                children: [/*#__PURE__*/_jsxDEV(Home, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 948,\n                  columnNumber: 17\n                }, this), \"Dashboard\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 923,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 827,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 734,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 726,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            flex: 1,\n            display: 'flex',\n            gap: '1.5rem',\n            padding: '1.5rem 2rem',\n            background: 'transparent',\n            alignItems: 'flex-start'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '280px',\n              flexShrink: 0\n            },\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: 'white',\n                borderRadius: '14px',\n                border: '1px solid #e5e7eb',\n                overflow: 'hidden',\n                position: 'sticky',\n                top: '80px',\n                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '1rem 1.25rem 0.75rem',\n                  borderBottom: '1px solid #f3f4f6',\n                  background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: '0.75rem',\n                    marginBottom: '0.5rem'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Pin, {\n                    size: 20,\n                    style: {\n                      color: 'white'\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 990,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    style: {\n                      margin: 0,\n                      fontSize: '1.125rem',\n                      fontWeight: '600',\n                      color: 'white'\n                    },\n                    children: \"Important Updates\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 991,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 984,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  style: {\n                    margin: 0,\n                    fontSize: '0.875rem',\n                    color: 'rgba(255, 255, 255, 0.8)'\n                  },\n                  children: \"Don't miss these announcements\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1000,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 979,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  padding: '0.75rem'\n                },\n                children: pinnedAnnouncements.length > 0 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                  children: [pinnedAnnouncements.slice(0, 3).map((announcement, index) => {\n                    // Handle alert announcements with special styling\n                    let colors;\n                    if (announcement.is_alert) {\n                      colors = ['#fee2e2', '#fecaca', '#ef4444']; // Red alert colors\n                    } else {\n                      const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n\n                      // Create gradient background based on category\n                      const gradientColors = {\n                        'ACADEMIC': ['#dbeafe', '#bfdbfe', '#3b82f6'],\n                        'EVENTS': ['#fef3c7', '#fde68a', '#f59e0b'],\n                        'EMERGENCY': ['#fee2e2', '#fecaca', '#ef4444'],\n                        'SPORTS': ['#dcfce7', '#bbf7d0', '#22c55e'],\n                        'DEADLINES': ['#ede9fe', '#ddd6fe', '#8b5cf6'],\n                        'GENERAL': ['#f3f4f6', '#e5e7eb', '#6b7280']\n                      };\n                      colors = gradientColors[categoryName] || gradientColors['GENERAL'];\n                    }\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        padding: '0.75rem',\n                        background: `linear-gradient(135deg, ${colors[0]} 0%, ${colors[1]} 100%)`,\n                        borderRadius: '10px',\n                        border: `1px solid ${colors[2]}`,\n                        marginBottom: '0.75rem',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      },\n                      onMouseEnter: e => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = `0 8px 25px ${colors[2]}30`;\n                      },\n                      onMouseLeave: e => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = 'none';\n                      },\n                      onClick: () => setSelectedPinnedPost(announcement),\n                      children: /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'flex-start',\n                          gap: '0.75rem'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            width: '10px',\n                            height: '10px',\n                            background: colors[2],\n                            borderRadius: '50%',\n                            marginTop: '0.5rem',\n                            flexShrink: 0,\n                            boxShadow: `0 0 0 3px ${colors[2]}30`\n                          }\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1061,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            flex: 1\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                            style: {\n                              margin: '0 0 0.5rem 0',\n                              fontSize: '0.875rem',\n                              fontWeight: '600',\n                              color: colors[2],\n                              lineHeight: '1.4'\n                            },\n                            children: announcement.title\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1071,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            style: {\n                              margin: '0 0 0.5rem 0',\n                              fontSize: '0.8rem',\n                              color: colors[2],\n                              lineHeight: '1.4'\n                            },\n                            children: announcement.content.length > 60 ? `${announcement.content.substring(0, 60)}...` : announcement.content\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1080,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.5rem',\n                              fontSize: '0.75rem',\n                              color: colors[2]\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                              size: 12\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1097,\n                              columnNumber: 33\n                            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                              children: new Date(announcement.created_at).toLocaleDateString()\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1098,\n                              columnNumber: 33\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1090,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1070,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1056,\n                        columnNumber: 27\n                      }, this)\n                    }, announcement.announcement_id, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1035,\n                      columnNumber: 25\n                    }, this);\n                  }), pinnedAnnouncements.length > 3 && /*#__PURE__*/_jsxDEV(\"button\", {\n                    style: {\n                      width: '100%',\n                      padding: '0.75rem',\n                      border: '1px solid #e5e7eb',\n                      borderRadius: '12px',\n                      background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',\n                      color: '#3b82f6',\n                      fontSize: '0.875rem',\n                      fontWeight: '600',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.background = 'linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)';\n                      e.currentTarget.style.borderColor = '#3b82f6';\n                      e.currentTarget.style.transform = 'translateY(-1px)';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.background = 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)';\n                      e.currentTarget.style.borderColor = '#e5e7eb';\n                      e.currentTarget.style.transform = 'translateY(0)';\n                    },\n                    children: [\"\\uD83D\\uDCCC View All \", pinnedAnnouncements.length, \" Important Updates\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1108,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true) : /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    padding: '2rem 1rem',\n                    textAlign: 'center',\n                    color: '#6b7280'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Pin, {\n                    size: 24,\n                    style: {\n                      marginBottom: '0.5rem',\n                      opacity: 0.5\n                    }\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1140,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: 0,\n                      fontSize: '0.875rem'\n                    },\n                    children: \"No pinned posts available\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1141,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1135,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1010,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 969,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 965,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1,\n              minWidth: 0\n            },\n            children: [(loading || calendarLoading) && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'center',\n                alignItems: 'center',\n                padding: '3rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  width: '2.5rem',\n                  height: '2.5rem',\n                  border: '3px solid #e5e7eb',\n                  borderTop: '3px solid #3b82f6',\n                  borderRadius: '50%',\n                  animation: 'spin 1s linear infinite'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1160,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1154,\n              columnNumber: 13\n            }, this), (error || calendarError) && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: 'rgba(239, 68, 68, 0.1)',\n                border: '1px solid rgba(239, 68, 68, 0.2)',\n                borderRadius: '12px',\n                padding: '1rem',\n                marginBottom: '1.5rem',\n                color: '#dc2626'\n              },\n              children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"Announcements: \", error]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1181,\n                columnNumber: 25\n              }, this), calendarError && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"Calendar: \", calendarError]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1182,\n                columnNumber: 33\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1173,\n              columnNumber: 13\n            }, this), !loading && !calendarLoading && displayAnnouncements.length === 0 && displayEvents.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                background: 'rgba(255, 255, 255, 0.8)',\n                borderRadius: '16px',\n                padding: '3rem',\n                textAlign: 'center',\n                border: '1px solid rgba(0, 0, 0, 0.1)',\n                backdropFilter: 'blur(10px)'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '1rem'\n                },\n                children: /*#__PURE__*/_jsxDEV(Filter, {\n                  size: 48,\n                  color: \"#9ca3af\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1199,\n                  columnNumber: 17\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1196,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  color: '#374151',\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  margin: '0 0 0.5rem 0'\n                },\n                children: \"No updates found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1201,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                style: {\n                  color: '#6b7280',\n                  margin: 0\n                },\n                children: searchTerm || filterCategory || filterGradeLevel ? 'Try adjusting your filters to see more content.' : 'Check back later for new updates.'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1209,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1188,\n              columnNumber: 13\n            }, this), !loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                maxWidth: '1200px',\n                margin: '0 auto',\n                display: 'flex',\n                flexDirection: 'column',\n                gap: '1.5rem'\n              },\n              children: [displayEvents.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: displayEvents.map(event => /*#__PURE__*/_jsxDEV(\"div\", {\n                  style: {\n                    background: 'rgba(255, 255, 255, 0.95)',\n                    borderRadius: '16px',\n                    padding: '1.5rem',\n                    border: '1px solid rgba(0, 0, 0, 0.1)',\n                    backdropFilter: 'blur(10px)',\n                    boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                    transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                  },\n                  onMouseEnter: e => {\n                    e.currentTarget.style.transform = 'translateY(-2px)';\n                    e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                  },\n                  onMouseLeave: e => {\n                    e.currentTarget.style.transform = 'translateY(0)';\n                    e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '1rem',\n                      marginBottom: '1rem'\n                    },\n                    children: [(() => {\n                      const holidayTypeName = (event.holiday_type_name || 'SCHOOL EVENT').toUpperCase();\n                      const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                      const IconComponent = holidayStyle.icon;\n                      return /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          width: '50px',\n                          height: '50px',\n                          borderRadius: '12px',\n                          background: holidayStyle.background,\n                          display: 'flex',\n                          alignItems: 'center',\n                          justifyContent: 'center'\n                        },\n                        children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                          size: 20,\n                          color: \"white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1275,\n                          columnNumber: 31\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1266,\n                        columnNumber: 29\n                      }, this);\n                    })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        flex: 1\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.5rem',\n                          marginBottom: '0.25rem'\n                        },\n                        children: (() => {\n                          const holidayTypeName = (event.holiday_type_name || 'SCHOOL EVENT').toUpperCase();\n                          const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                          const IconComponent = holidayStyle.icon;\n                          return /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              background: holidayStyle.background,\n                              color: 'white',\n                              padding: '0.25rem 0.75rem',\n                              borderRadius: '12px',\n                              fontSize: '0.75rem',\n                              fontWeight: '600',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                              size: 12,\n                              color: \"white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1303,\n                              columnNumber: 35\n                            }, this), holidayTypeName]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1292,\n                            columnNumber: 33\n                          }, this);\n                        })()\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1280,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          color: '#9ca3af',\n                          fontSize: '0.8rem'\n                        },\n                        children: new Date(event.event_date).toLocaleDateString('en-US', {\n                          weekday: 'long',\n                          year: 'numeric',\n                          month: 'long',\n                          day: 'numeric'\n                        })\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1309,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1279,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1254,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                    style: {\n                      margin: '0 0 0.75rem 0',\n                      color: '#1f2937',\n                      fontSize: '1.25rem',\n                      fontWeight: '700',\n                      lineHeight: '1.3'\n                    },\n                    children: event.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1324,\n                    columnNumber: 23\n                  }, this), event.description && /*#__PURE__*/_jsxDEV(\"p\", {\n                    style: {\n                      margin: '0 0 1rem 0',\n                      color: '#4b5563',\n                      fontSize: '0.95rem',\n                      lineHeight: '1.6'\n                    },\n                    children: event.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1335,\n                    columnNumber: 25\n                  }, this), (() => {\n                    // Get event images if they exist\n                    const eventImageUrls = [];\n                    if (event.images && event.images.length > 0) {\n                      event.images.forEach(img => {\n                        if (img.file_path) {\n                          // Convert file_path to full URL\n                          const imageUrl = getImageUrl(img.file_path);\n                          if (imageUrl) {\n                            eventImageUrls.push(imageUrl);\n                          }\n                        }\n                      });\n                    }\n                    return eventImageUrls.length > 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginBottom: '1rem'\n                      },\n                      children: /*#__PURE__*/_jsxDEV(FacebookImageGallery, {\n                        images: eventImageUrls.filter(Boolean),\n                        altPrefix: event.title,\n                        maxVisible: 4,\n                        onImageClick: index => {\n                          const filteredImages = eventImageUrls.filter(Boolean);\n                          openLightbox(filteredImages, index);\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1364,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1363,\n                      columnNumber: 27\n                    }, this) : null;\n                  })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      display: 'flex',\n                      alignItems: 'center',\n                      justifyContent: 'space-between',\n                      paddingTop: '1rem',\n                      borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem',\n                        color: '#6b7280',\n                        fontSize: '0.875rem'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '0.25rem'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(MapPin, {\n                          size: 14,\n                          color: \"#6b7280\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1393,\n                          columnNumber: 29\n                        }, this), \"School Event\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1392,\n                        columnNumber: 27\n                      }, this), event.end_date && event.end_date !== event.event_date && /*#__PURE__*/_jsxDEV(\"span\", {\n                        children: [\"Until \", new Date(event.end_date).toLocaleDateString()]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1397,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1385,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1378,\n                    columnNumber: 23\n                  }, this)]\n                }, `event-${event.calendar_id}`, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1233,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false), displayAnnouncements.length > 0 && /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: displayAnnouncements.map(announcement => {\n                  var _newComment$announcem, _newComment$announcem2, _newComment$announcem3, _newComment$announcem4;\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    style: {\n                      background: 'rgba(255, 255, 255, 0.95)',\n                      borderRadius: '16px',\n                      padding: '1.5rem',\n                      border: '1px solid rgba(0, 0, 0, 0.1)',\n                      backdropFilter: 'blur(10px)',\n                      boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                      transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                    },\n                    onMouseEnter: e => {\n                      e.currentTarget.style.transform = 'translateY(-2px)';\n                      e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                    },\n                    onMouseLeave: e => {\n                      e.currentTarget.style.transform = 'translateY(0)';\n                      e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      },\n                      children: [(() => {\n                        if (announcement.is_alert) {\n                          return /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              width: '50px',\n                              height: '50px',\n                              background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                              borderRadius: '12px',\n                              display: 'flex',\n                              alignItems: 'center',\n                              justifyContent: 'center',\n                              flexShrink: 0\n                            },\n                            children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                              size: 20,\n                              color: \"white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1452,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1442,\n                            columnNumber: 31\n                          }, this);\n                        } else {\n                          const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                          const categoryStyle = getCategoryStyle(categoryName);\n                          const IconComponent = categoryStyle.icon;\n                          return /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              width: '50px',\n                              height: '50px',\n                              borderRadius: '12px',\n                              background: categoryStyle.background,\n                              display: 'flex',\n                              alignItems: 'center',\n                              justifyContent: 'center'\n                            },\n                            children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                              size: 20,\n                              color: \"white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1470,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1461,\n                            columnNumber: 31\n                          }, this);\n                        }\n                      })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          flex: 1\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            marginBottom: '0.25rem'\n                          },\n                          children: [(() => {\n                            if (announcement.is_alert) {\n                              return /*#__PURE__*/_jsxDEV(\"span\", {\n                                style: {\n                                  background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                                  color: 'white',\n                                  fontSize: '0.75rem',\n                                  fontWeight: '600',\n                                  padding: '0.25rem 0.75rem',\n                                  borderRadius: '12px',\n                                  textTransform: 'uppercase',\n                                  letterSpacing: '0.5px',\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: '0.25rem'\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                                  size: 12,\n                                  color: \"white\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1498,\n                                  columnNumber: 37\n                                }, this), \"Alert\"]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1485,\n                                columnNumber: 35\n                              }, this);\n                            } else {\n                              const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                              const categoryStyle = getCategoryStyle(categoryName);\n                              const IconComponent = categoryStyle.icon;\n                              return /*#__PURE__*/_jsxDEV(\"span\", {\n                                style: {\n                                  background: categoryStyle.background,\n                                  color: 'white',\n                                  padding: '0.25rem 0.75rem',\n                                  borderRadius: '12px',\n                                  fontSize: '0.75rem',\n                                  fontWeight: '600',\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: '0.25rem'\n                                },\n                                children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                                  size: 12,\n                                  color: \"white\"\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 1519,\n                                  columnNumber: 37\n                                }, this), categoryName]\n                              }, void 0, true, {\n                                fileName: _jsxFileName,\n                                lineNumber: 1508,\n                                columnNumber: 35\n                              }, this);\n                            }\n                          })(), announcement.is_pinned === 1 && /*#__PURE__*/_jsxDEV(\"span\", {\n                            style: {\n                              background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n                              color: 'white',\n                              padding: '0.25rem 0.75rem',\n                              borderRadius: '12px',\n                              fontSize: '0.75rem',\n                              fontWeight: '600',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.25rem'\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(Pin, {\n                              size: 12,\n                              color: \"white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1537,\n                              columnNumber: 33\n                            }, this), \"PINNED\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1526,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1476,\n                          columnNumber: 27\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            color: '#9ca3af',\n                            fontSize: '0.8rem'\n                          },\n                          children: [\"By \", announcement.author_name, \" \\u2022 \", new Date(announcement.published_at).toLocaleDateString('en-US', {\n                            weekday: 'short',\n                            year: 'numeric',\n                            month: 'short',\n                            day: 'numeric',\n                            hour: '2-digit',\n                            minute: '2-digit'\n                          })]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1542,\n                          columnNumber: 27\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1475,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1433,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                      style: {\n                        margin: '0 0 0.75rem 0',\n                        color: '#1f2937',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        lineHeight: '1.3'\n                      },\n                      children: announcement.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1559,\n                      columnNumber: 23\n                    }, this), (() => {\n                      // Get images from multiple sources\n                      const imageUrls = [];\n\n                      // Add images from attachments (new multiple image system)\n                      if (announcement.attachments && announcement.attachments.length > 0) {\n                        announcement.attachments.forEach(img => {\n                          if (img.file_path) {\n                            // Use getImageUrl to construct the full URL\n                            const fullUrl = getImageUrl(img.file_path);\n                            if (fullUrl) {\n                              imageUrls.push(fullUrl);\n                            }\n                          }\n                        });\n                      }\n\n                      // Fallback to legacy single image\n                      if (imageUrls.length === 0 && (announcement.image_url || announcement.image_path)) {\n                        const legacyUrl = getImageUrl(announcement.image_url || announcement.image_path);\n                        if (legacyUrl) {\n                          imageUrls.push(legacyUrl);\n                        }\n                      }\n                      return imageUrls.length > 0 ? /*#__PURE__*/_jsxDEV(FacebookImageGallery, {\n                        images: imageUrls.filter(Boolean),\n                        altPrefix: announcement.title,\n                        maxVisible: 4,\n                        onImageClick: index => {\n                          const filteredImages = imageUrls.filter(Boolean);\n                          openLightbox(filteredImages, index);\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1596,\n                        columnNumber: 27\n                      }, this) : null;\n                    })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        margin: '0 0 1.5rem 0',\n                        color: '#4b5563',\n                        fontSize: '0.95rem',\n                        lineHeight: '1.6'\n                      },\n                      dangerouslySetInnerHTML: {\n                        __html: announcement.content\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1608,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        paddingTop: '1rem',\n                        borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => handleLikeToggle(announcement),\n                          style: {\n                            background: announcement.user_reaction ? 'rgba(239, 68, 68, 0.1)' : 'transparent',\n                            color: announcement.user_reaction ? '#dc2626' : '#6b7280',\n                            border: 'none',\n                            borderRadius: '8px',\n                            padding: '0.5rem 1rem',\n                            fontSize: '0.875rem',\n                            fontWeight: '600',\n                            cursor: 'pointer',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            transition: 'all 0.2s ease'\n                          },\n                          onMouseEnter: e => {\n                            if (!announcement.user_reaction) {\n                              e.currentTarget.style.background = 'rgba(239, 68, 68, 0.1)';\n                              e.currentTarget.style.color = '#dc2626';\n                            }\n                          },\n                          onMouseLeave: e => {\n                            if (!announcement.user_reaction) {\n                              e.currentTarget.style.background = 'transparent';\n                              e.currentTarget.style.color = '#6b7280';\n                            }\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(Heart, {\n                            size: 16,\n                            color: announcement.user_reaction ? \"#dc2626\" : \"#9ca3af\",\n                            fill: announcement.user_reaction ? \"#dc2626\" : \"none\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1660,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: announcement.reaction_count || 0\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1665,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1631,\n                          columnNumber: 27\n                        }, this), announcement.allow_comments && /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => setShowComments(showComments === announcement.announcement_id ? null : announcement.announcement_id),\n                          style: {\n                            background: showComments === announcement.announcement_id ? 'rgba(59, 130, 246, 0.1)' : 'transparent',\n                            color: showComments === announcement.announcement_id ? '#3b82f6' : '#6b7280',\n                            border: 'none',\n                            borderRadius: '8px',\n                            padding: '0.5rem 1rem',\n                            fontSize: '0.875rem',\n                            fontWeight: '600',\n                            cursor: 'pointer',\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            transition: 'all 0.2s ease'\n                          },\n                          onMouseEnter: e => {\n                            if (showComments !== announcement.announcement_id) {\n                              e.currentTarget.style.background = 'rgba(59, 130, 246, 0.1)';\n                              e.currentTarget.style.color = '#3b82f6';\n                            }\n                          },\n                          onMouseLeave: e => {\n                            if (showComments !== announcement.announcement_id) {\n                              e.currentTarget.style.background = 'transparent';\n                              e.currentTarget.style.color = '#6b7280';\n                            }\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(MessageSquare, {\n                            size: 16,\n                            color: \"#6b7280\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1701,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: announcement.comment_count || 0\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1702,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1670,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1625,\n                        columnNumber: 25\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          color: '#9ca3af',\n                          fontSize: '0.8rem'\n                        },\n                        children: [announcement.view_count || 0, \" views\"]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1707,\n                        columnNumber: 25\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1618,\n                      columnNumber: 23\n                    }, this), showComments === announcement.announcement_id && announcement.allow_comments && /*#__PURE__*/_jsxDEV(\"div\", {\n                      style: {\n                        marginTop: '1rem',\n                        paddingTop: '1rem',\n                        borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        style: {\n                          display: 'flex',\n                          gap: '0.75rem',\n                          marginBottom: '1rem'\n                        },\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            width: '40px',\n                            height: '40px',\n                            borderRadius: '50%',\n                            background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                            display: 'flex',\n                            alignItems: 'center',\n                            justifyContent: 'center',\n                            color: 'white',\n                            fontWeight: '600',\n                            fontSize: '0.875rem'\n                          },\n                          children: \"S\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1728,\n                          columnNumber: 29\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          style: {\n                            flex: 1\n                          },\n                          children: [/*#__PURE__*/_jsxDEV(\"textarea\", {\n                            value: newComment[announcement.announcement_id] || '',\n                            onChange: e => setNewComment(prev => ({\n                              ...prev,\n                              [announcement.announcement_id]: e.target.value\n                            })),\n                            placeholder: \"Write a comment...\",\n                            style: {\n                              width: '100%',\n                              minHeight: '80px',\n                              padding: '0.75rem',\n                              border: '1px solid #e5e7eb',\n                              borderRadius: '12px',\n                              fontSize: '0.9rem',\n                              outline: 'none',\n                              resize: 'vertical',\n                              fontFamily: 'inherit'\n                            }\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1743,\n                            columnNumber: 31\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            style: {\n                              display: 'flex',\n                              justifyContent: 'flex-end',\n                              marginTop: '0.5rem'\n                            },\n                            children: /*#__PURE__*/_jsxDEV(\"button\", {\n                              onClick: () => handleCommentSubmit(announcement.announcement_id, newComment[announcement.announcement_id] || ''),\n                              disabled: !((_newComment$announcem = newComment[announcement.announcement_id]) !== null && _newComment$announcem !== void 0 && _newComment$announcem.trim()) || submittingComment === announcement.announcement_id,\n                              style: {\n                                background: (_newComment$announcem2 = newComment[announcement.announcement_id]) !== null && _newComment$announcem2 !== void 0 && _newComment$announcem2.trim() ? 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)' : '#e5e7eb',\n                                color: (_newComment$announcem3 = newComment[announcement.announcement_id]) !== null && _newComment$announcem3 !== void 0 && _newComment$announcem3.trim() ? 'white' : '#9ca3af',\n                                border: 'none',\n                                borderRadius: '8px',\n                                padding: '0.5rem 1rem',\n                                fontSize: '0.875rem',\n                                fontWeight: '600',\n                                cursor: (_newComment$announcem4 = newComment[announcement.announcement_id]) !== null && _newComment$announcem4 !== void 0 && _newComment$announcem4.trim() ? 'pointer' : 'not-allowed',\n                                transition: 'all 0.2s ease'\n                              },\n                              children: submittingComment === announcement.announcement_id ? 'Posting...' : 'Post Comment'\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 1767,\n                              columnNumber: 33\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 1762,\n                            columnNumber: 31\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 1742,\n                          columnNumber: 29\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1723,\n                        columnNumber: 27\n                      }, this), /*#__PURE__*/_jsxDEV(CommentSection, {\n                        announcementId: announcement.announcement_id,\n                        allowComments: announcement.allow_comments,\n                        currentUserType: \"student\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1794,\n                        columnNumber: 27\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1717,\n                      columnNumber: 25\n                    }, this)]\n                  }, `announcement-${announcement.announcement_id}`, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1412,\n                    columnNumber: 21\n                  }, this);\n                })\n              }, void 0, false)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1222,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1151,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 956,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 716,\n        columnNumber: 7\n      }, this), selectedPinnedPost && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000,\n          padding: '2rem'\n        },\n        onClick: () => setSelectedPinnedPost(null),\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: 'white',\n            borderRadius: '16px',\n            maxWidth: '600px',\n            width: '100%',\n            maxHeight: '80vh',\n            overflow: 'auto',\n            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n          },\n          onClick: e => e.stopPropagation(),\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '1.5rem',\n              borderBottom: '1px solid #e5e7eb',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem'\n              },\n              children: [/*#__PURE__*/_jsxDEV(Pin, {\n                size: 20,\n                style: {\n                  color: '#22c55e'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1852,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                style: {\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827'\n                },\n                children: \"Pinned Post\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1853,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1847,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setSelectedPinnedPost(null),\n              style: {\n                background: 'none',\n                border: 'none',\n                fontSize: '1.5rem',\n                color: '#6b7280',\n                cursor: 'pointer',\n                padding: '0.25rem',\n                borderRadius: '4px',\n                transition: 'color 0.2s ease'\n              },\n              onMouseEnter: e => {\n                e.currentTarget.style.color = '#374151';\n              },\n              onMouseLeave: e => {\n                e.currentTarget.style.color = '#6b7280';\n              },\n              children: \"\\xD7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1862,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1840,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '1.5rem'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem',\n                marginBottom: '1rem'\n              },\n              children: [(() => {\n                if (selectedPinnedPost.is_alert) {\n                  return /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                      color: 'white',\n                      fontSize: '0.75rem',\n                      fontWeight: '600',\n                      padding: '0.25rem 0.75rem',\n                      borderRadius: '20px',\n                      textTransform: 'uppercase',\n                      letterSpacing: '0.5px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.25rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(AlertTriangle, {\n                      size: 12,\n                      color: \"white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1909,\n                      columnNumber: 25\n                    }, this), \"Alert\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1896,\n                    columnNumber: 23\n                  }, this);\n                } else {\n                  const categoryName = (selectedPinnedPost.category_name || 'GENERAL').toUpperCase();\n                  const categoryStyle = getCategoryStyle(categoryName);\n                  const IconComponent = categoryStyle.icon;\n                  return /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      background: categoryStyle.background,\n                      color: 'white',\n                      fontSize: '0.75rem',\n                      fontWeight: '600',\n                      padding: '0.25rem 0.75rem',\n                      borderRadius: '20px',\n                      textTransform: 'uppercase',\n                      letterSpacing: '0.5px',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: '0.25rem'\n                    },\n                    children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                      size: 12,\n                      color: \"white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1932,\n                      columnNumber: 25\n                    }, this), categoryName]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1919,\n                    columnNumber: 23\n                  }, this);\n                }\n              })(), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                  color: 'white',\n                  padding: '0.25rem 0.75rem',\n                  borderRadius: '12px',\n                  fontSize: '0.75rem',\n                  fontWeight: '600',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Pin, {\n                  size: 12\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1950,\n                  columnNumber: 19\n                }, this), \"PINNED\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1939,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1887,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n              style: {\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '700',\n                color: '#111827',\n                lineHeight: '1.3'\n              },\n              children: selectedPinnedPost.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1955,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#4b5563',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                marginBottom: '1.5rem'\n              },\n              children: selectedPinnedPost.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1965,\n              columnNumber: 15\n            }, this), selectedPinnedPost.attachments && selectedPinnedPost.attachments.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginBottom: '1.5rem'\n              },\n              children: /*#__PURE__*/_jsxDEV(FacebookImageGallery, {\n                images: selectedPinnedPost.attachments.map(img => getImageUrl(img.file_path)).filter(Boolean),\n                altPrefix: selectedPinnedPost.title,\n                onImageClick: index => {\n                  const imageUrls = selectedPinnedPost.attachments.map(img => getImageUrl(img.file_path)).filter(Boolean);\n                  openLightbox(imageUrls, index);\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1977,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1976,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem',\n                fontSize: '0.875rem',\n                color: '#6b7280',\n                paddingTop: '1rem',\n                borderTop: '1px solid #e5e7eb'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Calendar, {\n                  size: 16\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2002,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [\"Published: \", new Date(selectedPinnedPost.created_at).toLocaleDateString()]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 2003,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 1997,\n                columnNumber: 17\n              }, this), selectedPinnedPost.author_name && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [\"By: \", selectedPinnedPost.author_name]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 2006,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1988,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1886,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1828,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1813,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(ImageLightbox, {\n        images: lightboxImages,\n        initialIndex: lightboxInitialIndex,\n        isOpen: lightboxOpen,\n        onClose: () => setLightboxOpen(false),\n        altPrefix: \"Image\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 2017,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 708,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s3(StudentNewsfeed, \"ZSYm6fZW4vBW5jnBe1Sd5JndWe8=\", false, function () {\n  return [useNavigate, useCategories];\n});\n_c3 = StudentNewsfeed;\nexport default StudentNewsfeed;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"ImageDisplay\");\n$RefreshReg$(_c2, \"FacebookImageGallery\");\n$RefreshReg$(_c3, \"StudentNewsfeed\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useCategories", "announcementService", "CommentSection", "ImageLightbox", "getImageUrl", "API_BASE_URL", "Home", "Search", "<PERSON>n", "Calendar", "MessageSquare", "Heart", "Filter", "MapPin", "BookOpen", "Users", "PartyPopper", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Clock", "Trophy", "Briefcase", "GraduationCap", "Flag", "Coffee", "Plane", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "useImageLoader", "imagePath", "_s", "imageUrl", "setImageUrl", "loading", "setLoading", "error", "setError", "loadImage", "fullUrl", "Error", "console", "log", "response", "fetch", "method", "headers", "window", "location", "origin", "mode", "ok", "status", "statusText", "blob", "objectUrl", "URL", "createObjectURL", "err", "message", "startsWith", "revokeObjectURL", "ImageDisplay", "alt", "style", "className", "onLoad", "onMouseEnter", "onMouseLeave", "_s2", "display", "alignItems", "justifyContent", "backgroundColor", "color", "children", "textAlign", "marginBottom", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "border", "fontWeight", "marginTop", "src", "e", "_c", "FacebookImageGallery", "images", "altPrefix", "maxVisible", "onImageClick", "length", "visibleImages", "slice", "remainingCount", "getImageStyle", "index", "total", "baseStyle", "width", "height", "objectFit", "cursor", "transition", "borderRadius", "getContainerStyle", "position", "overflow", "renderOverlay", "count", "top", "left", "right", "bottom", "gap", "currentTarget", "transform", "onClick", "flexDirection", "map", "image", "idx", "actualIndex", "_c2", "StudentNewsfeed", "_s3", "navigate", "getCategoryStyle", "categoryName", "styles", "background", "icon", "getHolidayTypeStyle", "holidayTypeName", "filterCategory", "setFilterCategory", "filterGradeLevel", "setFilterGradeLevel", "searchTerm", "setSearchTerm", "showComments", "setShowComments", "newComment", "setNewComment", "submittingComment", "setSubmittingComment", "selectedPinnedPost", "setSelectedPinnedPost", "lightboxOpen", "setLightboxOpen", "lightboxImages", "setLightboxImages", "lightboxInitialIndex", "setLightboxInitialIndex", "calendarEvents", "setCalendarEvents", "calendarLoading", "setCalendarLoading", "calendarError", "setCalendarError", "announcements", "setAnnouncements", "pinnedAnnouncements", "setPinnedAnnouncements", "categories", "gradeLevels", "Array", "from", "_", "i", "openLightbox", "imageUrls", "initialIndex", "fetchPublishedAnnouncements", "undefined", "data", "json", "success", "announcementsData", "pinned", "filter", "ann", "is_pinned", "fetchCalendarEvents", "eventsData", "events", "eventsWithImages", "Promise", "all", "event", "imageResponse", "calendar_id", "imageData", "attachments", "imgErr", "warn", "handleLikeToggle", "announcement", "user_reaction", "removeReaction", "announcement_id", "body", "JSON", "stringify", "reaction_type_id", "notify_admin", "handleCommentSubmit", "announcementId", "commentText", "trim", "content", "commenter_name", "commenter_email", "prev", "filteredAnnouncements", "matchesSearch", "title", "toLowerCase", "includes", "matchesCategory", "category_id", "toString", "filteredCalendarEvents", "description", "today", "Date", "setHours", "eventDate", "event_date", "isEventDateValid", "isPublished", "is_published", "displayAnnouncements", "displayEvents", "combinedContent", "item", "type", "sortDate", "created_at", "sort", "a", "b", "getTime", "minHeight", "scroll<PERSON>eh<PERSON>or", "flex", "borderBottom", "zIndex", "boxShadow", "padding", "min<PERSON><PERSON><PERSON>", "margin", "lineHeight", "max<PERSON><PERSON><PERSON>", "size", "placeholder", "value", "onChange", "target", "outline", "onFocus", "borderColor", "onBlur", "category", "name", "grade", "flexShrink", "colors", "is_alert", "category_name", "toUpperCase", "gradientColors", "substring", "toLocaleDateString", "opacity", "borderTop", "animation", "<PERSON><PERSON>ilter", "holiday_type_name", "holidayStyle", "IconComponent", "weekday", "year", "month", "day", "eventImageUrls", "for<PERSON>ach", "img", "file_path", "push", "Boolean", "filteredImages", "paddingTop", "end_date", "_newComment$announcem", "_newComment$announcem2", "_newComment$announcem3", "_newComment$announcem4", "categoryStyle", "textTransform", "letterSpacing", "author_name", "published_at", "hour", "minute", "image_url", "image_path", "legacyUrl", "dangerouslySetInnerHTML", "__html", "fill", "reaction_count", "allow_comments", "comment_count", "view_count", "resize", "fontFamily", "disabled", "allowComments", "currentUserType", "maxHeight", "stopPropagation", "isOpen", "onClose", "_c3", "$RefreshReg$"], "sources": ["D:/capstone-e-bulletin-reactjs-proj/FRONT-VCBA-E-BULLETIN-BOARD/src/pages/student/StudentNewsfeed.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useAnnouncements, useCategories } from '../../hooks/useAnnouncements';\nimport { announcementService, calendarService } from '../../services';\nimport CommentSection from '../../components/student/CommentSection';\nimport ImageLightbox from '../../components/common/ImageLightbox';\nimport type { Announcement } from '../../types/announcement.types';\nimport type { AnnouncementAttachment } from '../../services/announcementService';\nimport type { CalendarEvent } from '../../types/calendar.types';\nimport { getImageUrl, API_BASE_URL } from '../../config/constants';\nimport {\n  Home,\n  Search,\n  Pin,\n  Calendar,\n  MessageSquare,\n  Heart,\n  Filter,\n  MapPin,\n  BookOpen,\n  Users,\n  PartyPopper,\n  AlertTriangle,\n  Clock,\n  Trophy,\n  Briefcase,\n  GraduationCap,\n  Flag,\n  Coffee,\n  Plane\n} from 'lucide-react';\n\n// Custom hook for CORS-safe image loading\nconst useImageLoader = (imagePath: string | null) => {\n  const [imageUrl, setImageUrl] = useState<string | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (!imagePath) {\n      setImageUrl(null);\n      return;\n    }\n\n    const loadImage = async () => {\n      setLoading(true);\n      setError(null);\n\n      try {\n        const fullUrl = getImageUrl(imagePath);\n        if (!fullUrl) {\n          throw new Error('Invalid image path');\n        }\n\n        console.log('🔄 Fetching image via CORS-safe method:', fullUrl);\n\n        // Fetch image as blob to bypass CORS restrictions\n        const response = await fetch(fullUrl, {\n          method: 'GET',\n          headers: {\n            'Origin': window.location.origin,\n          },\n          mode: 'cors',\n        });\n\n        if (!response.ok) {\n          throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n\n        const blob = await response.blob();\n        const objectUrl = URL.createObjectURL(blob);\n        setImageUrl(objectUrl);\n\n        console.log('✅ Image loaded successfully via fetch');\n\n      } catch (err) {\n        console.error('❌ Image fetch failed:', err);\n        setError(err instanceof Error ? err.message : 'Failed to load image');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadImage();\n\n    // Cleanup object URL on unmount\n    return () => {\n      if (imageUrl && imageUrl.startsWith('blob:')) {\n        URL.revokeObjectURL(imageUrl);\n      }\n    };\n  }, [imagePath]);\n\n  return { imageUrl, loading, error };\n};\n\n// Reusable CORS-safe image component\ninterface ImageDisplayProps {\n  imagePath: string | null;\n  alt: string;\n  style?: React.CSSProperties;\n  className?: string;\n  onLoad?: (e: React.SyntheticEvent<HTMLImageElement>) => void;\n  onMouseEnter?: (e: React.MouseEvent<HTMLImageElement>) => void;\n  onMouseLeave?: (e: React.MouseEvent<HTMLImageElement>) => void;\n}\n\nconst ImageDisplay: React.FC<ImageDisplayProps> = ({\n  imagePath,\n  alt,\n  style,\n  className,\n  onLoad,\n  onMouseEnter,\n  onMouseLeave\n}) => {\n  const { imageUrl, loading, error } = useImageLoader(imagePath);\n\n  if (loading) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b'\n      }} className={className}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>⏳</div>\n          <div style={{ fontSize: '0.875rem' }}>Loading...</div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error || !imageUrl) {\n    return (\n      <div style={{\n        ...style,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        backgroundColor: '#f8fafc',\n        color: '#64748b',\n        border: '2px dashed #cbd5e1'\n      }} className={className}>\n        <div style={{ textAlign: 'center' }}>\n          <div style={{ marginBottom: '0.5rem', fontSize: '1.5rem' }}>🖼️</div>\n          <div style={{ fontSize: '0.875rem', fontWeight: '500' }}>Image unavailable</div>\n          {error && (\n            <div style={{ fontSize: '0.75rem', marginTop: '0.25rem', color: '#9ca3af' }}>\n              {error}\n            </div>\n          )}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <img\n      src={imageUrl}\n      alt={alt}\n      style={style}\n      className={className}\n      onLoad={(e) => {\n        console.log('✅ Image rendered successfully via CORS-safe method');\n        onLoad?.(e);\n      }}\n      onMouseEnter={onMouseEnter}\n      onMouseLeave={onMouseLeave}\n    />\n  );\n};\n\n// Facebook-style image gallery component\ninterface FacebookImageGalleryProps {\n  images: string[];\n  altPrefix: string;\n  maxVisible?: number;\n  onImageClick?: (index: number) => void;\n}\n\nconst FacebookImageGallery: React.FC<FacebookImageGalleryProps> = ({\n  images,\n  altPrefix,\n  maxVisible = 4,\n  onImageClick\n}) => {\n  if (!images || images.length === 0) return null;\n\n  const visibleImages = images.slice(0, maxVisible);\n  const remainingCount = images.length - maxVisible;\n\n  const getImageStyle = (index: number, total: number): React.CSSProperties => {\n    const baseStyle: React.CSSProperties = {\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover',\n      cursor: 'pointer',\n      transition: 'transform 0.2s ease, filter 0.2s ease',\n      borderRadius: index === 0 && total === 1 ? '12px' : '8px'\n    };\n\n    return baseStyle;\n  };\n\n  const getContainerStyle = (index: number, total: number): React.CSSProperties => {\n    const baseStyle: React.CSSProperties = {\n      position: 'relative',\n      overflow: 'hidden',\n      backgroundColor: '#f3f4f6'\n    };\n\n    if (total === 1) {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '400px',\n        borderRadius: '12px'\n      };\n    }\n\n    if (total === 2) {\n      return {\n        ...baseStyle,\n        width: '50%',\n        height: '300px',\n        borderRadius: '8px'\n      };\n    }\n\n    if (total === 3) {\n      if (index === 0) {\n        return {\n          ...baseStyle,\n          width: '60%',\n          height: '300px',\n          borderRadius: '8px'\n        };\n      } else {\n        return {\n          ...baseStyle,\n          width: '100%',\n          height: '148px',\n          borderRadius: '8px'\n        };\n      }\n    }\n\n    // 4+ images\n    if (index === 0) {\n      return {\n        ...baseStyle,\n        width: '60%',\n        height: '300px',\n        borderRadius: '8px'\n      };\n    } else {\n      return {\n        ...baseStyle,\n        width: '100%',\n        height: '96px',\n        borderRadius: '8px'\n      };\n    }\n  };\n\n  const renderOverlay = (index: number, count: number) => {\n    if (index === maxVisible - 1 && count > 0) {\n      return (\n        <div style={{\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.6)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          color: 'white',\n          fontSize: '1.5rem',\n          fontWeight: '600',\n          borderRadius: '8px'\n        }}>\n          +{count}\n        </div>\n      );\n    }\n    return null;\n  };\n\n  return (\n    <div style={{\n      display: 'flex',\n      gap: '4px',\n      width: '100%',\n      marginBottom: '1rem'\n    }}>\n      {/* Main image or left side */}\n      <div style={getContainerStyle(0, visibleImages.length)}>\n        <ImageDisplay\n          imagePath={visibleImages[0]}\n          alt={`${altPrefix} - Image 1`}\n          style={getImageStyle(0, visibleImages.length)}\n          onMouseEnter={(e) => {\n            e.currentTarget.style.transform = 'scale(1.02)';\n          }}\n          onMouseLeave={(e) => {\n            e.currentTarget.style.transform = 'scale(1)';\n          }}\n        />\n        {onImageClick && (\n          <div\n            style={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              cursor: 'pointer'\n            }}\n            onClick={() => onImageClick(0)}\n          />\n        )}\n      </div>\n\n      {/* Right side images (for 2+ images) */}\n      {visibleImages.length > 1 && (\n        <div style={{\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '4px',\n          width: visibleImages.length === 2 ? '50%' : '40%'\n        }}>\n          {visibleImages.slice(1).map((image, idx) => {\n            const actualIndex = idx + 1;\n            return (\n              <div\n                key={actualIndex}\n                style={getContainerStyle(actualIndex, visibleImages.length)}\n              >\n                <ImageDisplay\n                  imagePath={image}\n                  alt={`${altPrefix} - Image ${actualIndex + 1}`}\n                  style={getImageStyle(actualIndex, visibleImages.length)}\n                  onMouseEnter={(e) => {\n                    e.currentTarget.style.transform = 'scale(1.02)';\n                  }}\n                  onMouseLeave={(e) => {\n                    e.currentTarget.style.transform = 'scale(1)';\n                  }}\n                />\n                {renderOverlay(actualIndex, remainingCount)}\n                {onImageClick && (\n                  <div\n                    style={{\n                      position: 'absolute',\n                      top: 0,\n                      left: 0,\n                      right: 0,\n                      bottom: 0,\n                      cursor: 'pointer'\n                    }}\n                    onClick={() => onImageClick(actualIndex)}\n                  />\n                )}\n              </div>\n            );\n          })}\n        </div>\n      )}\n    </div>\n  );\n};\n\nconst StudentNewsfeed: React.FC = () => {\n  const navigate = useNavigate();\n\n  // Category styling function\n  const getCategoryStyle = (categoryName: string) => {\n    const styles = {\n      'ACADEMIC': {\n        background: 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)',\n        icon: BookOpen\n      },\n      'GENERAL': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Users\n      },\n      'EVENTS': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: PartyPopper\n      },\n      'EMERGENCY': {\n        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n        icon: AlertTriangle\n      },\n      'SPORTS': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Trophy\n      },\n      'DEADLINES': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Clock\n      }\n    };\n\n    return styles[categoryName as keyof typeof styles] || styles['GENERAL'];\n  };\n\n  // Holiday type styling function\n  const getHolidayTypeStyle = (holidayTypeName: string) => {\n    const styles = {\n      'NATIONAL HOLIDAY': {\n        background: 'linear-gradient(135deg, #dc2626 0%, #b91c1c 100%)',\n        icon: Flag\n      },\n      'SCHOOL EVENT': {\n        background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n        icon: GraduationCap\n      },\n      'ACADEMIC BREAK': {\n        background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',\n        icon: Coffee\n      },\n      'SPORTS EVENT': {\n        background: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',\n        icon: Trophy\n      },\n      'FIELD TRIP': {\n        background: 'linear-gradient(135deg, #06b6d4 0%, #0891b2 100%)',\n        icon: Plane\n      },\n      'MEETING': {\n        background: 'linear-gradient(135deg, #6b7280 0%, #4b5563 100%)',\n        icon: Briefcase\n      }\n    };\n\n    return styles[holidayTypeName as keyof typeof styles] || styles['SCHOOL EVENT'];\n  };\n\n  // Filter states\n  const [filterCategory, setFilterCategory] = useState<string>('');\n  const [filterGradeLevel, setFilterGradeLevel] = useState<string>('');\n  const [searchTerm, setSearchTerm] = useState('');\n  \n  // UI states\n  const [showComments, setShowComments] = useState<number | null>(null);\n  const [newComment, setNewComment] = useState<{ [key: number]: string }>({});\n  const [submittingComment, setSubmittingComment] = useState<number | null>(null);\n  const [selectedPinnedPost, setSelectedPinnedPost] = useState<any | null>(null);\n\n  // Lightbox states\n  const [lightboxOpen, setLightboxOpen] = useState(false);\n  const [lightboxImages, setLightboxImages] = useState<string[]>([]);\n  const [lightboxInitialIndex, setLightboxInitialIndex] = useState(0);\n\n  // Data states\n  const [calendarEvents, setCalendarEvents] = useState<CalendarEvent[]>([]);\n  const [calendarLoading, setCalendarLoading] = useState(false);\n  const [calendarError, setCalendarError] = useState<string | undefined>();\n  const [announcements, setAnnouncements] = useState<any[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | undefined>();\n  const [pinnedAnnouncements, setPinnedAnnouncements] = useState<any[]>([]);\n\n  const { categories } = useCategories();\n\n  // Grade levels for dropdown (11-12)\n  const gradeLevels = Array.from({ length: 2 }, (_, i) => i + 11); // [11, 12]\n\n  // Open lightbox function\n  const openLightbox = (imageUrls: string[], initialIndex: number) => {\n    setLightboxImages(imageUrls);\n    setLightboxInitialIndex(initialIndex);\n    setLightboxOpen(true);\n  };\n\n  // Fetch published announcements with images\n  const fetchPublishedAnnouncements = async () => {\n    try {\n      setLoading(true);\n      setError(undefined);\n\n      const response = await fetch(`${API_BASE_URL}/api/announcements?status=published&page=1&limit=50&sort_by=created_at&sort_order=DESC`);\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        const announcementsData = data.data.announcements || [];\n\n        // The announcements already include attachments/images from the API\n        // No need to fetch images separately\n        setAnnouncements(announcementsData);\n\n        // Separate pinned announcements\n        const pinned = announcementsData.filter((ann: any) => ann.is_pinned === 1);\n        setPinnedAnnouncements(pinned);\n      } else {\n        setError('Failed to load announcements');\n      }\n    } catch (err: any) {\n      console.error('Error fetching announcements:', err);\n      setError(err.message || 'Failed to load announcements');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fetch calendar events\n  const fetchCalendarEvents = async () => {\n    try {\n      setCalendarLoading(true);\n      setCalendarError(undefined);\n\n      const response = await fetch(`${API_BASE_URL}/api/calendar?limit=50&sort_by=event_date&sort_order=ASC`);\n      const data = await response.json();\n\n      if (data.success && data.data) {\n        const eventsData = data.data.events || data.data || [];\n\n        // Fetch images for each event\n        const eventsWithImages = await Promise.all(\n          eventsData.map(async (event: any) => {\n            try {\n              const imageResponse = await fetch(`${API_BASE_URL}/api/calendar/${event.calendar_id}/images`);\n              const imageData = await imageResponse.json();\n\n              if (imageData.success && imageData.data) {\n                event.images = imageData.data.attachments || [];\n              } else {\n                event.images = [];\n              }\n            } catch (imgErr) {\n              console.warn(`Failed to fetch images for event ${event.calendar_id}:`, imgErr);\n              event.images = [];\n            }\n            return event;\n          })\n        );\n\n        setCalendarEvents(eventsWithImages);\n      } else {\n        setCalendarError('Failed to load calendar events');\n      }\n    } catch (err: any) {\n      console.error('Error fetching calendar events:', err);\n      setCalendarError(err.message || 'Failed to load calendar events');\n    } finally {\n      setCalendarLoading(false);\n    }\n  };\n\n  // Initial data fetch\n  useEffect(() => {\n    fetchPublishedAnnouncements();\n    fetchCalendarEvents();\n  }, []);\n\n  // Handle like/unlike functionality\n  const handleLikeToggle = async (announcement: any) => {\n    try {\n      if (announcement.user_reaction) {\n        await announcementService.removeReaction(announcement.announcement_id);\n      } else {\n        const response = await fetch(`${API_BASE_URL}/api/announcements/${announcement.announcement_id}/reactions`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            reaction_type_id: 1,\n            notify_admin: true\n          })\n        });\n\n        if (!response.ok) {\n          throw new Error('Failed to add reaction');\n        }\n      }\n      \n      // Refresh data\n      await fetchPublishedAnnouncements();\n    } catch (error) {\n      console.error('Error toggling like:', error);\n    }\n  };\n\n  // Handle comment submission\n  const handleCommentSubmit = async (announcementId: number, commentText: string) => {\n    if (!commentText.trim()) return;\n\n    try {\n      setSubmittingComment(announcementId);\n\n      const response = await fetch(`${API_BASE_URL}/api/announcements/${announcementId}/comments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          content: commentText,\n          commenter_name: 'Student User',\n          commenter_email: '<EMAIL>',\n          notify_admin: true\n        })\n      });\n\n      if (response.ok) {\n        setNewComment(prev => ({ ...prev, [announcementId]: '' }));\n        await fetchPublishedAnnouncements();\n      } else {\n        console.error('Failed to submit comment');\n      }\n    } catch (error) {\n      console.error('Error submitting comment:', error);\n    } finally {\n      setSubmittingComment(null);\n    }\n  };\n\n  // Filter announcements\n  const filteredAnnouncements = announcements.filter(announcement => {\n    const matchesSearch = !searchTerm ||\n      announcement.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      announcement.content.toLowerCase().includes(searchTerm.toLowerCase());\n    \n    const matchesCategory = !filterCategory || announcement.category_id.toString() === filterCategory;\n    \n    // Note: Grade level filtering would require additional backend support\n    // For now, we'll show all announcements regardless of grade level filter\n    \n    return matchesSearch && matchesCategory;\n  });\n\n  // Filter calendar events with date-based filtering\n  const filteredCalendarEvents = calendarEvents.filter(event => {\n    const matchesSearch = !searchTerm ||\n      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      (event.description && event.description.toLowerCase().includes(searchTerm.toLowerCase()));\n\n    // Only show events that are published and on or after today's date\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    const eventDate = new Date(event.event_date);\n    eventDate.setHours(0, 0, 0, 0); // Reset time to start of day\n\n    const isEventDateValid = eventDate >= today;\n    const isPublished = (event as any).is_published === 1;\n\n    return matchesSearch && isEventDateValid && isPublished;\n  });\n\n  // Combine and sort all content by date (most recent first)\n  const displayAnnouncements = filteredAnnouncements;\n  const displayEvents = filteredCalendarEvents;\n\n  // Create combined content array for better chronological display\n  const combinedContent = [\n    ...displayAnnouncements.map(item => ({ ...item, type: 'announcement', sortDate: new Date(item.created_at) })),\n    ...displayEvents.map(item => ({ ...item, type: 'event', sortDate: new Date(item.event_date) }))\n  ].sort((a, b) => b.sortDate.getTime() - a.sortDate.getTime());\n\n  return (\n    <>\n      {/* CSS Animations */}\n      <style>\n        {`\n          html {\n            scroll-behavior: smooth;\n          }\n\n          @keyframes spin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n          }\n\n          @media (max-width: 768px) {\n            .mobile-hide { display: none !important; }\n            .mobile-full { width: 100% !important; margin-left: 0 !important; }\n            .mobile-stack {\n              flex-direction: column !important;\n              gap: 0.75rem !important;\n            }\n            .mobile-grid { grid-template-columns: 1fr !important; }\n          }\n\n          @media (max-width: 480px) {\n            .mobile-small-padding { padding: 0.75rem !important; }\n            .mobile-small-text { font-size: 0.8rem !important; }\n            .mobile-compact-header {\n              padding: 0.75rem 1rem !important;\n            }\n            .mobile-compact-title {\n              font-size: 1.25rem !important;\n            }\n            .mobile-compact-search {\n              max-width: 200px !important;\n            }\n          }\n        `}\n      </style>\n\n      <div style={{\n        display: 'flex',\n        minHeight: '100vh',\n        background: 'linear-gradient(135deg, #f0f9ff 0%, #fefce8 100%)',\n        scrollBehavior: 'smooth'\n      }}>\n\n      {/* Main Content Area */}\n      <div\n        className=\"mobile-full\"\n        style={{\n          flex: 1,\n          display: 'flex',\n          flexDirection: 'column',\n          minHeight: '100vh',\n          width: '100%'\n        }}>\n        {/* Modern Student Header */}\n        <header style={{\n          background: 'white',\n          borderBottom: '1px solid #e5e7eb',\n          position: 'sticky',\n          top: 0,\n          zIndex: 100,\n          boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'\n        }}>\n          <div style={{\n            width: '100%',\n            padding: '0 2rem',\n            height: '72px',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          }}>\n            {/* Left Section: Logo + Page Title */}\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1.5rem',\n              minWidth: '300px'\n            }}>\n              <img\n                src=\"/logo/vcba1.png\"\n                alt=\"VCBA Logo\"\n                style={{\n                  width: '48px',\n                  height: '48px',\n                  objectFit: 'contain'\n                }}\n              />\n              <div>\n                <h1 style={{\n                  margin: 0,\n                  fontSize: '1.5rem',\n                  fontWeight: '700',\n                  color: '#111827',\n                  lineHeight: '1.2'\n                }}>\n                  VCBA E-Bulletin Board\n                </h1>\n                <p style={{\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: '#6b7280'\n                }}>\n                  Student Newsfeed\n                </p>\n              </div>\n            </div>\n\n            {/* Center Section: Search */}\n            <div style={{\n              flex: 1,\n              maxWidth: '500px',\n              margin: '0 2rem'\n            }}>\n              <div style={{ position: 'relative' }}>\n                <Search\n                  size={20}\n                  style={{\n                    position: 'absolute',\n                    left: '1rem',\n                    top: '50%',\n                    transform: 'translateY(-50%)',\n                    color: '#9ca3af'\n                  }}\n                />\n                <input\n                  type=\"text\"\n                  placeholder=\"Search post\"\n                  value={searchTerm}\n                  onChange={(e) => setSearchTerm(e.target.value)}\n                  style={{\n                    width: '100%',\n                    height: '44px',\n                    padding: '0 1rem 0 3rem',\n                    border: '1px solid #d1d5db',\n                    borderRadius: '12px',\n                    background: '#f9fafb',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    transition: 'all 0.2s ease'\n                  }}\n                  onFocus={(e) => {\n                    e.target.style.borderColor = '#3b82f6';\n                    e.target.style.background = 'white';\n                    e.target.style.boxShadow = '0 0 0 3px rgba(59, 130, 246, 0.1)';\n                  }}\n                  onBlur={(e) => {\n                    e.target.style.borderColor = '#d1d5db';\n                    e.target.style.background = '#f9fafb';\n                    e.target.style.boxShadow = 'none';\n                  }}\n                />\n              </div>\n            </div>\n\n            {/* Right Section: Filters + Actions */}\n            <div style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '1rem',\n              minWidth: '400px',\n              justifyContent: 'flex-end'\n            }}>\n              {/* Filters Group */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.5rem',\n                padding: '0.5rem',\n                background: '#f9fafb',\n                borderRadius: '12px',\n                border: '1px solid #e5e7eb'\n              }}>\n                <select\n                  value={filterCategory}\n                  onChange={(e) => setFilterCategory(e.target.value)}\n                  style={{\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '110px'\n                  }}\n                >\n                  <option value=\"\">All Categories</option>\n                  {categories.map(category => (\n                    <option key={category.category_id} value={category.category_id.toString()}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n\n                <select\n                  value={filterGradeLevel}\n                  onChange={(e) => setFilterGradeLevel(e.target.value)}\n                  style={{\n                    padding: '0.5rem 0.75rem',\n                    border: 'none',\n                    borderRadius: '8px',\n                    background: 'white',\n                    color: '#374151',\n                    fontSize: '0.875rem',\n                    outline: 'none',\n                    cursor: 'pointer',\n                    minWidth: '100px'\n                  }}\n                >\n                  <option value=\"\">All Grades</option>\n                  {gradeLevels.map(grade => (\n                    <option key={grade} value={grade.toString()}>\n                      Grade {grade}\n                    </option>\n                  ))}\n                </select>\n\n                {(searchTerm || filterCategory || filterGradeLevel) && (\n                  <button\n                    onClick={() => {\n                      setSearchTerm('');\n                      setFilterCategory('');\n                      setFilterGradeLevel('');\n                    }}\n                    style={{\n                      padding: '0.5rem 0.75rem',\n                      border: 'none',\n                      borderRadius: '8px',\n                      background: '#ef4444',\n                      color: 'white',\n                      fontSize: '0.875rem',\n                      fontWeight: '500',\n                      cursor: 'pointer',\n                      transition: 'all 0.2s ease'\n                    }}\n                    onMouseEnter={(e) => {\n                      e.currentTarget.style.background = '#dc2626';\n                    }}\n                    onMouseLeave={(e) => {\n                      e.currentTarget.style.background = '#ef4444';\n                    }}\n                  >\n                    Clear\n                  </button>\n                )}\n              </div>\n\n\n\n              {/* Dashboard Button */}\n              <button\n                onClick={() => navigate('/student/dashboard')}\n                style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem',\n                  padding: '0.5rem 1rem',\n                  border: '1px solid #d1d5db',\n                  borderRadius: '8px',\n                  background: 'white',\n                  color: '#374151',\n                  fontSize: '0.875rem',\n                  fontWeight: '500',\n                  cursor: 'pointer',\n                  transition: 'all 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.borderColor = '#3b82f6';\n                  e.currentTarget.style.color = '#3b82f6';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.borderColor = '#d1d5db';\n                  e.currentTarget.style.color = '#374151';\n                }}\n              >\n                <Home size={16} />\n                Dashboard\n              </button>\n            </div>\n          </div>\n        </header>\n\n        {/* Main Content Layout */}\n        <div style={{\n          flex: 1,\n          display: 'flex',\n          gap: '1.5rem',\n          padding: '1.5rem 2rem',\n          background: 'transparent',\n          alignItems: 'flex-start'\n        }}>\n          {/* Left Sidebar: Pinned Posts */}\n          <div style={{\n            width: '280px',\n            flexShrink: 0\n          }}>\n            <div style={{\n              background: 'white',\n              borderRadius: '14px',\n              border: '1px solid #e5e7eb',\n              overflow: 'hidden',\n              position: 'sticky',\n              top: '80px',\n              boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'\n            }}>\n              {/* Pinned Posts Header */}\n              <div style={{\n                padding: '1rem 1.25rem 0.75rem',\n                borderBottom: '1px solid #f3f4f6',\n                background: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.75rem',\n                  marginBottom: '0.5rem'\n                }}>\n                  <Pin size={20} style={{ color: 'white' }} />\n                  <h3 style={{\n                    margin: 0,\n                    fontSize: '1.125rem',\n                    fontWeight: '600',\n                    color: 'white'\n                  }}>\n                    Important Updates\n                  </h3>\n                </div>\n                <p style={{\n                  margin: 0,\n                  fontSize: '0.875rem',\n                  color: 'rgba(255, 255, 255, 0.8)'\n                }}>\n                  Don't miss these announcements\n                </p>\n              </div>\n\n              {/* Pinned Posts List */}\n              <div style={{ padding: '0.75rem' }}>\n                {pinnedAnnouncements.length > 0 ? (\n                  <>\n                    {pinnedAnnouncements.slice(0, 3).map((announcement, index) => {\n                      // Handle alert announcements with special styling\n                      let colors;\n                      if (announcement.is_alert) {\n                        colors = ['#fee2e2', '#fecaca', '#ef4444']; // Red alert colors\n                      } else {\n                        const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n\n                        // Create gradient background based on category\n                        const gradientColors = {\n                          'ACADEMIC': ['#dbeafe', '#bfdbfe', '#3b82f6'],\n                          'EVENTS': ['#fef3c7', '#fde68a', '#f59e0b'],\n                          'EMERGENCY': ['#fee2e2', '#fecaca', '#ef4444'],\n                          'SPORTS': ['#dcfce7', '#bbf7d0', '#22c55e'],\n                          'DEADLINES': ['#ede9fe', '#ddd6fe', '#8b5cf6'],\n                          'GENERAL': ['#f3f4f6', '#e5e7eb', '#6b7280']\n                        };\n\n                        colors = gradientColors[categoryName as keyof typeof gradientColors] || gradientColors['GENERAL'];\n                      }\n\n                      return (\n                        <div\n                          key={announcement.announcement_id}\n                          style={{\n                            padding: '0.75rem',\n                            background: `linear-gradient(135deg, ${colors[0]} 0%, ${colors[1]} 100%)`,\n                            borderRadius: '10px',\n                            border: `1px solid ${colors[2]}`,\n                            marginBottom: '0.75rem',\n                            cursor: 'pointer',\n                            transition: 'all 0.2s ease'\n                          }}\n                          onMouseEnter={(e) => {\n                            e.currentTarget.style.transform = 'translateY(-2px)';\n                            e.currentTarget.style.boxShadow = `0 8px 25px ${colors[2]}30`;\n                          }}\n                          onMouseLeave={(e) => {\n                            e.currentTarget.style.transform = 'translateY(0)';\n                            e.currentTarget.style.boxShadow = 'none';\n                          }}\n                          onClick={() => setSelectedPinnedPost(announcement)}\n                        >\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'flex-start',\n                            gap: '0.75rem'\n                          }}>\n                            <div style={{\n                              width: '10px',\n                              height: '10px',\n                              background: colors[2],\n                              borderRadius: '50%',\n                              marginTop: '0.5rem',\n                              flexShrink: 0,\n                              boxShadow: `0 0 0 3px ${colors[2]}30`\n                            }} />\n                            <div style={{ flex: 1 }}>\n                              <h4 style={{\n                                margin: '0 0 0.5rem 0',\n                                fontSize: '0.875rem',\n                                fontWeight: '600',\n                                color: colors[2],\n                                lineHeight: '1.4'\n                              }}>\n                                {announcement.title}\n                              </h4>\n                              <p style={{\n                                margin: '0 0 0.5rem 0',\n                                fontSize: '0.8rem',\n                                color: colors[2],\n                                lineHeight: '1.4'\n                              }}>\n                                {announcement.content.length > 60\n                                  ? `${announcement.content.substring(0, 60)}...`\n                                  : announcement.content}\n                              </p>\n                              <div style={{\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                fontSize: '0.75rem',\n                                color: colors[2]\n                              }}>\n                                <Calendar size={12} />\n                                <span>{new Date(announcement.created_at).toLocaleDateString()}</span>\n                              </div>\n                            </div>\n                          </div>\n                        </div>\n                      );\n                    })}\n\n\n                    {pinnedAnnouncements.length > 3 && (\n                      <button style={{\n                        width: '100%',\n                        padding: '0.75rem',\n                        border: '1px solid #e5e7eb',\n                        borderRadius: '12px',\n                        background: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)',\n                        color: '#3b82f6',\n                        fontSize: '0.875rem',\n                        fontWeight: '600',\n                        cursor: 'pointer',\n                        transition: 'all 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.background = 'linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%)';\n                        e.currentTarget.style.borderColor = '#3b82f6';\n                        e.currentTarget.style.transform = 'translateY(-1px)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.background = 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%)';\n                        e.currentTarget.style.borderColor = '#e5e7eb';\n                        e.currentTarget.style.transform = 'translateY(0)';\n                      }}>\n                        📌 View All {pinnedAnnouncements.length} Important Updates\n                      </button>\n                    )}\n                  </>\n                ) : (\n                  <div style={{\n                    padding: '2rem 1rem',\n                    textAlign: 'center',\n                    color: '#6b7280'\n                  }}>\n                    <Pin size={24} style={{ marginBottom: '0.5rem', opacity: 0.5 }} />\n                    <p style={{ margin: 0, fontSize: '0.875rem' }}>\n                      No pinned posts available\n                    </p>\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Right Content: Main Feed */}\n          <div style={{ flex: 1, minWidth: 0 }}>\n          {/* Loading State */}\n          {(loading || calendarLoading) && (\n            <div style={{\n              display: 'flex',\n              justifyContent: 'center',\n              alignItems: 'center',\n              padding: '3rem'\n            }}>\n              <div style={{\n                width: '2.5rem',\n                height: '2.5rem',\n                border: '3px solid #e5e7eb',\n                borderTop: '3px solid #3b82f6',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }}></div>\n            </div>\n          )}\n\n          {/* Error Messages */}\n          {(error || calendarError) && (\n            <div style={{\n              background: 'rgba(239, 68, 68, 0.1)',\n              border: '1px solid rgba(239, 68, 68, 0.2)',\n              borderRadius: '12px',\n              padding: '1rem',\n              marginBottom: '1.5rem',\n              color: '#dc2626'\n            }}>\n              {error && <div>Announcements: {error}</div>}\n              {calendarError && <div>Calendar: {calendarError}</div>}\n            </div>\n          )}\n\n          {/* No Content */}\n          {!loading && !calendarLoading && displayAnnouncements.length === 0 && displayEvents.length === 0 && (\n            <div style={{\n              background: 'rgba(255, 255, 255, 0.8)',\n              borderRadius: '16px',\n              padding: '3rem',\n              textAlign: 'center',\n              border: '1px solid rgba(0, 0, 0, 0.1)',\n              backdropFilter: 'blur(10px)'\n            }}>\n              <div style={{\n                marginBottom: '1rem'\n              }}>\n                <Filter size={48} color=\"#9ca3af\" />\n              </div>\n              <h3 style={{\n                color: '#374151',\n                fontSize: '1.25rem',\n                fontWeight: '600',\n                margin: '0 0 0.5rem 0'\n              }}>\n                No updates found\n              </h3>\n              <p style={{\n                color: '#6b7280',\n                margin: 0\n              }}>\n                {searchTerm || filterCategory || filterGradeLevel\n                  ? 'Try adjusting your filters to see more content.'\n                  : 'Check back later for new updates.'}\n              </p>\n            </div>\n          )}\n\n          {/* Content Feed */}\n          {!loading && !calendarLoading && (displayAnnouncements.length > 0 || displayEvents.length > 0) && (\n            <div style={{\n              maxWidth: '1200px',\n              margin: '0 auto',\n              display: 'flex',\n              flexDirection: 'column',\n              gap: '1.5rem'\n            }}>\n              {/* Calendar Events */}\n              {displayEvents.length > 0 && (\n                <>\n                  {displayEvents.map(event => (\n                    <div\n                      key={`event-${event.calendar_id}`}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Event Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        {(() => {\n                          const holidayTypeName = (event.holiday_type_name || 'SCHOOL EVENT').toUpperCase();\n                          const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                          const IconComponent = holidayStyle.icon;\n\n                          return (\n                            <div style={{\n                              width: '50px',\n                              height: '50px',\n                              borderRadius: '12px',\n                              background: holidayStyle.background,\n                              display: 'flex',\n                              alignItems: 'center',\n                              justifyContent: 'center'\n                            }}>\n                              <IconComponent size={20} color=\"white\" />\n                            </div>\n                          );\n                        })()}\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            marginBottom: '0.25rem'\n                          }}>\n                            {(() => {\n                              const holidayTypeName = (event.holiday_type_name || 'SCHOOL EVENT').toUpperCase();\n                              const holidayStyle = getHolidayTypeStyle(holidayTypeName);\n                              const IconComponent = holidayStyle.icon;\n\n                              return (\n                                <span style={{\n                                  background: holidayStyle.background,\n                                  color: 'white',\n                                  padding: '0.25rem 0.75rem',\n                                  borderRadius: '12px',\n                                  fontSize: '0.75rem',\n                                  fontWeight: '600',\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  gap: '0.25rem'\n                                }}>\n                                  <IconComponent size={12} color=\"white\" />\n                                  {holidayTypeName}\n                                </span>\n                              );\n                            })()}\n                          </div>\n                          <div style={{\n                            color: '#9ca3af',\n                            fontSize: '0.8rem'\n                          }}>\n                            {new Date(event.event_date).toLocaleDateString('en-US', {\n                              weekday: 'long',\n                              year: 'numeric',\n                              month: 'long',\n                              day: 'numeric'\n                            })}\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Event Content */}\n                      <h3 style={{\n                        margin: '0 0 0.75rem 0',\n                        color: '#1f2937',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        lineHeight: '1.3'\n                      }}>\n                        {event.title}\n                      </h3>\n\n                      {event.description && (\n                        <p style={{\n                          margin: '0 0 1rem 0',\n                          color: '#4b5563',\n                          fontSize: '0.95rem',\n                          lineHeight: '1.6'\n                        }}>\n                          {event.description}\n                        </p>\n                      )}\n\n                      {/* Event Images */}\n                      {(() => {\n                        // Get event images if they exist\n                        const eventImageUrls: string[] = [];\n\n                        if ((event as any).images && (event as any).images.length > 0) {\n                          (event as any).images.forEach((img: any) => {\n                            if (img.file_path) {\n                              // Convert file_path to full URL\n                              const imageUrl = getImageUrl(img.file_path);\n                              if (imageUrl) {\n                                eventImageUrls.push(imageUrl);\n                              }\n                            }\n                          });\n                        }\n\n                        return eventImageUrls.length > 0 ? (\n                          <div style={{ marginBottom: '1rem' }}>\n                            <FacebookImageGallery\n                              images={eventImageUrls.filter(Boolean) as string[]}\n                              altPrefix={event.title}\n                              maxVisible={4}\n                              onImageClick={(index) => {\n                                const filteredImages = eventImageUrls.filter(Boolean) as string[];\n                                openLightbox(filteredImages, index);\n                              }}\n                            />\n                          </div>\n                        ) : null;\n                      })()}\n\n                      {/* Event Footer */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        paddingTop: '1rem',\n                        borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem',\n                          color: '#6b7280',\n                          fontSize: '0.875rem'\n                        }}>\n                          <span style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>\n                            <MapPin size={14} color=\"#6b7280\" />\n                            School Event\n                          </span>\n                          {event.end_date && event.end_date !== event.event_date && (\n                            <span>\n                              Until {new Date(event.end_date).toLocaleDateString()}\n                            </span>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </>\n              )}\n\n              {/* Announcements */}\n              {displayAnnouncements.length > 0 && (\n                <>\n                  {displayAnnouncements.map(announcement => (\n                    <div\n                      key={`announcement-${announcement.announcement_id}`}\n                      style={{\n                        background: 'rgba(255, 255, 255, 0.95)',\n                        borderRadius: '16px',\n                        padding: '1.5rem',\n                        border: '1px solid rgba(0, 0, 0, 0.1)',\n                        backdropFilter: 'blur(10px)',\n                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)',\n                        transition: 'transform 0.2s ease, box-shadow 0.2s ease'\n                      }}\n                      onMouseEnter={(e) => {\n                        e.currentTarget.style.transform = 'translateY(-2px)';\n                        e.currentTarget.style.boxShadow = '0 8px 30px rgba(0, 0, 0, 0.12)';\n                      }}\n                      onMouseLeave={(e) => {\n                        e.currentTarget.style.transform = 'translateY(0)';\n                        e.currentTarget.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.08)';\n                      }}\n                    >\n                      {/* Announcement Header */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '1rem',\n                        marginBottom: '1rem'\n                      }}>\n                        {(() => {\n                          if (announcement.is_alert) {\n                            return (\n                              <div style={{\n                                width: '50px',\n                                height: '50px',\n                                background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                                borderRadius: '12px',\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center',\n                                flexShrink: 0\n                              }}>\n                                <AlertTriangle size={20} color=\"white\" />\n                              </div>\n                            );\n                          } else {\n                            const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                            const categoryStyle = getCategoryStyle(categoryName);\n                            const IconComponent = categoryStyle.icon;\n\n                            return (\n                              <div style={{\n                                width: '50px',\n                                height: '50px',\n                                borderRadius: '12px',\n                                background: categoryStyle.background,\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center'\n                              }}>\n                                <IconComponent size={20} color=\"white\" />\n                              </div>\n                            );\n                          }\n                        })()}\n                        <div style={{ flex: 1 }}>\n                          <div style={{\n                            display: 'flex',\n                            alignItems: 'center',\n                            gap: '0.5rem',\n                            marginBottom: '0.25rem'\n                          }}>\n                            {(() => {\n                              if (announcement.is_alert) {\n                                return (\n                                  <span style={{\n                                    background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                                    color: 'white',\n                                    fontSize: '0.75rem',\n                                    fontWeight: '600',\n                                    padding: '0.25rem 0.75rem',\n                                    borderRadius: '12px',\n                                    textTransform: 'uppercase',\n                                    letterSpacing: '0.5px',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.25rem'\n                                  }}>\n                                    <AlertTriangle size={12} color=\"white\" />\n                                    Alert\n                                  </span>\n                                );\n                              } else {\n                                const categoryName = (announcement.category_name || 'GENERAL').toUpperCase();\n                                const categoryStyle = getCategoryStyle(categoryName);\n                                const IconComponent = categoryStyle.icon;\n\n                                return (\n                                  <span style={{\n                                    background: categoryStyle.background,\n                                    color: 'white',\n                                    padding: '0.25rem 0.75rem',\n                                    borderRadius: '12px',\n                                    fontSize: '0.75rem',\n                                    fontWeight: '600',\n                                    display: 'flex',\n                                    alignItems: 'center',\n                                    gap: '0.25rem'\n                                  }}>\n                                    <IconComponent size={12} color=\"white\" />\n                                    {categoryName}\n                                  </span>\n                                );\n                              }\n                            })()}\n                            {announcement.is_pinned === 1 && (\n                              <span style={{\n                                background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',\n                                color: 'white',\n                                padding: '0.25rem 0.75rem',\n                                borderRadius: '12px',\n                                fontSize: '0.75rem',\n                                fontWeight: '600',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.25rem'\n                              }}>\n                                <Pin size={12} color=\"white\" />\n                                PINNED\n                              </span>\n                            )}\n                          </div>\n                          <div style={{\n                            color: '#9ca3af',\n                            fontSize: '0.8rem'\n                          }}>\n                            By {announcement.author_name} • {new Date(announcement.published_at).toLocaleDateString('en-US', {\n                              weekday: 'short',\n                              year: 'numeric',\n                              month: 'short',\n                              day: 'numeric',\n                              hour: '2-digit',\n                              minute: '2-digit'\n                            })}\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Announcement Content */}\n                      <h3 style={{\n                        margin: '0 0 0.75rem 0',\n                        color: '#1f2937',\n                        fontSize: '1.25rem',\n                        fontWeight: '700',\n                        lineHeight: '1.3'\n                      }}>\n                        {announcement.title}\n                      </h3>\n\n                      {/* Images - Facebook-style Gallery */}\n                      {(() => {\n                        // Get images from multiple sources\n                        const imageUrls: string[] = [];\n\n                        // Add images from attachments (new multiple image system)\n                        if (announcement.attachments && announcement.attachments.length > 0) {\n                          announcement.attachments.forEach((img: AnnouncementAttachment) => {\n                            if (img.file_path) {\n                              // Use getImageUrl to construct the full URL\n                              const fullUrl = getImageUrl(img.file_path);\n                              if (fullUrl) {\n                                imageUrls.push(fullUrl);\n                              }\n                            }\n                          });\n                        }\n\n                        // Fallback to legacy single image\n                        if (imageUrls.length === 0 && (announcement.image_url || announcement.image_path)) {\n                          const legacyUrl = getImageUrl(announcement.image_url || announcement.image_path);\n                          if (legacyUrl) {\n                            imageUrls.push(legacyUrl);\n                          }\n                        }\n\n                        return imageUrls.length > 0 ? (\n                          <FacebookImageGallery\n                            images={imageUrls.filter(Boolean) as string[]}\n                            altPrefix={announcement.title}\n                            maxVisible={4}\n                            onImageClick={(index) => {\n                              const filteredImages = imageUrls.filter(Boolean) as string[];\n                              openLightbox(filteredImages, index);\n                            }}\n                          />\n                        ) : null;\n                      })()}\n\n                      <div style={{\n                        margin: '0 0 1.5rem 0',\n                        color: '#4b5563',\n                        fontSize: '0.95rem',\n                        lineHeight: '1.6'\n                      }}\n                      dangerouslySetInnerHTML={{ __html: announcement.content }}\n                      />\n\n                      {/* Announcement Actions */}\n                      <div style={{\n                        display: 'flex',\n                        alignItems: 'center',\n                        justifyContent: 'space-between',\n                        paddingTop: '1rem',\n                        borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                      }}>\n                        <div style={{\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: '1rem'\n                        }}>\n                          {/* Like Button */}\n                          <button\n                            onClick={() => handleLikeToggle(announcement)}\n                            style={{\n                              background: announcement.user_reaction ? 'rgba(239, 68, 68, 0.1)' : 'transparent',\n                              color: announcement.user_reaction ? '#dc2626' : '#6b7280',\n                              border: 'none',\n                              borderRadius: '8px',\n                              padding: '0.5rem 1rem',\n                              fontSize: '0.875rem',\n                              fontWeight: '600',\n                              cursor: 'pointer',\n                              display: 'flex',\n                              alignItems: 'center',\n                              gap: '0.5rem',\n                              transition: 'all 0.2s ease'\n                            }}\n                            onMouseEnter={(e) => {\n                              if (!announcement.user_reaction) {\n                                e.currentTarget.style.background = 'rgba(239, 68, 68, 0.1)';\n                                e.currentTarget.style.color = '#dc2626';\n                              }\n                            }}\n                            onMouseLeave={(e) => {\n                              if (!announcement.user_reaction) {\n                                e.currentTarget.style.background = 'transparent';\n                                e.currentTarget.style.color = '#6b7280';\n                              }\n                            }}\n                          >\n                            <Heart\n                              size={16}\n                              color={announcement.user_reaction ? \"#dc2626\" : \"#9ca3af\"}\n                              fill={announcement.user_reaction ? \"#dc2626\" : \"none\"}\n                            />\n                            <span>{announcement.reaction_count || 0}</span>\n                          </button>\n\n                          {/* Comment Button */}\n                          {announcement.allow_comments && (\n                            <button\n                              onClick={() => setShowComments(\n                                showComments === announcement.announcement_id ? null : announcement.announcement_id\n                              )}\n                              style={{\n                                background: showComments === announcement.announcement_id ? 'rgba(59, 130, 246, 0.1)' : 'transparent',\n                                color: showComments === announcement.announcement_id ? '#3b82f6' : '#6b7280',\n                                border: 'none',\n                                borderRadius: '8px',\n                                padding: '0.5rem 1rem',\n                                fontSize: '0.875rem',\n                                fontWeight: '600',\n                                cursor: 'pointer',\n                                display: 'flex',\n                                alignItems: 'center',\n                                gap: '0.5rem',\n                                transition: 'all 0.2s ease'\n                              }}\n                              onMouseEnter={(e) => {\n                                if (showComments !== announcement.announcement_id) {\n                                  e.currentTarget.style.background = 'rgba(59, 130, 246, 0.1)';\n                                  e.currentTarget.style.color = '#3b82f6';\n                                }\n                              }}\n                              onMouseLeave={(e) => {\n                                if (showComments !== announcement.announcement_id) {\n                                  e.currentTarget.style.background = 'transparent';\n                                  e.currentTarget.style.color = '#6b7280';\n                                }\n                              }}\n                            >\n                              <MessageSquare size={16} color=\"#6b7280\" />\n                              <span>{announcement.comment_count || 0}</span>\n                            </button>\n                          )}\n                        </div>\n\n                        <div style={{\n                          color: '#9ca3af',\n                          fontSize: '0.8rem'\n                        }}>\n                          {announcement.view_count || 0} views\n                        </div>\n                      </div>\n\n                      {/* Comments Section */}\n                      {showComments === announcement.announcement_id && announcement.allow_comments && (\n                        <div style={{\n                          marginTop: '1rem',\n                          paddingTop: '1rem',\n                          borderTop: '1px solid rgba(0, 0, 0, 0.1)'\n                        }}>\n                          {/* Comment Input */}\n                          <div style={{\n                            display: 'flex',\n                            gap: '0.75rem',\n                            marginBottom: '1rem'\n                          }}>\n                            <div style={{\n                              width: '40px',\n                              height: '40px',\n                              borderRadius: '50%',\n                              background: 'linear-gradient(135deg, #22c55e 0%, #16a34a 100%)',\n                              display: 'flex',\n                              alignItems: 'center',\n                              justifyContent: 'center',\n                              color: 'white',\n                              fontWeight: '600',\n                              fontSize: '0.875rem'\n                            }}>\n                              S\n                            </div>\n                            <div style={{ flex: 1 }}>\n                              <textarea\n                                value={newComment[announcement.announcement_id] || ''}\n                                onChange={(e) => setNewComment(prev => ({\n                                  ...prev,\n                                  [announcement.announcement_id]: e.target.value\n                                }))}\n                                placeholder=\"Write a comment...\"\n                                style={{\n                                  width: '100%',\n                                  minHeight: '80px',\n                                  padding: '0.75rem',\n                                  border: '1px solid #e5e7eb',\n                                  borderRadius: '12px',\n                                  fontSize: '0.9rem',\n                                  outline: 'none',\n                                  resize: 'vertical',\n                                  fontFamily: 'inherit'\n                                }}\n                              />\n                              <div style={{\n                                display: 'flex',\n                                justifyContent: 'flex-end',\n                                marginTop: '0.5rem'\n                              }}>\n                                <button\n                                  onClick={() => handleCommentSubmit(\n                                    announcement.announcement_id,\n                                    newComment[announcement.announcement_id] || ''\n                                  )}\n                                  disabled={!newComment[announcement.announcement_id]?.trim() || submittingComment === announcement.announcement_id}\n                                  style={{\n                                    background: newComment[announcement.announcement_id]?.trim()\n                                      ? 'linear-gradient(135deg, #3b82f6 0%, #1e40af 100%)'\n                                      : '#e5e7eb',\n                                    color: newComment[announcement.announcement_id]?.trim() ? 'white' : '#9ca3af',\n                                    border: 'none',\n                                    borderRadius: '8px',\n                                    padding: '0.5rem 1rem',\n                                    fontSize: '0.875rem',\n                                    fontWeight: '600',\n                                    cursor: newComment[announcement.announcement_id]?.trim() ? 'pointer' : 'not-allowed',\n                                    transition: 'all 0.2s ease'\n                                  }}\n                                >\n                                  {submittingComment === announcement.announcement_id ? 'Posting...' : 'Post Comment'}\n                                </button>\n                              </div>\n                            </div>\n                          </div>\n\n                          {/* Existing Comments */}\n                          <CommentSection\n                            announcementId={announcement.announcement_id}\n                            allowComments={announcement.allow_comments}\n                            currentUserType=\"student\"\n                          />\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </>\n              )}\n            </div>\n          )}\n          </div>\n        </div>\n      </div>\n\n      {/* Pinned Post Dialog */}\n      {selectedPinnedPost && (\n        <div style={{\n          position: 'fixed',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          backgroundColor: 'rgba(0, 0, 0, 0.5)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 1000,\n          padding: '2rem'\n        }}\n        onClick={() => setSelectedPinnedPost(null)}\n        >\n          <div style={{\n            backgroundColor: 'white',\n            borderRadius: '16px',\n            maxWidth: '600px',\n            width: '100%',\n            maxHeight: '80vh',\n            overflow: 'auto',\n            boxShadow: '0 20px 60px rgba(0, 0, 0, 0.3)'\n          }}\n          onClick={(e) => e.stopPropagation()}\n          >\n            {/* Dialog Header */}\n            <div style={{\n              padding: '1.5rem',\n              borderBottom: '1px solid #e5e7eb',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between'\n            }}>\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem'\n              }}>\n                <Pin size={20} style={{ color: '#22c55e' }} />\n                <h3 style={{\n                  margin: 0,\n                  fontSize: '1.25rem',\n                  fontWeight: '600',\n                  color: '#111827'\n                }}>\n                  Pinned Post\n                </h3>\n              </div>\n              <button\n                onClick={() => setSelectedPinnedPost(null)}\n                style={{\n                  background: 'none',\n                  border: 'none',\n                  fontSize: '1.5rem',\n                  color: '#6b7280',\n                  cursor: 'pointer',\n                  padding: '0.25rem',\n                  borderRadius: '4px',\n                  transition: 'color 0.2s ease'\n                }}\n                onMouseEnter={(e) => {\n                  e.currentTarget.style.color = '#374151';\n                }}\n                onMouseLeave={(e) => {\n                  e.currentTarget.style.color = '#6b7280';\n                }}\n              >\n                ×\n              </button>\n            </div>\n\n            {/* Dialog Content */}\n            <div style={{ padding: '1.5rem' }}>\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '0.75rem',\n                marginBottom: '1rem'\n              }}>\n                {(() => {\n                  if (selectedPinnedPost.is_alert) {\n                    return (\n                      <span style={{\n                        background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',\n                        color: 'white',\n                        fontSize: '0.75rem',\n                        fontWeight: '600',\n                        padding: '0.25rem 0.75rem',\n                        borderRadius: '20px',\n                        textTransform: 'uppercase',\n                        letterSpacing: '0.5px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      }}>\n                        <AlertTriangle size={12} color=\"white\" />\n                        Alert\n                      </span>\n                    );\n                  } else {\n                    const categoryName = (selectedPinnedPost.category_name || 'GENERAL').toUpperCase();\n                    const categoryStyle = getCategoryStyle(categoryName);\n                    const IconComponent = categoryStyle.icon;\n\n                    return (\n                      <span style={{\n                        background: categoryStyle.background,\n                        color: 'white',\n                        fontSize: '0.75rem',\n                        fontWeight: '600',\n                        padding: '0.25rem 0.75rem',\n                        borderRadius: '20px',\n                        textTransform: 'uppercase',\n                        letterSpacing: '0.5px',\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: '0.25rem'\n                      }}>\n                        <IconComponent size={12} color=\"white\" />\n                        {categoryName}\n                      </span>\n                    );\n                  }\n                })()}\n\n                <span style={{\n                  background: 'linear-gradient(135deg, #facc15 0%, #eab308 100%)',\n                  color: 'white',\n                  padding: '0.25rem 0.75rem',\n                  borderRadius: '12px',\n                  fontSize: '0.75rem',\n                  fontWeight: '600',\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.25rem'\n                }}>\n                  <Pin size={12} />\n                  PINNED\n                </span>\n              </div>\n\n              <h2 style={{\n                margin: '0 0 1rem 0',\n                fontSize: '1.5rem',\n                fontWeight: '700',\n                color: '#111827',\n                lineHeight: '1.3'\n              }}>\n                {selectedPinnedPost.title}\n              </h2>\n\n              <div style={{\n                color: '#4b5563',\n                fontSize: '1rem',\n                lineHeight: '1.6',\n                marginBottom: '1.5rem'\n              }}>\n                {selectedPinnedPost.content}\n              </div>\n\n              {/* Images */}\n              {selectedPinnedPost.attachments && selectedPinnedPost.attachments.length > 0 && (\n                <div style={{ marginBottom: '1.5rem' }}>\n                  <FacebookImageGallery\n                    images={selectedPinnedPost.attachments.map((img: any) => getImageUrl(img.file_path)).filter(Boolean)}\n                    altPrefix={selectedPinnedPost.title}\n                    onImageClick={(index) => {\n                      const imageUrls = selectedPinnedPost.attachments.map((img: any) => getImageUrl(img.file_path)).filter(Boolean);\n                      openLightbox(imageUrls, index);\n                    }}\n                  />\n                </div>\n              )}\n\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                gap: '1rem',\n                fontSize: '0.875rem',\n                color: '#6b7280',\n                paddingTop: '1rem',\n                borderTop: '1px solid #e5e7eb'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  alignItems: 'center',\n                  gap: '0.5rem'\n                }}>\n                  <Calendar size={16} />\n                  <span>Published: {new Date(selectedPinnedPost.created_at).toLocaleDateString()}</span>\n                </div>\n                {selectedPinnedPost.author_name && (\n                  <div>\n                    By: {selectedPinnedPost.author_name}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Image Lightbox */}\n      <ImageLightbox\n        images={lightboxImages}\n        initialIndex={lightboxInitialIndex}\n        isOpen={lightboxOpen}\n        onClose={() => setLightboxOpen(false)}\n        altPrefix=\"Image\"\n      />\n    </div>\n    </>\n  );\n};\n\nexport default StudentNewsfeed;\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAA2BC,aAAa,QAAQ,8BAA8B;AAC9E,SAASC,mBAAmB,QAAyB,gBAAgB;AACrE,OAAOC,cAAc,MAAM,yCAAyC;AACpE,OAAOC,aAAa,MAAM,uCAAuC;AAIjE,SAASC,WAAW,EAAEC,YAAY,QAAQ,wBAAwB;AAClE,SACEC,IAAI,EACJC,MAAM,EACNC,GAAG,EACHC,QAAQ,EACRC,aAAa,EACbC,KAAK,EACLC,MAAM,EACNC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,WAAW,EACXC,aAAa,EACbC,KAAK,EACLC,MAAM,EACNC,SAAS,EACTC,aAAa,EACbC,IAAI,EACJC,MAAM,EACNC,KAAK,QACA,cAAc;;AAErB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,cAAc,GAAIC,SAAwB,IAAK;EAAAC,EAAA;EACnD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpC,QAAQ,CAAgB,IAAI,CAAC;EAC7D,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuC,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACd,IAAI,CAACgC,SAAS,EAAE;MACdG,WAAW,CAAC,IAAI,CAAC;MACjB;IACF;IAEA,MAAMK,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5BH,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI;QACF,MAAME,OAAO,GAAGnC,WAAW,CAAC0B,SAAS,CAAC;QACtC,IAAI,CAACS,OAAO,EAAE;UACZ,MAAM,IAAIC,KAAK,CAAC,oBAAoB,CAAC;QACvC;QAEAC,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAEH,OAAO,CAAC;;QAE/D;QACA,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAACL,OAAO,EAAE;UACpCM,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;YACP,QAAQ,EAAEC,MAAM,CAACC,QAAQ,CAACC;UAC5B,CAAC;UACDC,IAAI,EAAE;QACR,CAAC,CAAC;QAEF,IAAI,CAACP,QAAQ,CAACQ,EAAE,EAAE;UAChB,MAAM,IAAIX,KAAK,CAAC,QAAQG,QAAQ,CAACS,MAAM,KAAKT,QAAQ,CAACU,UAAU,EAAE,CAAC;QACpE;QAEA,MAAMC,IAAI,GAAG,MAAMX,QAAQ,CAACW,IAAI,CAAC,CAAC;QAClC,MAAMC,SAAS,GAAGC,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;QAC3CrB,WAAW,CAACsB,SAAS,CAAC;QAEtBd,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MAEtD,CAAC,CAAC,OAAOgB,GAAG,EAAE;QACZjB,OAAO,CAACL,KAAK,CAAC,uBAAuB,EAAEsB,GAAG,CAAC;QAC3CrB,QAAQ,CAACqB,GAAG,YAAYlB,KAAK,GAAGkB,GAAG,CAACC,OAAO,GAAG,sBAAsB,CAAC;MACvE,CAAC,SAAS;QACRxB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDG,SAAS,CAAC,CAAC;;IAEX;IACA,OAAO,MAAM;MACX,IAAIN,QAAQ,IAAIA,QAAQ,CAAC4B,UAAU,CAAC,OAAO,CAAC,EAAE;QAC5CJ,GAAG,CAACK,eAAe,CAAC7B,QAAQ,CAAC;MAC/B;IACF,CAAC;EACH,CAAC,EAAE,CAACF,SAAS,CAAC,CAAC;EAEf,OAAO;IAAEE,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC;AACrC,CAAC;;AAED;AAAAL,EAAA,CA/DMF,cAAc;AA0EpB,MAAMiC,YAAyC,GAAGA,CAAC;EACjDhC,SAAS;EACTiC,GAAG;EACHC,KAAK;EACLC,SAAS;EACTC,MAAM;EACNC,YAAY;EACZC;AACF,CAAC,KAAK;EAAAC,GAAA;EACJ,MAAM;IAAErC,QAAQ;IAAEE,OAAO;IAAEE;EAAM,CAAC,GAAGP,cAAc,CAACC,SAAS,CAAC;EAE9D,IAAII,OAAO,EAAE;IACX,oBACER,OAAA;MAAKsC,KAAK,EAAE;QACV,GAAGA,KAAK;QACRM,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE;MACT,CAAE;MAACT,SAAS,EAAEA,SAAU;MAAAU,QAAA,eACtBjD,OAAA;QAAKsC,KAAK,EAAE;UAAEY,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCjD,OAAA;UAAKsC,KAAK,EAAE;YAAEa,YAAY,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAAH,QAAA,EAAC;QAAC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACnExD,OAAA;UAAKsC,KAAK,EAAE;YAAEc,QAAQ,EAAE;UAAW,CAAE;UAAAH,QAAA,EAAC;QAAU;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI9C,KAAK,IAAI,CAACJ,QAAQ,EAAE;IACtB,oBACEN,OAAA;MAAKsC,KAAK,EAAE;QACV,GAAGA,KAAK;QACRM,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,eAAe,EAAE,SAAS;QAC1BC,KAAK,EAAE,SAAS;QAChBS,MAAM,EAAE;MACV,CAAE;MAAClB,SAAS,EAAEA,SAAU;MAAAU,QAAA,eACtBjD,OAAA;QAAKsC,KAAK,EAAE;UAAEY,SAAS,EAAE;QAAS,CAAE;QAAAD,QAAA,gBAClCjD,OAAA;UAAKsC,KAAK,EAAE;YAAEa,YAAY,EAAE,QAAQ;YAAEC,QAAQ,EAAE;UAAS,CAAE;UAAAH,QAAA,EAAC;QAAG;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrExD,OAAA;UAAKsC,KAAK,EAAE;YAAEc,QAAQ,EAAE,UAAU;YAAEM,UAAU,EAAE;UAAM,CAAE;UAAAT,QAAA,EAAC;QAAiB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,EAC/E9C,KAAK,iBACJV,OAAA;UAAKsC,KAAK,EAAE;YAAEc,QAAQ,EAAE,SAAS;YAAEO,SAAS,EAAE,SAAS;YAAEX,KAAK,EAAE;UAAU,CAAE;UAAAC,QAAA,EACzEvC;QAAK;UAAA2C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACExD,OAAA;IACE4D,GAAG,EAAEtD,QAAS;IACd+B,GAAG,EAAEA,GAAI;IACTC,KAAK,EAAEA,KAAM;IACbC,SAAS,EAAEA,SAAU;IACrBC,MAAM,EAAGqB,CAAC,IAAK;MACb9C,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjEwB,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAGqB,CAAC,CAAC;IACb,CAAE;IACFpB,YAAY,EAAEA,YAAa;IAC3BC,YAAY,EAAEA;EAAa;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC5B,CAAC;AAEN,CAAC;;AAED;AAAAb,GAAA,CArEMP,YAAyC;EAAA,QASRjC,cAAc;AAAA;AAAA2D,EAAA,GAT/C1B,YAAyC;AA6E/C,MAAM2B,oBAAyD,GAAGA,CAAC;EACjEC,MAAM;EACNC,SAAS;EACTC,UAAU,GAAG,CAAC;EACdC;AACF,CAAC,KAAK;EACJ,IAAI,CAACH,MAAM,IAAIA,MAAM,CAACI,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;EAE/C,MAAMC,aAAa,GAAGL,MAAM,CAACM,KAAK,CAAC,CAAC,EAAEJ,UAAU,CAAC;EACjD,MAAMK,cAAc,GAAGP,MAAM,CAACI,MAAM,GAAGF,UAAU;EAEjD,MAAMM,aAAa,GAAGA,CAACC,KAAa,EAAEC,KAAa,KAA0B;IAC3E,MAAMC,SAA8B,GAAG;MACrCC,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,OAAO;MAClBC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE,uCAAuC;MACnDC,YAAY,EAAER,KAAK,KAAK,CAAC,IAAIC,KAAK,KAAK,CAAC,GAAG,MAAM,GAAG;IACtD,CAAC;IAED,OAAOC,SAAS;EAClB,CAAC;EAED,MAAMO,iBAAiB,GAAGA,CAACT,KAAa,EAAEC,KAAa,KAA0B;IAC/E,MAAMC,SAA8B,GAAG;MACrCQ,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE,QAAQ;MAClBrC,eAAe,EAAE;IACnB,CAAC;IAED,IAAI2B,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QACL,GAAGC,SAAS;QACZC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,OAAO;QACfI,YAAY,EAAE;MAChB,CAAC;IACH;IAEA,IAAIP,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QACL,GAAGC,SAAS;QACZC,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,OAAO;QACfI,YAAY,EAAE;MAChB,CAAC;IACH;IAEA,IAAIP,KAAK,KAAK,CAAC,EAAE;MACf,IAAID,KAAK,KAAK,CAAC,EAAE;QACf,OAAO;UACL,GAAGE,SAAS;UACZC,KAAK,EAAE,KAAK;UACZC,MAAM,EAAE,OAAO;UACfI,YAAY,EAAE;QAChB,CAAC;MACH,CAAC,MAAM;QACL,OAAO;UACL,GAAGN,SAAS;UACZC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,OAAO;UACfI,YAAY,EAAE;QAChB,CAAC;MACH;IACF;;IAEA;IACA,IAAIR,KAAK,KAAK,CAAC,EAAE;MACf,OAAO;QACL,GAAGE,SAAS;QACZC,KAAK,EAAE,KAAK;QACZC,MAAM,EAAE,OAAO;QACfI,YAAY,EAAE;MAChB,CAAC;IACH,CAAC,MAAM;MACL,OAAO;QACL,GAAGN,SAAS;QACZC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdI,YAAY,EAAE;MAChB,CAAC;IACH;EACF,CAAC;EAED,MAAMI,aAAa,GAAGA,CAACZ,KAAa,EAAEa,KAAa,KAAK;IACtD,IAAIb,KAAK,KAAKP,UAAU,GAAG,CAAC,IAAIoB,KAAK,GAAG,CAAC,EAAE;MACzC,oBACEtF,OAAA;QAAKsC,KAAK,EAAE;UACV6C,QAAQ,EAAE,UAAU;UACpBI,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACT3C,eAAe,EAAE,oBAAoB;UACrCH,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBE,KAAK,EAAE,OAAO;UACdI,QAAQ,EAAE,QAAQ;UAClBM,UAAU,EAAE,KAAK;UACjBuB,YAAY,EAAE;QAChB,CAAE;QAAAhC,QAAA,GAAC,GACA,EAACqC,KAAK;MAAA;QAAAjC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAEV;IACA,OAAO,IAAI;EACb,CAAC;EAED,oBACExD,OAAA;IAAKsC,KAAK,EAAE;MACVM,OAAO,EAAE,MAAM;MACf+C,GAAG,EAAE,KAAK;MACVf,KAAK,EAAE,MAAM;MACbzB,YAAY,EAAE;IAChB,CAAE;IAAAF,QAAA,gBAEAjD,OAAA;MAAKsC,KAAK,EAAE4C,iBAAiB,CAAC,CAAC,EAAEb,aAAa,CAACD,MAAM,CAAE;MAAAnB,QAAA,gBACrDjD,OAAA,CAACoC,YAAY;QACXhC,SAAS,EAAEiE,aAAa,CAAC,CAAC,CAAE;QAC5BhC,GAAG,EAAE,GAAG4B,SAAS,YAAa;QAC9B3B,KAAK,EAAEkC,aAAa,CAAC,CAAC,EAAEH,aAAa,CAACD,MAAM,CAAE;QAC9C3B,YAAY,EAAGoB,CAAC,IAAK;UACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,aAAa;QACjD,CAAE;QACFnD,YAAY,EAAGmB,CAAC,IAAK;UACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,UAAU;QAC9C;MAAE;QAAAxC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACDW,YAAY,iBACXnE,OAAA;QACEsC,KAAK,EAAE;UACL6C,QAAQ,EAAE,UAAU;UACpBI,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTX,MAAM,EAAE;QACV,CAAE;QACFe,OAAO,EAAEA,CAAA,KAAM3B,YAAY,CAAC,CAAC;MAAE;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAGLa,aAAa,CAACD,MAAM,GAAG,CAAC,iBACvBpE,OAAA;MAAKsC,KAAK,EAAE;QACVM,OAAO,EAAE,MAAM;QACfmD,aAAa,EAAE,QAAQ;QACvBJ,GAAG,EAAE,KAAK;QACVf,KAAK,EAAEP,aAAa,CAACD,MAAM,KAAK,CAAC,GAAG,KAAK,GAAG;MAC9C,CAAE;MAAAnB,QAAA,EACCoB,aAAa,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC0B,GAAG,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;QAC1C,MAAMC,WAAW,GAAGD,GAAG,GAAG,CAAC;QAC3B,oBACElG,OAAA;UAEEsC,KAAK,EAAE4C,iBAAiB,CAACiB,WAAW,EAAE9B,aAAa,CAACD,MAAM,CAAE;UAAAnB,QAAA,gBAE5DjD,OAAA,CAACoC,YAAY;YACXhC,SAAS,EAAE6F,KAAM;YACjB5D,GAAG,EAAE,GAAG4B,SAAS,YAAYkC,WAAW,GAAG,CAAC,EAAG;YAC/C7D,KAAK,EAAEkC,aAAa,CAAC2B,WAAW,EAAE9B,aAAa,CAACD,MAAM,CAAE;YACxD3B,YAAY,EAAGoB,CAAC,IAAK;cACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,aAAa;YACjD,CAAE;YACFnD,YAAY,EAAGmB,CAAC,IAAK;cACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,UAAU;YAC9C;UAAE;YAAAxC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EACD6B,aAAa,CAACc,WAAW,EAAE5B,cAAc,CAAC,EAC1CJ,YAAY,iBACXnE,OAAA;YACEsC,KAAK,EAAE;cACL6C,QAAQ,EAAE,UAAU;cACpBI,GAAG,EAAE,CAAC;cACNC,IAAI,EAAE,CAAC;cACPC,KAAK,EAAE,CAAC;cACRC,MAAM,EAAE,CAAC;cACTX,MAAM,EAAE;YACV,CAAE;YACFe,OAAO,EAAEA,CAAA,KAAM3B,YAAY,CAACgC,WAAW;UAAE;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CACF;QAAA,GA3BI2C,WAAW;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4Bb,CAAC;MAEV,CAAC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC4C,GAAA,GAhMIrC,oBAAyD;AAkM/D,MAAMsC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACtC,MAAMC,QAAQ,GAAGlI,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMmI,gBAAgB,GAAIC,YAAoB,IAAK;IACjD,MAAMC,MAAM,GAAG;MACb,UAAU,EAAE;QACVC,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAExH;MACR,CAAC;MACD,SAAS,EAAE;QACTuH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEvH;MACR,CAAC;MACD,QAAQ,EAAE;QACRsH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEtH;MACR,CAAC;MACD,WAAW,EAAE;QACXqH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAErH;MACR,CAAC;MACD,QAAQ,EAAE;QACRoH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEnH;MACR,CAAC;MACD,WAAW,EAAE;QACXkH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEpH;MACR;IACF,CAAC;IAED,OAAOkH,MAAM,CAACD,YAAY,CAAwB,IAAIC,MAAM,CAAC,SAAS,CAAC;EACzE,CAAC;;EAED;EACA,MAAMG,mBAAmB,GAAIC,eAAuB,IAAK;IACvD,MAAMJ,MAAM,GAAG;MACb,kBAAkB,EAAE;QAClBC,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEhH;MACR,CAAC;MACD,cAAc,EAAE;QACd+G,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEjH;MACR,CAAC;MACD,gBAAgB,EAAE;QAChBgH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE/G;MACR,CAAC;MACD,cAAc,EAAE;QACd8G,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAEnH;MACR,CAAC;MACD,YAAY,EAAE;QACZkH,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAE9G;MACR,CAAC;MACD,SAAS,EAAE;QACT6G,UAAU,EAAE,mDAAmD;QAC/DC,IAAI,EAAElH;MACR;IACF,CAAC;IAED,OAAOgH,MAAM,CAACI,eAAe,CAAwB,IAAIJ,MAAM,CAAC,cAAc,CAAC;EACjF,CAAC;;EAED;EACA,MAAM,CAACK,cAAc,EAAEC,iBAAiB,CAAC,GAAG7I,QAAQ,CAAS,EAAE,CAAC;EAChE,MAAM,CAAC8I,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/I,QAAQ,CAAS,EAAE,CAAC;EACpE,MAAM,CAACgJ,UAAU,EAAEC,aAAa,CAAC,GAAGjJ,QAAQ,CAAC,EAAE,CAAC;;EAEhD;EACA,MAAM,CAACkJ,YAAY,EAAEC,eAAe,CAAC,GAAGnJ,QAAQ,CAAgB,IAAI,CAAC;EACrE,MAAM,CAACoJ,UAAU,EAAEC,aAAa,CAAC,GAAGrJ,QAAQ,CAA4B,CAAC,CAAC,CAAC;EAC3E,MAAM,CAACsJ,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGvJ,QAAQ,CAAgB,IAAI,CAAC;EAC/E,MAAM,CAACwJ,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGzJ,QAAQ,CAAa,IAAI,CAAC;;EAE9E;EACA,MAAM,CAAC0J,YAAY,EAAEC,eAAe,CAAC,GAAG3J,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4J,cAAc,EAAEC,iBAAiB,CAAC,GAAG7J,QAAQ,CAAW,EAAE,CAAC;EAClE,MAAM,CAAC8J,oBAAoB,EAAEC,uBAAuB,CAAC,GAAG/J,QAAQ,CAAC,CAAC,CAAC;;EAEnE;EACA,MAAM,CAACgK,cAAc,EAAEC,iBAAiB,CAAC,GAAGjK,QAAQ,CAAkB,EAAE,CAAC;EACzE,MAAM,CAACkK,eAAe,EAAEC,kBAAkB,CAAC,GAAGnK,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACoK,aAAa,EAAEC,gBAAgB,CAAC,GAAGrK,QAAQ,CAAqB,CAAC;EACxE,MAAM,CAACsK,aAAa,EAAEC,gBAAgB,CAAC,GAAGvK,QAAQ,CAAQ,EAAE,CAAC;EAC7D,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuC,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAAqB,CAAC;EACxD,MAAM,CAACwK,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzK,QAAQ,CAAQ,EAAE,CAAC;EAEzE,MAAM;IAAE0K;EAAW,CAAC,GAAGvK,aAAa,CAAC,CAAC;;EAEtC;EACA,MAAMwK,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAE5E,MAAM,EAAE;EAAE,CAAC,EAAE,CAAC6E,CAAC,EAAEC,CAAC,KAAKA,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;;EAEjE;EACA,MAAMC,YAAY,GAAGA,CAACC,SAAmB,EAAEC,YAAoB,KAAK;IAClErB,iBAAiB,CAACoB,SAAS,CAAC;IAC5BlB,uBAAuB,CAACmB,YAAY,CAAC;IACrCvB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMwB,2BAA2B,GAAG,MAAAA,CAAA,KAAY;IAC9C,IAAI;MACF7I,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC4I,SAAS,CAAC;MAEnB,MAAMtI,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGvC,YAAY,wFAAwF,CAAC;MACrI,MAAM6K,IAAI,GAAG,MAAMvI,QAAQ,CAACwI,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC7B,MAAMG,iBAAiB,GAAGH,IAAI,CAACA,IAAI,CAACf,aAAa,IAAI,EAAE;;QAEvD;QACA;QACAC,gBAAgB,CAACiB,iBAAiB,CAAC;;QAEnC;QACA,MAAMC,MAAM,GAAGD,iBAAiB,CAACE,MAAM,CAAEC,GAAQ,IAAKA,GAAG,CAACC,SAAS,KAAK,CAAC,CAAC;QAC1EnB,sBAAsB,CAACgB,MAAM,CAAC;MAChC,CAAC,MAAM;QACLjJ,QAAQ,CAAC,8BAA8B,CAAC;MAC1C;IACF,CAAC,CAAC,OAAOqB,GAAQ,EAAE;MACjBjB,OAAO,CAACL,KAAK,CAAC,+BAA+B,EAAEsB,GAAG,CAAC;MACnDrB,QAAQ,CAACqB,GAAG,CAACC,OAAO,IAAI,8BAA8B,CAAC;IACzD,CAAC,SAAS;MACRxB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMuJ,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI;MACF1B,kBAAkB,CAAC,IAAI,CAAC;MACxBE,gBAAgB,CAACe,SAAS,CAAC;MAE3B,MAAMtI,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGvC,YAAY,0DAA0D,CAAC;MACvG,MAAM6K,IAAI,GAAG,MAAMvI,QAAQ,CAACwI,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACA,IAAI,EAAE;QAC7B,MAAMS,UAAU,GAAGT,IAAI,CAACA,IAAI,CAACU,MAAM,IAAIV,IAAI,CAACA,IAAI,IAAI,EAAE;;QAEtD;QACA,MAAMW,gBAAgB,GAAG,MAAMC,OAAO,CAACC,GAAG,CACxCJ,UAAU,CAACjE,GAAG,CAAC,MAAOsE,KAAU,IAAK;UACnC,IAAI;YACF,MAAMC,aAAa,GAAG,MAAMrJ,KAAK,CAAC,GAAGvC,YAAY,iBAAiB2L,KAAK,CAACE,WAAW,SAAS,CAAC;YAC7F,MAAMC,SAAS,GAAG,MAAMF,aAAa,CAACd,IAAI,CAAC,CAAC;YAE5C,IAAIgB,SAAS,CAACf,OAAO,IAAIe,SAAS,CAACjB,IAAI,EAAE;cACvCc,KAAK,CAACtG,MAAM,GAAGyG,SAAS,CAACjB,IAAI,CAACkB,WAAW,IAAI,EAAE;YACjD,CAAC,MAAM;cACLJ,KAAK,CAACtG,MAAM,GAAG,EAAE;YACnB;UACF,CAAC,CAAC,OAAO2G,MAAM,EAAE;YACf5J,OAAO,CAAC6J,IAAI,CAAC,oCAAoCN,KAAK,CAACE,WAAW,GAAG,EAAEG,MAAM,CAAC;YAC9EL,KAAK,CAACtG,MAAM,GAAG,EAAE;UACnB;UACA,OAAOsG,KAAK;QACd,CAAC,CACH,CAAC;QAEDlC,iBAAiB,CAAC+B,gBAAgB,CAAC;MACrC,CAAC,MAAM;QACL3B,gBAAgB,CAAC,gCAAgC,CAAC;MACpD;IACF,CAAC,CAAC,OAAOxG,GAAQ,EAAE;MACjBjB,OAAO,CAACL,KAAK,CAAC,iCAAiC,EAAEsB,GAAG,CAAC;MACrDwG,gBAAgB,CAACxG,GAAG,CAACC,OAAO,IAAI,gCAAgC,CAAC;IACnE,CAAC,SAAS;MACRqG,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACAlK,SAAS,CAAC,MAAM;IACdkL,2BAA2B,CAAC,CAAC;IAC7BU,mBAAmB,CAAC,CAAC;EACvB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMa,gBAAgB,GAAG,MAAOC,YAAiB,IAAK;IACpD,IAAI;MACF,IAAIA,YAAY,CAACC,aAAa,EAAE;QAC9B,MAAMxM,mBAAmB,CAACyM,cAAc,CAACF,YAAY,CAACG,eAAe,CAAC;MACxE,CAAC,MAAM;QACL,MAAMhK,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGvC,YAAY,sBAAsBmM,YAAY,CAACG,eAAe,YAAY,EAAE;UAC1G9J,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACD8J,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBC,gBAAgB,EAAE,CAAC;YACnBC,YAAY,EAAE;UAChB,CAAC;QACH,CAAC,CAAC;QAEF,IAAI,CAACrK,QAAQ,CAACQ,EAAE,EAAE;UAChB,MAAM,IAAIX,KAAK,CAAC,wBAAwB,CAAC;QAC3C;MACF;;MAEA;MACA,MAAMwI,2BAA2B,CAAC,CAAC;IACrC,CAAC,CAAC,OAAO5I,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC9C;EACF,CAAC;;EAED;EACA,MAAM6K,mBAAmB,GAAG,MAAAA,CAAOC,cAAsB,EAAEC,WAAmB,KAAK;IACjF,IAAI,CAACA,WAAW,CAACC,IAAI,CAAC,CAAC,EAAE;IAEzB,IAAI;MACFhE,oBAAoB,CAAC8D,cAAc,CAAC;MAEpC,MAAMvK,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGvC,YAAY,sBAAsB6M,cAAc,WAAW,EAAE;QAC3FrK,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACD8J,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBO,OAAO,EAAEF,WAAW;UACpBG,cAAc,EAAE,cAAc;UAC9BC,eAAe,EAAE,qBAAqB;UACtCP,YAAY,EAAE;QAChB,CAAC;MACH,CAAC,CAAC;MAEF,IAAIrK,QAAQ,CAACQ,EAAE,EAAE;QACf+F,aAAa,CAACsE,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACN,cAAc,GAAG;QAAG,CAAC,CAAC,CAAC;QAC1D,MAAMlC,2BAA2B,CAAC,CAAC;MACrC,CAAC,MAAM;QACLvI,OAAO,CAACL,KAAK,CAAC,0BAA0B,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD,CAAC,SAAS;MACRgH,oBAAoB,CAAC,IAAI,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMqE,qBAAqB,GAAGtD,aAAa,CAACoB,MAAM,CAACiB,YAAY,IAAI;IACjE,MAAMkB,aAAa,GAAG,CAAC7E,UAAU,IAC/B2D,YAAY,CAACmB,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChF,UAAU,CAAC+E,WAAW,CAAC,CAAC,CAAC,IACnEpB,YAAY,CAACa,OAAO,CAACO,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChF,UAAU,CAAC+E,WAAW,CAAC,CAAC,CAAC;IAEvE,MAAME,eAAe,GAAG,CAACrF,cAAc,IAAI+D,YAAY,CAACuB,WAAW,CAACC,QAAQ,CAAC,CAAC,KAAKvF,cAAc;;IAEjG;IACA;;IAEA,OAAOiF,aAAa,IAAII,eAAe;EACzC,CAAC,CAAC;;EAEF;EACA,MAAMG,sBAAsB,GAAGpE,cAAc,CAAC0B,MAAM,CAACS,KAAK,IAAI;IAC5D,MAAM0B,aAAa,GAAG,CAAC7E,UAAU,IAC/BmD,KAAK,CAAC2B,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChF,UAAU,CAAC+E,WAAW,CAAC,CAAC,CAAC,IAC3D5B,KAAK,CAACkC,WAAW,IAAIlC,KAAK,CAACkC,WAAW,CAACN,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChF,UAAU,CAAC+E,WAAW,CAAC,CAAC,CAAE;;IAE3F;IACA,MAAMO,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;IACxBD,KAAK,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAE5B,MAAMC,SAAS,GAAG,IAAIF,IAAI,CAACpC,KAAK,CAACuC,UAAU,CAAC;IAC5CD,SAAS,CAACD,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;IAEhC,MAAMG,gBAAgB,GAAGF,SAAS,IAAIH,KAAK;IAC3C,MAAMM,WAAW,GAAIzC,KAAK,CAAS0C,YAAY,KAAK,CAAC;IAErD,OAAOhB,aAAa,IAAIc,gBAAgB,IAAIC,WAAW;EACzD,CAAC,CAAC;;EAEF;EACA,MAAME,oBAAoB,GAAGlB,qBAAqB;EAClD,MAAMmB,aAAa,GAAGX,sBAAsB;;EAE5C;EACA,MAAMY,eAAe,GAAG,CACtB,GAAGF,oBAAoB,CAACjH,GAAG,CAACoH,IAAI,KAAK;IAAE,GAAGA,IAAI;IAAEC,IAAI,EAAE,cAAc;IAAEC,QAAQ,EAAE,IAAIZ,IAAI,CAACU,IAAI,CAACG,UAAU;EAAE,CAAC,CAAC,CAAC,EAC7G,GAAGL,aAAa,CAAClH,GAAG,CAACoH,IAAI,KAAK;IAAE,GAAGA,IAAI;IAAEC,IAAI,EAAE,OAAO;IAAEC,QAAQ,EAAE,IAAIZ,IAAI,CAACU,IAAI,CAACP,UAAU;EAAE,CAAC,CAAC,CAAC,CAChG,CAACW,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAACJ,QAAQ,CAACK,OAAO,CAAC,CAAC,GAAGF,CAAC,CAACH,QAAQ,CAACK,OAAO,CAAC,CAAC,CAAC;EAE7D,oBACE3N,OAAA,CAAAE,SAAA;IAAA+C,QAAA,gBAEEjD,OAAA;MAAAiD,QAAA,EACG;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAS;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAERxD,OAAA;MAAKsC,KAAK,EAAE;QACVM,OAAO,EAAE,MAAM;QACfgL,SAAS,EAAE,OAAO;QAClBjH,UAAU,EAAE,mDAAmD;QAC/DkH,cAAc,EAAE;MAClB,CAAE;MAAA5K,QAAA,gBAGFjD,OAAA;QACEuC,SAAS,EAAC,aAAa;QACvBD,KAAK,EAAE;UACLwL,IAAI,EAAE,CAAC;UACPlL,OAAO,EAAE,MAAM;UACfmD,aAAa,EAAE,QAAQ;UACvB6H,SAAS,EAAE,OAAO;UAClBhJ,KAAK,EAAE;QACT,CAAE;QAAA3B,QAAA,gBAEFjD,OAAA;UAAQsC,KAAK,EAAE;YACbqE,UAAU,EAAE,OAAO;YACnBoH,YAAY,EAAE,mBAAmB;YACjC5I,QAAQ,EAAE,QAAQ;YAClBI,GAAG,EAAE,CAAC;YACNyI,MAAM,EAAE,GAAG;YACXC,SAAS,EAAE;UACb,CAAE;UAAAhL,QAAA,eACAjD,OAAA;YAAKsC,KAAK,EAAE;cACVsC,KAAK,EAAE,MAAM;cACbsJ,OAAO,EAAE,QAAQ;cACjBrJ,MAAM,EAAE,MAAM;cACdjC,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,gBAEAjD,OAAA;cAAKsC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB8C,GAAG,EAAE,QAAQ;gBACbwI,QAAQ,EAAE;cACZ,CAAE;cAAAlL,QAAA,gBACAjD,OAAA;gBACE4D,GAAG,EAAC,iBAAiB;gBACrBvB,GAAG,EAAC,WAAW;gBACfC,KAAK,EAAE;kBACLsC,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,MAAM;kBACdC,SAAS,EAAE;gBACb;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFxD,OAAA;gBAAAiD,QAAA,gBACEjD,OAAA;kBAAIsC,KAAK,EAAE;oBACT8L,MAAM,EAAE,CAAC;oBACThL,QAAQ,EAAE,QAAQ;oBAClBM,UAAU,EAAE,KAAK;oBACjBV,KAAK,EAAE,SAAS;oBAChBqL,UAAU,EAAE;kBACd,CAAE;kBAAApL,QAAA,EAAC;gBAEH;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACLxD,OAAA;kBAAGsC,KAAK,EAAE;oBACR8L,MAAM,EAAE,CAAC;oBACThL,QAAQ,EAAE,UAAU;oBACpBJ,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,EAAC;gBAEH;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNxD,OAAA;cAAKsC,KAAK,EAAE;gBACVwL,IAAI,EAAE,CAAC;gBACPQ,QAAQ,EAAE,OAAO;gBACjBF,MAAM,EAAE;cACV,CAAE;cAAAnL,QAAA,eACAjD,OAAA;gBAAKsC,KAAK,EAAE;kBAAE6C,QAAQ,EAAE;gBAAW,CAAE;gBAAAlC,QAAA,gBACnCjD,OAAA,CAACnB,MAAM;kBACL0P,IAAI,EAAE,EAAG;kBACTjM,KAAK,EAAE;oBACL6C,QAAQ,EAAE,UAAU;oBACpBK,IAAI,EAAE,MAAM;oBACZD,GAAG,EAAE,KAAK;oBACVM,SAAS,EAAE,kBAAkB;oBAC7B7C,KAAK,EAAE;kBACT;gBAAE;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACFxD,OAAA;kBACEqN,IAAI,EAAC,MAAM;kBACXmB,WAAW,EAAC,aAAa;kBACzBC,KAAK,EAAEtH,UAAW;kBAClBuH,QAAQ,EAAG7K,CAAC,IAAKuD,aAAa,CAACvD,CAAC,CAAC8K,MAAM,CAACF,KAAK,CAAE;kBAC/CnM,KAAK,EAAE;oBACLsC,KAAK,EAAE,MAAM;oBACbC,MAAM,EAAE,MAAM;oBACdqJ,OAAO,EAAE,eAAe;oBACxBzK,MAAM,EAAE,mBAAmB;oBAC3BwB,YAAY,EAAE,MAAM;oBACpB0B,UAAU,EAAE,SAAS;oBACrB3D,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,UAAU;oBACpBwL,OAAO,EAAE,MAAM;oBACf5J,UAAU,EAAE;kBACd,CAAE;kBACF6J,OAAO,EAAGhL,CAAC,IAAK;oBACdA,CAAC,CAAC8K,MAAM,CAACrM,KAAK,CAACwM,WAAW,GAAG,SAAS;oBACtCjL,CAAC,CAAC8K,MAAM,CAACrM,KAAK,CAACqE,UAAU,GAAG,OAAO;oBACnC9C,CAAC,CAAC8K,MAAM,CAACrM,KAAK,CAAC2L,SAAS,GAAG,mCAAmC;kBAChE,CAAE;kBACFc,MAAM,EAAGlL,CAAC,IAAK;oBACbA,CAAC,CAAC8K,MAAM,CAACrM,KAAK,CAACwM,WAAW,GAAG,SAAS;oBACtCjL,CAAC,CAAC8K,MAAM,CAACrM,KAAK,CAACqE,UAAU,GAAG,SAAS;oBACrC9C,CAAC,CAAC8K,MAAM,CAACrM,KAAK,CAAC2L,SAAS,GAAG,MAAM;kBACnC;gBAAE;kBAAA5K,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNxD,OAAA;cAAKsC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB8C,GAAG,EAAE,MAAM;gBACXwI,QAAQ,EAAE,OAAO;gBACjBrL,cAAc,EAAE;cAClB,CAAE;cAAAG,QAAA,gBAEAjD,OAAA;gBAAKsC,KAAK,EAAE;kBACVM,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpB8C,GAAG,EAAE,QAAQ;kBACbuI,OAAO,EAAE,QAAQ;kBACjBvH,UAAU,EAAE,SAAS;kBACrB1B,YAAY,EAAE,MAAM;kBACpBxB,MAAM,EAAE;gBACV,CAAE;gBAAAR,QAAA,gBACAjD,OAAA;kBACEyO,KAAK,EAAE1H,cAAe;kBACtB2H,QAAQ,EAAG7K,CAAC,IAAKmD,iBAAiB,CAACnD,CAAC,CAAC8K,MAAM,CAACF,KAAK,CAAE;kBACnDnM,KAAK,EAAE;oBACL4L,OAAO,EAAE,gBAAgB;oBACzBzK,MAAM,EAAE,MAAM;oBACdwB,YAAY,EAAE,KAAK;oBACnB0B,UAAU,EAAE,OAAO;oBACnB3D,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,UAAU;oBACpBwL,OAAO,EAAE,MAAM;oBACf7J,MAAM,EAAE,SAAS;oBACjBoJ,QAAQ,EAAE;kBACZ,CAAE;kBAAAlL,QAAA,gBAEFjD,OAAA;oBAAQyO,KAAK,EAAC,EAAE;oBAAAxL,QAAA,EAAC;kBAAc;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACvCqF,UAAU,CAAC7C,GAAG,CAACgJ,QAAQ,iBACtBhP,OAAA;oBAAmCyO,KAAK,EAAEO,QAAQ,CAAC3C,WAAW,CAACC,QAAQ,CAAC,CAAE;oBAAArJ,QAAA,EACvE+L,QAAQ,CAACC;kBAAI,GADHD,QAAQ,CAAC3C,WAAW;oBAAAhJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEzB,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,eAETxD,OAAA;kBACEyO,KAAK,EAAExH,gBAAiB;kBACxByH,QAAQ,EAAG7K,CAAC,IAAKqD,mBAAmB,CAACrD,CAAC,CAAC8K,MAAM,CAACF,KAAK,CAAE;kBACrDnM,KAAK,EAAE;oBACL4L,OAAO,EAAE,gBAAgB;oBACzBzK,MAAM,EAAE,MAAM;oBACdwB,YAAY,EAAE,KAAK;oBACnB0B,UAAU,EAAE,OAAO;oBACnB3D,KAAK,EAAE,SAAS;oBAChBI,QAAQ,EAAE,UAAU;oBACpBwL,OAAO,EAAE,MAAM;oBACf7J,MAAM,EAAE,SAAS;oBACjBoJ,QAAQ,EAAE;kBACZ,CAAE;kBAAAlL,QAAA,gBAEFjD,OAAA;oBAAQyO,KAAK,EAAC,EAAE;oBAAAxL,QAAA,EAAC;kBAAU;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,EACnCsF,WAAW,CAAC9C,GAAG,CAACkJ,KAAK,iBACpBlP,OAAA;oBAAoByO,KAAK,EAAES,KAAK,CAAC5C,QAAQ,CAAC,CAAE;oBAAArJ,QAAA,GAAC,QACrC,EAACiM,KAAK;kBAAA,GADDA,KAAK;oBAAA7L,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAEV,CACT,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CAAC,EAER,CAAC2D,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,kBAChDjH,OAAA;kBACE8F,OAAO,EAAEA,CAAA,KAAM;oBACbsB,aAAa,CAAC,EAAE,CAAC;oBACjBJ,iBAAiB,CAAC,EAAE,CAAC;oBACrBE,mBAAmB,CAAC,EAAE,CAAC;kBACzB,CAAE;kBACF5E,KAAK,EAAE;oBACL4L,OAAO,EAAE,gBAAgB;oBACzBzK,MAAM,EAAE,MAAM;oBACdwB,YAAY,EAAE,KAAK;oBACnB0B,UAAU,EAAE,SAAS;oBACrB3D,KAAK,EAAE,OAAO;oBACdI,QAAQ,EAAE,UAAU;oBACpBM,UAAU,EAAE,KAAK;oBACjBqB,MAAM,EAAE,SAAS;oBACjBC,UAAU,EAAE;kBACd,CAAE;kBACFvC,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACqE,UAAU,GAAG,SAAS;kBAC9C,CAAE;kBACFjE,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACqE,UAAU,GAAG,SAAS;kBAC9C,CAAE;kBAAA1D,QAAA,EACH;gBAED;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,eAKNxD,OAAA;gBACE8F,OAAO,EAAEA,CAAA,KAAMS,QAAQ,CAAC,oBAAoB,CAAE;gBAC9CjE,KAAK,EAAE;kBACLM,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpB8C,GAAG,EAAE,QAAQ;kBACbuI,OAAO,EAAE,aAAa;kBACtBzK,MAAM,EAAE,mBAAmB;kBAC3BwB,YAAY,EAAE,KAAK;kBACnB0B,UAAU,EAAE,OAAO;kBACnB3D,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,UAAU;kBACpBM,UAAU,EAAE,KAAK;kBACjBqB,MAAM,EAAE,SAAS;kBACjBC,UAAU,EAAE;gBACd,CAAE;gBACFvC,YAAY,EAAGoB,CAAC,IAAK;kBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwM,WAAW,GAAG,SAAS;kBAC7CjL,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;gBACzC,CAAE;gBACFN,YAAY,EAAGmB,CAAC,IAAK;kBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwM,WAAW,GAAG,SAAS;kBAC7CjL,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;gBACzC,CAAE;gBAAAC,QAAA,gBAEFjD,OAAA,CAACpB,IAAI;kBAAC2P,IAAI,EAAE;gBAAG;kBAAAlL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,aAEpB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAGTxD,OAAA;UAAKsC,KAAK,EAAE;YACVwL,IAAI,EAAE,CAAC;YACPlL,OAAO,EAAE,MAAM;YACf+C,GAAG,EAAE,QAAQ;YACbuI,OAAO,EAAE,aAAa;YACtBvH,UAAU,EAAE,aAAa;YACzB9D,UAAU,EAAE;UACd,CAAE;UAAAI,QAAA,gBAEAjD,OAAA;YAAKsC,KAAK,EAAE;cACVsC,KAAK,EAAE,OAAO;cACduK,UAAU,EAAE;YACd,CAAE;YAAAlM,QAAA,eACAjD,OAAA;cAAKsC,KAAK,EAAE;gBACVqE,UAAU,EAAE,OAAO;gBACnB1B,YAAY,EAAE,MAAM;gBACpBxB,MAAM,EAAE,mBAAmB;gBAC3B2B,QAAQ,EAAE,QAAQ;gBAClBD,QAAQ,EAAE,QAAQ;gBAClBI,GAAG,EAAE,MAAM;gBACX0I,SAAS,EAAE;cACb,CAAE;cAAAhL,QAAA,gBAEAjD,OAAA;gBAAKsC,KAAK,EAAE;kBACV4L,OAAO,EAAE,sBAAsB;kBAC/BH,YAAY,EAAE,mBAAmB;kBACjCpH,UAAU,EAAE;gBACd,CAAE;gBAAA1D,QAAA,gBACAjD,OAAA;kBAAKsC,KAAK,EAAE;oBACVM,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpB8C,GAAG,EAAE,SAAS;oBACdxC,YAAY,EAAE;kBAChB,CAAE;kBAAAF,QAAA,gBACAjD,OAAA,CAAClB,GAAG;oBAACyP,IAAI,EAAE,EAAG;oBAACjM,KAAK,EAAE;sBAAEU,KAAK,EAAE;oBAAQ;kBAAE;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5CxD,OAAA;oBAAIsC,KAAK,EAAE;sBACT8L,MAAM,EAAE,CAAC;sBACThL,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE,KAAK;sBACjBV,KAAK,EAAE;oBACT,CAAE;oBAAAC,QAAA,EAAC;kBAEH;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eACNxD,OAAA;kBAAGsC,KAAK,EAAE;oBACR8L,MAAM,EAAE,CAAC;oBACThL,QAAQ,EAAE,UAAU;oBACpBJ,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,EAAC;gBAEH;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAGNxD,OAAA;gBAAKsC,KAAK,EAAE;kBAAE4L,OAAO,EAAE;gBAAU,CAAE;gBAAAjL,QAAA,EAChC0F,mBAAmB,CAACvE,MAAM,GAAG,CAAC,gBAC7BpE,OAAA,CAAAE,SAAA;kBAAA+C,QAAA,GACG0F,mBAAmB,CAACrE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC0B,GAAG,CAAC,CAAC8E,YAAY,EAAErG,KAAK,KAAK;oBAC5D;oBACA,IAAI2K,MAAM;oBACV,IAAItE,YAAY,CAACuE,QAAQ,EAAE;sBACzBD,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;oBAC9C,CAAC,MAAM;sBACL,MAAM3I,YAAY,GAAG,CAACqE,YAAY,CAACwE,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;;sBAE5E;sBACA,MAAMC,cAAc,GAAG;wBACrB,UAAU,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBAC7C,QAAQ,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBAC3C,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBAC9C,QAAQ,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBAC3C,WAAW,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC;wBAC9C,SAAS,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS;sBAC7C,CAAC;sBAEDJ,MAAM,GAAGI,cAAc,CAAC/I,YAAY,CAAgC,IAAI+I,cAAc,CAAC,SAAS,CAAC;oBACnG;oBAEA,oBACExP,OAAA;sBAEEsC,KAAK,EAAE;wBACL4L,OAAO,EAAE,SAAS;wBAClBvH,UAAU,EAAE,2BAA2ByI,MAAM,CAAC,CAAC,CAAC,QAAQA,MAAM,CAAC,CAAC,CAAC,QAAQ;wBACzEnK,YAAY,EAAE,MAAM;wBACpBxB,MAAM,EAAE,aAAa2L,MAAM,CAAC,CAAC,CAAC,EAAE;wBAChCjM,YAAY,EAAE,SAAS;wBACvB4B,MAAM,EAAE,SAAS;wBACjBC,UAAU,EAAE;sBACd,CAAE;sBACFvC,YAAY,EAAGoB,CAAC,IAAK;wBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,kBAAkB;wBACpDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAAC2L,SAAS,GAAG,cAAcmB,MAAM,CAAC,CAAC,CAAC,IAAI;sBAC/D,CAAE;sBACF1M,YAAY,EAAGmB,CAAC,IAAK;wBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,eAAe;wBACjDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAAC2L,SAAS,GAAG,MAAM;sBAC1C,CAAE;sBACFnI,OAAO,EAAEA,CAAA,KAAM8B,qBAAqB,CAACkD,YAAY,CAAE;sBAAA7H,QAAA,eAEnDjD,OAAA;wBAAKsC,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,YAAY;0BACxB8C,GAAG,EAAE;wBACP,CAAE;wBAAA1C,QAAA,gBACAjD,OAAA;0BAAKsC,KAAK,EAAE;4BACVsC,KAAK,EAAE,MAAM;4BACbC,MAAM,EAAE,MAAM;4BACd8B,UAAU,EAAEyI,MAAM,CAAC,CAAC,CAAC;4BACrBnK,YAAY,EAAE,KAAK;4BACnBtB,SAAS,EAAE,QAAQ;4BACnBwL,UAAU,EAAE,CAAC;4BACblB,SAAS,EAAE,aAAamB,MAAM,CAAC,CAAC,CAAC;0BACnC;wBAAE;0BAAA/L,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eACLxD,OAAA;0BAAKsC,KAAK,EAAE;4BAAEwL,IAAI,EAAE;0BAAE,CAAE;0BAAA7K,QAAA,gBACtBjD,OAAA;4BAAIsC,KAAK,EAAE;8BACT8L,MAAM,EAAE,cAAc;8BACtBhL,QAAQ,EAAE,UAAU;8BACpBM,UAAU,EAAE,KAAK;8BACjBV,KAAK,EAAEoM,MAAM,CAAC,CAAC,CAAC;8BAChBf,UAAU,EAAE;4BACd,CAAE;4BAAApL,QAAA,EACC6H,YAAY,CAACmB;0BAAK;4BAAA5I,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACjB,CAAC,eACLxD,OAAA;4BAAGsC,KAAK,EAAE;8BACR8L,MAAM,EAAE,cAAc;8BACtBhL,QAAQ,EAAE,QAAQ;8BAClBJ,KAAK,EAAEoM,MAAM,CAAC,CAAC,CAAC;8BAChBf,UAAU,EAAE;4BACd,CAAE;4BAAApL,QAAA,EACC6H,YAAY,CAACa,OAAO,CAACvH,MAAM,GAAG,EAAE,GAC7B,GAAG0G,YAAY,CAACa,OAAO,CAAC8D,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,GAC7C3E,YAAY,CAACa;0BAAO;4BAAAtI,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvB,CAAC,eACJxD,OAAA;4BAAKsC,KAAK,EAAE;8BACVM,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpB8C,GAAG,EAAE,QAAQ;8BACbvC,QAAQ,EAAE,SAAS;8BACnBJ,KAAK,EAAEoM,MAAM,CAAC,CAAC;4BACjB,CAAE;4BAAAnM,QAAA,gBACAjD,OAAA,CAACjB,QAAQ;8BAACwP,IAAI,EAAE;4BAAG;8BAAAlL,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,eACtBxD,OAAA;8BAAAiD,QAAA,EAAO,IAAIyJ,IAAI,CAAC5B,YAAY,CAACyC,UAAU,CAAC,CAACmC,kBAAkB,CAAC;4BAAC;8BAAArM,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAO,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAClE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC,GAjEDsH,YAAY,CAACG,eAAe;sBAAA5H,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAkE9B,CAAC;kBAEV,CAAC,CAAC,EAGDmF,mBAAmB,CAACvE,MAAM,GAAG,CAAC,iBAC7BpE,OAAA;oBAAQsC,KAAK,EAAE;sBACbsC,KAAK,EAAE,MAAM;sBACbsJ,OAAO,EAAE,SAAS;sBAClBzK,MAAM,EAAE,mBAAmB;sBAC3BwB,YAAY,EAAE,MAAM;sBACpB0B,UAAU,EAAE,mDAAmD;sBAC/D3D,KAAK,EAAE,SAAS;sBAChBI,QAAQ,EAAE,UAAU;sBACpBM,UAAU,EAAE,KAAK;sBACjBqB,MAAM,EAAE,SAAS;sBACjBC,UAAU,EAAE;oBACd,CAAE;oBACFvC,YAAY,EAAGoB,CAAC,IAAK;sBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACqE,UAAU,GAAG,mDAAmD;sBACtF9C,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwM,WAAW,GAAG,SAAS;sBAC7CjL,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,kBAAkB;oBACtD,CAAE;oBACFnD,YAAY,EAAGmB,CAAC,IAAK;sBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACqE,UAAU,GAAG,mDAAmD;sBACtF9C,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACwM,WAAW,GAAG,SAAS;sBAC7CjL,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,eAAe;oBACnD,CAAE;oBAAA5C,QAAA,GAAC,wBACW,EAAC0F,mBAAmB,CAACvE,MAAM,EAAC,oBAC1C;kBAAA;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CACT;gBAAA,eACD,CAAC,gBAEHxD,OAAA;kBAAKsC,KAAK,EAAE;oBACV4L,OAAO,EAAE,WAAW;oBACpBhL,SAAS,EAAE,QAAQ;oBACnBF,KAAK,EAAE;kBACT,CAAE;kBAAAC,QAAA,gBACAjD,OAAA,CAAClB,GAAG;oBAACyP,IAAI,EAAE,EAAG;oBAACjM,KAAK,EAAE;sBAAEa,YAAY,EAAE,QAAQ;sBAAEwM,OAAO,EAAE;oBAAI;kBAAE;oBAAAtM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClExD,OAAA;oBAAGsC,KAAK,EAAE;sBAAE8L,MAAM,EAAE,CAAC;sBAAEhL,QAAQ,EAAE;oBAAW,CAAE;oBAAAH,QAAA,EAAC;kBAE/C;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNxD,OAAA;YAAKsC,KAAK,EAAE;cAAEwL,IAAI,EAAE,CAAC;cAAEK,QAAQ,EAAE;YAAE,CAAE;YAAAlL,QAAA,GAEpC,CAACzC,OAAO,IAAI6H,eAAe,kBAC1BrI,OAAA;cAAKsC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfE,cAAc,EAAE,QAAQ;gBACxBD,UAAU,EAAE,QAAQ;gBACpBqL,OAAO,EAAE;cACX,CAAE;cAAAjL,QAAA,eACAjD,OAAA;gBAAKsC,KAAK,EAAE;kBACVsC,KAAK,EAAE,QAAQ;kBACfC,MAAM,EAAE,QAAQ;kBAChBpB,MAAM,EAAE,mBAAmB;kBAC3BmM,SAAS,EAAE,mBAAmB;kBAC9B3K,YAAY,EAAE,KAAK;kBACnB4K,SAAS,EAAE;gBACb;cAAE;gBAAAxM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN,EAGA,CAAC9C,KAAK,IAAI6H,aAAa,kBACtBvI,OAAA;cAAKsC,KAAK,EAAE;gBACVqE,UAAU,EAAE,wBAAwB;gBACpClD,MAAM,EAAE,kCAAkC;gBAC1CwB,YAAY,EAAE,MAAM;gBACpBiJ,OAAO,EAAE,MAAM;gBACf/K,YAAY,EAAE,QAAQ;gBACtBH,KAAK,EAAE;cACT,CAAE;cAAAC,QAAA,GACCvC,KAAK,iBAAIV,OAAA;gBAAAiD,QAAA,GAAK,iBAAe,EAACvC,KAAK;cAAA;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,EAC1C+E,aAAa,iBAAIvI,OAAA;gBAAAiD,QAAA,GAAK,YAAU,EAACsF,aAAa;cAAA;gBAAAlF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD,CACN,EAGA,CAAChD,OAAO,IAAI,CAAC6H,eAAe,IAAI4E,oBAAoB,CAAC7I,MAAM,KAAK,CAAC,IAAI8I,aAAa,CAAC9I,MAAM,KAAK,CAAC,iBAC9FpE,OAAA;cAAKsC,KAAK,EAAE;gBACVqE,UAAU,EAAE,0BAA0B;gBACtC1B,YAAY,EAAE,MAAM;gBACpBiJ,OAAO,EAAE,MAAM;gBACfhL,SAAS,EAAE,QAAQ;gBACnBO,MAAM,EAAE,8BAA8B;gBACtCqM,cAAc,EAAE;cAClB,CAAE;cAAA7M,QAAA,gBACAjD,OAAA;gBAAKsC,KAAK,EAAE;kBACVa,YAAY,EAAE;gBAChB,CAAE;gBAAAF,QAAA,eACAjD,OAAA,CAACd,MAAM;kBAACqP,IAAI,EAAE,EAAG;kBAACvL,KAAK,EAAC;gBAAS;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC,eACNxD,OAAA;gBAAIsC,KAAK,EAAE;kBACTU,KAAK,EAAE,SAAS;kBAChBI,QAAQ,EAAE,SAAS;kBACnBM,UAAU,EAAE,KAAK;kBACjB0K,MAAM,EAAE;gBACV,CAAE;gBAAAnL,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACLxD,OAAA;gBAAGsC,KAAK,EAAE;kBACRU,KAAK,EAAE,SAAS;kBAChBoL,MAAM,EAAE;gBACV,CAAE;gBAAAnL,QAAA,EACCkE,UAAU,IAAIJ,cAAc,IAAIE,gBAAgB,GAC7C,iDAAiD,GACjD;cAAmC;gBAAA5D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CACN,EAGA,CAAChD,OAAO,IAAI,CAAC6H,eAAe,KAAK4E,oBAAoB,CAAC7I,MAAM,GAAG,CAAC,IAAI8I,aAAa,CAAC9I,MAAM,GAAG,CAAC,CAAC,iBAC5FpE,OAAA;cAAKsC,KAAK,EAAE;gBACVgM,QAAQ,EAAE,QAAQ;gBAClBF,MAAM,EAAE,QAAQ;gBAChBxL,OAAO,EAAE,MAAM;gBACfmD,aAAa,EAAE,QAAQ;gBACvBJ,GAAG,EAAE;cACP,CAAE;cAAA1C,QAAA,GAECiK,aAAa,CAAC9I,MAAM,GAAG,CAAC,iBACvBpE,OAAA,CAAAE,SAAA;gBAAA+C,QAAA,EACGiK,aAAa,CAAClH,GAAG,CAACsE,KAAK,iBACtBtK,OAAA;kBAEEsC,KAAK,EAAE;oBACLqE,UAAU,EAAE,2BAA2B;oBACvC1B,YAAY,EAAE,MAAM;oBACpBiJ,OAAO,EAAE,QAAQ;oBACjBzK,MAAM,EAAE,8BAA8B;oBACtCqM,cAAc,EAAE,YAAY;oBAC5B7B,SAAS,EAAE,gCAAgC;oBAC3CjJ,UAAU,EAAE;kBACd,CAAE;kBACFvC,YAAY,EAAGoB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,kBAAkB;oBACpDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAAC2L,SAAS,GAAG,gCAAgC;kBACpE,CAAE;kBACFvL,YAAY,EAAGmB,CAAC,IAAK;oBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,eAAe;oBACjDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAAC2L,SAAS,GAAG,gCAAgC;kBACpE,CAAE;kBAAAhL,QAAA,gBAGFjD,OAAA;oBAAKsC,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB8C,GAAG,EAAE,MAAM;sBACXxC,YAAY,EAAE;oBAChB,CAAE;oBAAAF,QAAA,GACC,CAAC,MAAM;sBACN,MAAM6D,eAAe,GAAG,CAACwD,KAAK,CAACyF,iBAAiB,IAAI,cAAc,EAAER,WAAW,CAAC,CAAC;sBACjF,MAAMS,YAAY,GAAGnJ,mBAAmB,CAACC,eAAe,CAAC;sBACzD,MAAMmJ,aAAa,GAAGD,YAAY,CAACpJ,IAAI;sBAEvC,oBACE5G,OAAA;wBAAKsC,KAAK,EAAE;0BACVsC,KAAK,EAAE,MAAM;0BACbC,MAAM,EAAE,MAAM;0BACdI,YAAY,EAAE,MAAM;0BACpB0B,UAAU,EAAEqJ,YAAY,CAACrJ,UAAU;0BACnC/D,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpBC,cAAc,EAAE;wBAClB,CAAE;wBAAAG,QAAA,eACAjD,OAAA,CAACiQ,aAAa;0BAAC1B,IAAI,EAAE,EAAG;0BAACvL,KAAK,EAAC;wBAAO;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtC,CAAC;oBAEV,CAAC,EAAE,CAAC,eACJxD,OAAA;sBAAKsC,KAAK,EAAE;wBAAEwL,IAAI,EAAE;sBAAE,CAAE;sBAAA7K,QAAA,gBACtBjD,OAAA;wBAAKsC,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE,QAAQ;0BACbxC,YAAY,EAAE;wBAChB,CAAE;wBAAAF,QAAA,EACC,CAAC,MAAM;0BACN,MAAM6D,eAAe,GAAG,CAACwD,KAAK,CAACyF,iBAAiB,IAAI,cAAc,EAAER,WAAW,CAAC,CAAC;0BACjF,MAAMS,YAAY,GAAGnJ,mBAAmB,CAACC,eAAe,CAAC;0BACzD,MAAMmJ,aAAa,GAAGD,YAAY,CAACpJ,IAAI;0BAEvC,oBACE5G,OAAA;4BAAMsC,KAAK,EAAE;8BACXqE,UAAU,EAAEqJ,YAAY,CAACrJ,UAAU;8BACnC3D,KAAK,EAAE,OAAO;8BACdkL,OAAO,EAAE,iBAAiB;8BAC1BjJ,YAAY,EAAE,MAAM;8BACpB7B,QAAQ,EAAE,SAAS;8BACnBM,UAAU,EAAE,KAAK;8BACjBd,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpB8C,GAAG,EAAE;4BACP,CAAE;4BAAA1C,QAAA,gBACAjD,OAAA,CAACiQ,aAAa;8BAAC1B,IAAI,EAAE,EAAG;8BAACvL,KAAK,EAAC;4BAAO;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,EACxCsD,eAAe;0BAAA;4BAAAzD,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACZ,CAAC;wBAEX,CAAC,EAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACD,CAAC,eACNxD,OAAA;wBAAKsC,KAAK,EAAE;0BACVU,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE;wBACZ,CAAE;wBAAAH,QAAA,EACC,IAAIyJ,IAAI,CAACpC,KAAK,CAACuC,UAAU,CAAC,CAAC6C,kBAAkB,CAAC,OAAO,EAAE;0BACtDQ,OAAO,EAAE,MAAM;0BACfC,IAAI,EAAE,SAAS;0BACfC,KAAK,EAAE,MAAM;0BACbC,GAAG,EAAE;wBACP,CAAC;sBAAC;wBAAAhN,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNxD,OAAA;oBAAIsC,KAAK,EAAE;sBACT8L,MAAM,EAAE,eAAe;sBACvBpL,KAAK,EAAE,SAAS;sBAChBI,QAAQ,EAAE,SAAS;sBACnBM,UAAU,EAAE,KAAK;sBACjB2K,UAAU,EAAE;oBACd,CAAE;oBAAApL,QAAA,EACCqH,KAAK,CAAC2B;kBAAK;oBAAA5I,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,EAEJ8G,KAAK,CAACkC,WAAW,iBAChBxM,OAAA;oBAAGsC,KAAK,EAAE;sBACR8L,MAAM,EAAE,YAAY;sBACpBpL,KAAK,EAAE,SAAS;sBAChBI,QAAQ,EAAE,SAAS;sBACnBiL,UAAU,EAAE;oBACd,CAAE;oBAAApL,QAAA,EACCqH,KAAK,CAACkC;kBAAW;oBAAAnJ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB,CACJ,EAGA,CAAC,MAAM;oBACN;oBACA,MAAM8M,cAAwB,GAAG,EAAE;oBAEnC,IAAKhG,KAAK,CAAStG,MAAM,IAAKsG,KAAK,CAAStG,MAAM,CAACI,MAAM,GAAG,CAAC,EAAE;sBAC5DkG,KAAK,CAAStG,MAAM,CAACuM,OAAO,CAAEC,GAAQ,IAAK;wBAC1C,IAAIA,GAAG,CAACC,SAAS,EAAE;0BACjB;0BACA,MAAMnQ,QAAQ,GAAG5B,WAAW,CAAC8R,GAAG,CAACC,SAAS,CAAC;0BAC3C,IAAInQ,QAAQ,EAAE;4BACZgQ,cAAc,CAACI,IAAI,CAACpQ,QAAQ,CAAC;0BAC/B;wBACF;sBACF,CAAC,CAAC;oBACJ;oBAEA,OAAOgQ,cAAc,CAAClM,MAAM,GAAG,CAAC,gBAC9BpE,OAAA;sBAAKsC,KAAK,EAAE;wBAAEa,YAAY,EAAE;sBAAO,CAAE;sBAAAF,QAAA,eACnCjD,OAAA,CAAC+D,oBAAoB;wBACnBC,MAAM,EAAEsM,cAAc,CAACzG,MAAM,CAAC8G,OAAO,CAAc;wBACnD1M,SAAS,EAAEqG,KAAK,CAAC2B,KAAM;wBACvB/H,UAAU,EAAE,CAAE;wBACdC,YAAY,EAAGM,KAAK,IAAK;0BACvB,MAAMmM,cAAc,GAAGN,cAAc,CAACzG,MAAM,CAAC8G,OAAO,CAAa;0BACjExH,YAAY,CAACyH,cAAc,EAAEnM,KAAK,CAAC;wBACrC;sBAAE;wBAAApB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC,GACJ,IAAI;kBACV,CAAC,EAAE,CAAC,eAGJxD,OAAA;oBAAKsC,KAAK,EAAE;sBACVM,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpBC,cAAc,EAAE,eAAe;sBAC/B+N,UAAU,EAAE,MAAM;sBAClBjB,SAAS,EAAE;oBACb,CAAE;oBAAA3M,QAAA,eACAjD,OAAA;sBAAKsC,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB8C,GAAG,EAAE,MAAM;wBACX3C,KAAK,EAAE,SAAS;wBAChBI,QAAQ,EAAE;sBACZ,CAAE;sBAAAH,QAAA,gBACAjD,OAAA;wBAAMsC,KAAK,EAAE;0BAAEM,OAAO,EAAE,MAAM;0BAAEC,UAAU,EAAE,QAAQ;0BAAE8C,GAAG,EAAE;wBAAU,CAAE;wBAAA1C,QAAA,gBACrEjD,OAAA,CAACb,MAAM;0BAACoP,IAAI,EAAE,EAAG;0BAACvL,KAAK,EAAC;wBAAS;0BAAAK,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,gBAEtC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC,EACN8G,KAAK,CAACwG,QAAQ,IAAIxG,KAAK,CAACwG,QAAQ,KAAKxG,KAAK,CAACuC,UAAU,iBACpD7M,OAAA;wBAAAiD,QAAA,GAAM,QACE,EAAC,IAAIyJ,IAAI,CAACpC,KAAK,CAACwG,QAAQ,CAAC,CAACpB,kBAAkB,CAAC,CAAC;sBAAA;wBAAArM,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAChD,CACP;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA,GAxKD,SAAS8G,KAAK,CAACE,WAAW,EAAE;kBAAAnH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAyK9B,CACN;cAAC,gBACF,CACH,EAGAyJ,oBAAoB,CAAC7I,MAAM,GAAG,CAAC,iBAC9BpE,OAAA,CAAAE,SAAA;gBAAA+C,QAAA,EACGgK,oBAAoB,CAACjH,GAAG,CAAC8E,YAAY;kBAAA,IAAAiG,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;kBAAA,oBACpClR,OAAA;oBAEEsC,KAAK,EAAE;sBACLqE,UAAU,EAAE,2BAA2B;sBACvC1B,YAAY,EAAE,MAAM;sBACpBiJ,OAAO,EAAE,QAAQ;sBACjBzK,MAAM,EAAE,8BAA8B;sBACtCqM,cAAc,EAAE,YAAY;sBAC5B7B,SAAS,EAAE,gCAAgC;sBAC3CjJ,UAAU,EAAE;oBACd,CAAE;oBACFvC,YAAY,EAAGoB,CAAC,IAAK;sBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,kBAAkB;sBACpDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAAC2L,SAAS,GAAG,gCAAgC;oBACpE,CAAE;oBACFvL,YAAY,EAAGmB,CAAC,IAAK;sBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACuD,SAAS,GAAG,eAAe;sBACjDhC,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAAC2L,SAAS,GAAG,gCAAgC;oBACpE,CAAE;oBAAAhL,QAAA,gBAGFjD,OAAA;sBAAKsC,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpB8C,GAAG,EAAE,MAAM;wBACXxC,YAAY,EAAE;sBAChB,CAAE;sBAAAF,QAAA,GACC,CAAC,MAAM;wBACN,IAAI6H,YAAY,CAACuE,QAAQ,EAAE;0BACzB,oBACErP,OAAA;4BAAKsC,KAAK,EAAE;8BACVsC,KAAK,EAAE,MAAM;8BACbC,MAAM,EAAE,MAAM;8BACd8B,UAAU,EAAE,mDAAmD;8BAC/D1B,YAAY,EAAE,MAAM;8BACpBrC,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpBC,cAAc,EAAE,QAAQ;8BACxBqM,UAAU,EAAE;4BACd,CAAE;4BAAAlM,QAAA,eACAjD,OAAA,CAACT,aAAa;8BAACgP,IAAI,EAAE,EAAG;8BAACvL,KAAK,EAAC;4BAAO;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtC,CAAC;wBAEV,CAAC,MAAM;0BACL,MAAMiD,YAAY,GAAG,CAACqE,YAAY,CAACwE,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;0BAC5E,MAAM4B,aAAa,GAAG3K,gBAAgB,CAACC,YAAY,CAAC;0BACpD,MAAMwJ,aAAa,GAAGkB,aAAa,CAACvK,IAAI;0BAExC,oBACE5G,OAAA;4BAAKsC,KAAK,EAAE;8BACVsC,KAAK,EAAE,MAAM;8BACbC,MAAM,EAAE,MAAM;8BACdI,YAAY,EAAE,MAAM;8BACpB0B,UAAU,EAAEwK,aAAa,CAACxK,UAAU;8BACpC/D,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpBC,cAAc,EAAE;4BAClB,CAAE;4BAAAG,QAAA,eACAjD,OAAA,CAACiQ,aAAa;8BAAC1B,IAAI,EAAE,EAAG;8BAACvL,KAAK,EAAC;4BAAO;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACtC,CAAC;wBAEV;sBACF,CAAC,EAAE,CAAC,eACJxD,OAAA;wBAAKsC,KAAK,EAAE;0BAAEwL,IAAI,EAAE;wBAAE,CAAE;wBAAA7K,QAAA,gBACtBjD,OAAA;0BAAKsC,KAAK,EAAE;4BACVM,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpB8C,GAAG,EAAE,QAAQ;4BACbxC,YAAY,EAAE;0BAChB,CAAE;0BAAAF,QAAA,GACC,CAAC,MAAM;4BACN,IAAI6H,YAAY,CAACuE,QAAQ,EAAE;8BACzB,oBACErP,OAAA;gCAAMsC,KAAK,EAAE;kCACXqE,UAAU,EAAE,mDAAmD;kCAC/D3D,KAAK,EAAE,OAAO;kCACdI,QAAQ,EAAE,SAAS;kCACnBM,UAAU,EAAE,KAAK;kCACjBwK,OAAO,EAAE,iBAAiB;kCAC1BjJ,YAAY,EAAE,MAAM;kCACpBmM,aAAa,EAAE,WAAW;kCAC1BC,aAAa,EAAE,OAAO;kCACtBzO,OAAO,EAAE,MAAM;kCACfC,UAAU,EAAE,QAAQ;kCACpB8C,GAAG,EAAE;gCACP,CAAE;gCAAA1C,QAAA,gBACAjD,OAAA,CAACT,aAAa;kCAACgP,IAAI,EAAE,EAAG;kCAACvL,KAAK,EAAC;gCAAO;kCAAAK,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,SAE3C;8BAAA;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAM,CAAC;4BAEX,CAAC,MAAM;8BACL,MAAMiD,YAAY,GAAG,CAACqE,YAAY,CAACwE,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;8BAC5E,MAAM4B,aAAa,GAAG3K,gBAAgB,CAACC,YAAY,CAAC;8BACpD,MAAMwJ,aAAa,GAAGkB,aAAa,CAACvK,IAAI;8BAExC,oBACE5G,OAAA;gCAAMsC,KAAK,EAAE;kCACXqE,UAAU,EAAEwK,aAAa,CAACxK,UAAU;kCACpC3D,KAAK,EAAE,OAAO;kCACdkL,OAAO,EAAE,iBAAiB;kCAC1BjJ,YAAY,EAAE,MAAM;kCACpB7B,QAAQ,EAAE,SAAS;kCACnBM,UAAU,EAAE,KAAK;kCACjBd,OAAO,EAAE,MAAM;kCACfC,UAAU,EAAE,QAAQ;kCACpB8C,GAAG,EAAE;gCACP,CAAE;gCAAA1C,QAAA,gBACAjD,OAAA,CAACiQ,aAAa;kCAAC1B,IAAI,EAAE,EAAG;kCAACvL,KAAK,EAAC;gCAAO;kCAAAK,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,EACxCiD,YAAY;8BAAA;gCAAApD,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACT,CAAC;4BAEX;0BACF,CAAC,EAAE,CAAC,EACHsH,YAAY,CAACf,SAAS,KAAK,CAAC,iBAC3B/J,OAAA;4BAAMsC,KAAK,EAAE;8BACXqE,UAAU,EAAE,mDAAmD;8BAC/D3D,KAAK,EAAE,OAAO;8BACdkL,OAAO,EAAE,iBAAiB;8BAC1BjJ,YAAY,EAAE,MAAM;8BACpB7B,QAAQ,EAAE,SAAS;8BACnBM,UAAU,EAAE,KAAK;8BACjBd,OAAO,EAAE,MAAM;8BACfC,UAAU,EAAE,QAAQ;8BACpB8C,GAAG,EAAE;4BACP,CAAE;4BAAA1C,QAAA,gBACAjD,OAAA,CAAClB,GAAG;8BAACyP,IAAI,EAAE,EAAG;8BAACvL,KAAK,EAAC;4BAAO;8BAAAK,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE,CAAC,UAEjC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CACP;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACE,CAAC,eACNxD,OAAA;0BAAKsC,KAAK,EAAE;4BACVU,KAAK,EAAE,SAAS;4BAChBI,QAAQ,EAAE;0BACZ,CAAE;0BAAAH,QAAA,GAAC,KACE,EAAC6H,YAAY,CAACwG,WAAW,EAAC,UAAG,EAAC,IAAI5E,IAAI,CAAC5B,YAAY,CAACyG,YAAY,CAAC,CAAC7B,kBAAkB,CAAC,OAAO,EAAE;4BAC/FQ,OAAO,EAAE,OAAO;4BAChBC,IAAI,EAAE,SAAS;4BACfC,KAAK,EAAE,OAAO;4BACdC,GAAG,EAAE,SAAS;4BACdmB,IAAI,EAAE,SAAS;4BACfC,MAAM,EAAE;0BACV,CAAC,CAAC;wBAAA;0BAAApO,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAGNxD,OAAA;sBAAIsC,KAAK,EAAE;wBACT8L,MAAM,EAAE,eAAe;wBACvBpL,KAAK,EAAE,SAAS;wBAChBI,QAAQ,EAAE,SAAS;wBACnBM,UAAU,EAAE,KAAK;wBACjB2K,UAAU,EAAE;sBACd,CAAE;sBAAApL,QAAA,EACC6H,YAAY,CAACmB;oBAAK;sBAAA5I,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjB,CAAC,EAGJ,CAAC,MAAM;sBACN;sBACA,MAAM4F,SAAmB,GAAG,EAAE;;sBAE9B;sBACA,IAAI0B,YAAY,CAACJ,WAAW,IAAII,YAAY,CAACJ,WAAW,CAACtG,MAAM,GAAG,CAAC,EAAE;wBACnE0G,YAAY,CAACJ,WAAW,CAAC6F,OAAO,CAAEC,GAA2B,IAAK;0BAChE,IAAIA,GAAG,CAACC,SAAS,EAAE;4BACjB;4BACA,MAAM5P,OAAO,GAAGnC,WAAW,CAAC8R,GAAG,CAACC,SAAS,CAAC;4BAC1C,IAAI5P,OAAO,EAAE;8BACXuI,SAAS,CAACsH,IAAI,CAAC7P,OAAO,CAAC;4BACzB;0BACF;wBACF,CAAC,CAAC;sBACJ;;sBAEA;sBACA,IAAIuI,SAAS,CAAChF,MAAM,KAAK,CAAC,KAAK0G,YAAY,CAAC4G,SAAS,IAAI5G,YAAY,CAAC6G,UAAU,CAAC,EAAE;wBACjF,MAAMC,SAAS,GAAGlT,WAAW,CAACoM,YAAY,CAAC4G,SAAS,IAAI5G,YAAY,CAAC6G,UAAU,CAAC;wBAChF,IAAIC,SAAS,EAAE;0BACbxI,SAAS,CAACsH,IAAI,CAACkB,SAAS,CAAC;wBAC3B;sBACF;sBAEA,OAAOxI,SAAS,CAAChF,MAAM,GAAG,CAAC,gBACzBpE,OAAA,CAAC+D,oBAAoB;wBACnBC,MAAM,EAAEoF,SAAS,CAACS,MAAM,CAAC8G,OAAO,CAAc;wBAC9C1M,SAAS,EAAE6G,YAAY,CAACmB,KAAM;wBAC9B/H,UAAU,EAAE,CAAE;wBACdC,YAAY,EAAGM,KAAK,IAAK;0BACvB,MAAMmM,cAAc,GAAGxH,SAAS,CAACS,MAAM,CAAC8G,OAAO,CAAa;0BAC5DxH,YAAY,CAACyH,cAAc,EAAEnM,KAAK,CAAC;wBACrC;sBAAE;wBAAApB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,GACA,IAAI;oBACV,CAAC,EAAE,CAAC,eAEJxD,OAAA;sBAAKsC,KAAK,EAAE;wBACV8L,MAAM,EAAE,cAAc;wBACtBpL,KAAK,EAAE,SAAS;wBAChBI,QAAQ,EAAE,SAAS;wBACnBiL,UAAU,EAAE;sBACd,CAAE;sBACFwD,uBAAuB,EAAE;wBAAEC,MAAM,EAAEhH,YAAY,CAACa;sBAAQ;oBAAE;sBAAAtI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzD,CAAC,eAGFxD,OAAA;sBAAKsC,KAAK,EAAE;wBACVM,OAAO,EAAE,MAAM;wBACfC,UAAU,EAAE,QAAQ;wBACpBC,cAAc,EAAE,eAAe;wBAC/B+N,UAAU,EAAE,MAAM;wBAClBjB,SAAS,EAAE;sBACb,CAAE;sBAAA3M,QAAA,gBACAjD,OAAA;wBAAKsC,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACfC,UAAU,EAAE,QAAQ;0BACpB8C,GAAG,EAAE;wBACP,CAAE;wBAAA1C,QAAA,gBAEAjD,OAAA;0BACE8F,OAAO,EAAEA,CAAA,KAAM+E,gBAAgB,CAACC,YAAY,CAAE;0BAC9CxI,KAAK,EAAE;4BACLqE,UAAU,EAAEmE,YAAY,CAACC,aAAa,GAAG,wBAAwB,GAAG,aAAa;4BACjF/H,KAAK,EAAE8H,YAAY,CAACC,aAAa,GAAG,SAAS,GAAG,SAAS;4BACzDtH,MAAM,EAAE,MAAM;4BACdwB,YAAY,EAAE,KAAK;4BACnBiJ,OAAO,EAAE,aAAa;4BACtB9K,QAAQ,EAAE,UAAU;4BACpBM,UAAU,EAAE,KAAK;4BACjBqB,MAAM,EAAE,SAAS;4BACjBnC,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpB8C,GAAG,EAAE,QAAQ;4BACbX,UAAU,EAAE;0BACd,CAAE;0BACFvC,YAAY,EAAGoB,CAAC,IAAK;4BACnB,IAAI,CAACiH,YAAY,CAACC,aAAa,EAAE;8BAC/BlH,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACqE,UAAU,GAAG,wBAAwB;8BAC3D9C,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;4BACzC;0BACF,CAAE;0BACFN,YAAY,EAAGmB,CAAC,IAAK;4BACnB,IAAI,CAACiH,YAAY,CAACC,aAAa,EAAE;8BAC/BlH,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACqE,UAAU,GAAG,aAAa;8BAChD9C,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;4BACzC;0BACF,CAAE;0BAAAC,QAAA,gBAEFjD,OAAA,CAACf,KAAK;4BACJsP,IAAI,EAAE,EAAG;4BACTvL,KAAK,EAAE8H,YAAY,CAACC,aAAa,GAAG,SAAS,GAAG,SAAU;4BAC1DgH,IAAI,EAAEjH,YAAY,CAACC,aAAa,GAAG,SAAS,GAAG;0BAAO;4BAAA1H,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACvD,CAAC,eACFxD,OAAA;4BAAAiD,QAAA,EAAO6H,YAAY,CAACkH,cAAc,IAAI;0BAAC;4BAAA3O,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACzC,CAAC,EAGRsH,YAAY,CAACmH,cAAc,iBAC1BjS,OAAA;0BACE8F,OAAO,EAAEA,CAAA,KAAMwB,eAAe,CAC5BD,YAAY,KAAKyD,YAAY,CAACG,eAAe,GAAG,IAAI,GAAGH,YAAY,CAACG,eACtE,CAAE;0BACF3I,KAAK,EAAE;4BACLqE,UAAU,EAAEU,YAAY,KAAKyD,YAAY,CAACG,eAAe,GAAG,yBAAyB,GAAG,aAAa;4BACrGjI,KAAK,EAAEqE,YAAY,KAAKyD,YAAY,CAACG,eAAe,GAAG,SAAS,GAAG,SAAS;4BAC5ExH,MAAM,EAAE,MAAM;4BACdwB,YAAY,EAAE,KAAK;4BACnBiJ,OAAO,EAAE,aAAa;4BACtB9K,QAAQ,EAAE,UAAU;4BACpBM,UAAU,EAAE,KAAK;4BACjBqB,MAAM,EAAE,SAAS;4BACjBnC,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpB8C,GAAG,EAAE,QAAQ;4BACbX,UAAU,EAAE;0BACd,CAAE;0BACFvC,YAAY,EAAGoB,CAAC,IAAK;4BACnB,IAAIwD,YAAY,KAAKyD,YAAY,CAACG,eAAe,EAAE;8BACjDpH,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACqE,UAAU,GAAG,yBAAyB;8BAC5D9C,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;4BACzC;0BACF,CAAE;0BACFN,YAAY,EAAGmB,CAAC,IAAK;4BACnB,IAAIwD,YAAY,KAAKyD,YAAY,CAACG,eAAe,EAAE;8BACjDpH,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACqE,UAAU,GAAG,aAAa;8BAChD9C,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;4BACzC;0BACF,CAAE;0BAAAC,QAAA,gBAEFjD,OAAA,CAAChB,aAAa;4BAACuP,IAAI,EAAE,EAAG;4BAACvL,KAAK,EAAC;0BAAS;4BAAAK,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAC3CxD,OAAA;4BAAAiD,QAAA,EAAO6H,YAAY,CAACoH,aAAa,IAAI;0BAAC;4BAAA7O,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAO,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CACT;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACE,CAAC,eAENxD,OAAA;wBAAKsC,KAAK,EAAE;0BACVU,KAAK,EAAE,SAAS;0BAChBI,QAAQ,EAAE;wBACZ,CAAE;wBAAAH,QAAA,GACC6H,YAAY,CAACqH,UAAU,IAAI,CAAC,EAAC,QAChC;sBAAA;wBAAA9O,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAK,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,EAGL6D,YAAY,KAAKyD,YAAY,CAACG,eAAe,IAAIH,YAAY,CAACmH,cAAc,iBAC3EjS,OAAA;sBAAKsC,KAAK,EAAE;wBACVqB,SAAS,EAAE,MAAM;wBACjBkN,UAAU,EAAE,MAAM;wBAClBjB,SAAS,EAAE;sBACb,CAAE;sBAAA3M,QAAA,gBAEAjD,OAAA;wBAAKsC,KAAK,EAAE;0BACVM,OAAO,EAAE,MAAM;0BACf+C,GAAG,EAAE,SAAS;0BACdxC,YAAY,EAAE;wBAChB,CAAE;wBAAAF,QAAA,gBACAjD,OAAA;0BAAKsC,KAAK,EAAE;4BACVsC,KAAK,EAAE,MAAM;4BACbC,MAAM,EAAE,MAAM;4BACdI,YAAY,EAAE,KAAK;4BACnB0B,UAAU,EAAE,mDAAmD;4BAC/D/D,OAAO,EAAE,MAAM;4BACfC,UAAU,EAAE,QAAQ;4BACpBC,cAAc,EAAE,QAAQ;4BACxBE,KAAK,EAAE,OAAO;4BACdU,UAAU,EAAE,KAAK;4BACjBN,QAAQ,EAAE;0BACZ,CAAE;0BAAAH,QAAA,EAAC;wBAEH;0BAAAI,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAK,CAAC,eACNxD,OAAA;0BAAKsC,KAAK,EAAE;4BAAEwL,IAAI,EAAE;0BAAE,CAAE;0BAAA7K,QAAA,gBACtBjD,OAAA;4BACEyO,KAAK,EAAElH,UAAU,CAACuD,YAAY,CAACG,eAAe,CAAC,IAAI,EAAG;4BACtDyD,QAAQ,EAAG7K,CAAC,IAAK2D,aAAa,CAACsE,IAAI,KAAK;8BACtC,GAAGA,IAAI;8BACP,CAAChB,YAAY,CAACG,eAAe,GAAGpH,CAAC,CAAC8K,MAAM,CAACF;4BAC3C,CAAC,CAAC,CAAE;4BACJD,WAAW,EAAC,oBAAoB;4BAChClM,KAAK,EAAE;8BACLsC,KAAK,EAAE,MAAM;8BACbgJ,SAAS,EAAE,MAAM;8BACjBM,OAAO,EAAE,SAAS;8BAClBzK,MAAM,EAAE,mBAAmB;8BAC3BwB,YAAY,EAAE,MAAM;8BACpB7B,QAAQ,EAAE,QAAQ;8BAClBwL,OAAO,EAAE,MAAM;8BACfwD,MAAM,EAAE,UAAU;8BAClBC,UAAU,EAAE;4BACd;0BAAE;4BAAAhP,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH,CAAC,eACFxD,OAAA;4BAAKsC,KAAK,EAAE;8BACVM,OAAO,EAAE,MAAM;8BACfE,cAAc,EAAE,UAAU;8BAC1Ba,SAAS,EAAE;4BACb,CAAE;4BAAAV,QAAA,eACAjD,OAAA;8BACE8F,OAAO,EAAEA,CAAA,KAAMyF,mBAAmB,CAChCT,YAAY,CAACG,eAAe,EAC5B1D,UAAU,CAACuD,YAAY,CAACG,eAAe,CAAC,IAAI,EAC9C,CAAE;8BACFqH,QAAQ,EAAE,GAAAvB,qBAAA,GAACxJ,UAAU,CAACuD,YAAY,CAACG,eAAe,CAAC,cAAA8F,qBAAA,eAAxCA,qBAAA,CAA0CrF,IAAI,CAAC,CAAC,KAAIjE,iBAAiB,KAAKqD,YAAY,CAACG,eAAgB;8BAClH3I,KAAK,EAAE;gCACLqE,UAAU,EAAE,CAAAqK,sBAAA,GAAAzJ,UAAU,CAACuD,YAAY,CAACG,eAAe,CAAC,cAAA+F,sBAAA,eAAxCA,sBAAA,CAA0CtF,IAAI,CAAC,CAAC,GACxD,mDAAmD,GACnD,SAAS;gCACb1I,KAAK,EAAE,CAAAiO,sBAAA,GAAA1J,UAAU,CAACuD,YAAY,CAACG,eAAe,CAAC,cAAAgG,sBAAA,eAAxCA,sBAAA,CAA0CvF,IAAI,CAAC,CAAC,GAAG,OAAO,GAAG,SAAS;gCAC7EjI,MAAM,EAAE,MAAM;gCACdwB,YAAY,EAAE,KAAK;gCACnBiJ,OAAO,EAAE,aAAa;gCACtB9K,QAAQ,EAAE,UAAU;gCACpBM,UAAU,EAAE,KAAK;gCACjBqB,MAAM,EAAE,CAAAmM,sBAAA,GAAA3J,UAAU,CAACuD,YAAY,CAACG,eAAe,CAAC,cAAAiG,sBAAA,eAAxCA,sBAAA,CAA0CxF,IAAI,CAAC,CAAC,GAAG,SAAS,GAAG,aAAa;gCACpF1G,UAAU,EAAE;8BACd,CAAE;8BAAA/B,QAAA,EAEDwE,iBAAiB,KAAKqD,YAAY,CAACG,eAAe,GAAG,YAAY,GAAG;4BAAc;8BAAA5H,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC7E;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACN,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGNxD,OAAA,CAACxB,cAAc;wBACbgN,cAAc,EAAEV,YAAY,CAACG,eAAgB;wBAC7CsH,aAAa,EAAEzH,YAAY,CAACmH,cAAe;wBAC3CO,eAAe,EAAC;sBAAS;wBAAAnP,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CACN;kBAAA,GAnYI,gBAAgBsH,YAAY,CAACG,eAAe,EAAE;oBAAA5H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAoYhD,CAAC;gBAAA,CACP;cAAC,gBACF,CACH;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGLmE,kBAAkB,iBACjB3H,OAAA;QAAKsC,KAAK,EAAE;UACV6C,QAAQ,EAAE,OAAO;UACjBI,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACT3C,eAAe,EAAE,oBAAoB;UACrCH,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBkL,MAAM,EAAE,IAAI;UACZE,OAAO,EAAE;QACX,CAAE;QACFpI,OAAO,EAAEA,CAAA,KAAM8B,qBAAqB,CAAC,IAAI,CAAE;QAAA3E,QAAA,eAEzCjD,OAAA;UAAKsC,KAAK,EAAE;YACVS,eAAe,EAAE,OAAO;YACxBkC,YAAY,EAAE,MAAM;YACpBqJ,QAAQ,EAAE,OAAO;YACjB1J,KAAK,EAAE,MAAM;YACb6N,SAAS,EAAE,MAAM;YACjBrN,QAAQ,EAAE,MAAM;YAChB6I,SAAS,EAAE;UACb,CAAE;UACFnI,OAAO,EAAGjC,CAAC,IAAKA,CAAC,CAAC6O,eAAe,CAAC,CAAE;UAAAzP,QAAA,gBAGlCjD,OAAA;YAAKsC,KAAK,EAAE;cACV4L,OAAO,EAAE,QAAQ;cACjBH,YAAY,EAAE,mBAAmB;cACjCnL,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE;YAClB,CAAE;YAAAG,QAAA,gBACAjD,OAAA;cAAKsC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB8C,GAAG,EAAE;cACP,CAAE;cAAA1C,QAAA,gBACAjD,OAAA,CAAClB,GAAG;gBAACyP,IAAI,EAAE,EAAG;gBAACjM,KAAK,EAAE;kBAAEU,KAAK,EAAE;gBAAU;cAAE;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9CxD,OAAA;gBAAIsC,KAAK,EAAE;kBACT8L,MAAM,EAAE,CAAC;kBACThL,QAAQ,EAAE,SAAS;kBACnBM,UAAU,EAAE,KAAK;kBACjBV,KAAK,EAAE;gBACT,CAAE;gBAAAC,QAAA,EAAC;cAEH;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC,eACNxD,OAAA;cACE8F,OAAO,EAAEA,CAAA,KAAM8B,qBAAqB,CAAC,IAAI,CAAE;cAC3CtF,KAAK,EAAE;gBACLqE,UAAU,EAAE,MAAM;gBAClBlD,MAAM,EAAE,MAAM;gBACdL,QAAQ,EAAE,QAAQ;gBAClBJ,KAAK,EAAE,SAAS;gBAChB+B,MAAM,EAAE,SAAS;gBACjBmJ,OAAO,EAAE,SAAS;gBAClBjJ,YAAY,EAAE,KAAK;gBACnBD,UAAU,EAAE;cACd,CAAE;cACFvC,YAAY,EAAGoB,CAAC,IAAK;gBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;cACzC,CAAE;cACFN,YAAY,EAAGmB,CAAC,IAAK;gBACnBA,CAAC,CAAC+B,aAAa,CAACtD,KAAK,CAACU,KAAK,GAAG,SAAS;cACzC,CAAE;cAAAC,QAAA,EACH;YAED;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGNxD,OAAA;YAAKsC,KAAK,EAAE;cAAE4L,OAAO,EAAE;YAAS,CAAE;YAAAjL,QAAA,gBAChCjD,OAAA;cAAKsC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB8C,GAAG,EAAE,SAAS;gBACdxC,YAAY,EAAE;cAChB,CAAE;cAAAF,QAAA,GACC,CAAC,MAAM;gBACN,IAAI0E,kBAAkB,CAAC0H,QAAQ,EAAE;kBAC/B,oBACErP,OAAA;oBAAMsC,KAAK,EAAE;sBACXqE,UAAU,EAAE,mDAAmD;sBAC/D3D,KAAK,EAAE,OAAO;sBACdI,QAAQ,EAAE,SAAS;sBACnBM,UAAU,EAAE,KAAK;sBACjBwK,OAAO,EAAE,iBAAiB;sBAC1BjJ,YAAY,EAAE,MAAM;sBACpBmM,aAAa,EAAE,WAAW;sBAC1BC,aAAa,EAAE,OAAO;sBACtBzO,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB8C,GAAG,EAAE;oBACP,CAAE;oBAAA1C,QAAA,gBACAjD,OAAA,CAACT,aAAa;sBAACgP,IAAI,EAAE,EAAG;sBAACvL,KAAK,EAAC;oBAAO;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,SAE3C;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAEX,CAAC,MAAM;kBACL,MAAMiD,YAAY,GAAG,CAACkB,kBAAkB,CAAC2H,aAAa,IAAI,SAAS,EAAEC,WAAW,CAAC,CAAC;kBAClF,MAAM4B,aAAa,GAAG3K,gBAAgB,CAACC,YAAY,CAAC;kBACpD,MAAMwJ,aAAa,GAAGkB,aAAa,CAACvK,IAAI;kBAExC,oBACE5G,OAAA;oBAAMsC,KAAK,EAAE;sBACXqE,UAAU,EAAEwK,aAAa,CAACxK,UAAU;sBACpC3D,KAAK,EAAE,OAAO;sBACdI,QAAQ,EAAE,SAAS;sBACnBM,UAAU,EAAE,KAAK;sBACjBwK,OAAO,EAAE,iBAAiB;sBAC1BjJ,YAAY,EAAE,MAAM;sBACpBmM,aAAa,EAAE,WAAW;sBAC1BC,aAAa,EAAE,OAAO;sBACtBzO,OAAO,EAAE,MAAM;sBACfC,UAAU,EAAE,QAAQ;sBACpB8C,GAAG,EAAE;oBACP,CAAE;oBAAA1C,QAAA,gBACAjD,OAAA,CAACiQ,aAAa;sBAAC1B,IAAI,EAAE,EAAG;sBAACvL,KAAK,EAAC;oBAAO;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,EACxCiD,YAAY;kBAAA;oBAAApD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACT,CAAC;gBAEX;cACF,CAAC,EAAE,CAAC,eAEJxD,OAAA;gBAAMsC,KAAK,EAAE;kBACXqE,UAAU,EAAE,mDAAmD;kBAC/D3D,KAAK,EAAE,OAAO;kBACdkL,OAAO,EAAE,iBAAiB;kBAC1BjJ,YAAY,EAAE,MAAM;kBACpB7B,QAAQ,EAAE,SAAS;kBACnBM,UAAU,EAAE,KAAK;kBACjBd,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpB8C,GAAG,EAAE;gBACP,CAAE;gBAAA1C,QAAA,gBACAjD,OAAA,CAAClB,GAAG;kBAACyP,IAAI,EAAE;gBAAG;kBAAAlL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,UAEnB;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENxD,OAAA;cAAIsC,KAAK,EAAE;gBACT8L,MAAM,EAAE,YAAY;gBACpBhL,QAAQ,EAAE,QAAQ;gBAClBM,UAAU,EAAE,KAAK;gBACjBV,KAAK,EAAE,SAAS;gBAChBqL,UAAU,EAAE;cACd,CAAE;cAAApL,QAAA,EACC0E,kBAAkB,CAACsE;YAAK;cAAA5I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eAELxD,OAAA;cAAKsC,KAAK,EAAE;gBACVU,KAAK,EAAE,SAAS;gBAChBI,QAAQ,EAAE,MAAM;gBAChBiL,UAAU,EAAE,KAAK;gBACjBlL,YAAY,EAAE;cAChB,CAAE;cAAAF,QAAA,EACC0E,kBAAkB,CAACgE;YAAO;cAAAtI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,EAGLmE,kBAAkB,CAAC+C,WAAW,IAAI/C,kBAAkB,CAAC+C,WAAW,CAACtG,MAAM,GAAG,CAAC,iBAC1EpE,OAAA;cAAKsC,KAAK,EAAE;gBAAEa,YAAY,EAAE;cAAS,CAAE;cAAAF,QAAA,eACrCjD,OAAA,CAAC+D,oBAAoB;gBACnBC,MAAM,EAAE2D,kBAAkB,CAAC+C,WAAW,CAAC1E,GAAG,CAAEwK,GAAQ,IAAK9R,WAAW,CAAC8R,GAAG,CAACC,SAAS,CAAC,CAAC,CAAC5G,MAAM,CAAC8G,OAAO,CAAE;gBACrG1M,SAAS,EAAE0D,kBAAkB,CAACsE,KAAM;gBACpC9H,YAAY,EAAGM,KAAK,IAAK;kBACvB,MAAM2E,SAAS,GAAGzB,kBAAkB,CAAC+C,WAAW,CAAC1E,GAAG,CAAEwK,GAAQ,IAAK9R,WAAW,CAAC8R,GAAG,CAACC,SAAS,CAAC,CAAC,CAAC5G,MAAM,CAAC8G,OAAO,CAAC;kBAC9GxH,YAAY,CAACC,SAAS,EAAE3E,KAAK,CAAC;gBAChC;cAAE;gBAAApB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,eAEDxD,OAAA;cAAKsC,KAAK,EAAE;gBACVM,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpB8C,GAAG,EAAE,MAAM;gBACXvC,QAAQ,EAAE,UAAU;gBACpBJ,KAAK,EAAE,SAAS;gBAChB6N,UAAU,EAAE,MAAM;gBAClBjB,SAAS,EAAE;cACb,CAAE;cAAA3M,QAAA,gBACAjD,OAAA;gBAAKsC,KAAK,EAAE;kBACVM,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpB8C,GAAG,EAAE;gBACP,CAAE;gBAAA1C,QAAA,gBACAjD,OAAA,CAACjB,QAAQ;kBAACwP,IAAI,EAAE;gBAAG;kBAAAlL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACtBxD,OAAA;kBAAAiD,QAAA,GAAM,aAAW,EAAC,IAAIyJ,IAAI,CAAC/E,kBAAkB,CAAC4F,UAAU,CAAC,CAACmC,kBAAkB,CAAC,CAAC;gBAAA;kBAAArM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnF,CAAC,EACLmE,kBAAkB,CAAC2J,WAAW,iBAC7BtR,OAAA;gBAAAiD,QAAA,GAAK,MACC,EAAC0E,kBAAkB,CAAC2J,WAAW;cAAA;gBAAAjO,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGDxD,OAAA,CAACvB,aAAa;QACZuF,MAAM,EAAE+D,cAAe;QACvBsB,YAAY,EAAEpB,oBAAqB;QACnC0K,MAAM,EAAE9K,YAAa;QACrB+K,OAAO,EAAEA,CAAA,KAAM9K,eAAe,CAAC,KAAK,CAAE;QACtC7D,SAAS,EAAC;MAAO;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAAA,eACJ,CAAC;AAEP,CAAC;AAAC8C,GAAA,CAhnDID,eAAyB;EAAA,QACZhI,WAAW,EA2FLC,aAAa;AAAA;AAAAuU,GAAA,GA5FhCxM,eAAyB;AAknD/B,eAAeA,eAAe;AAAC,IAAAvC,EAAA,EAAAsC,GAAA,EAAAyM,GAAA;AAAAC,YAAA,CAAAhP,EAAA;AAAAgP,YAAA,CAAA1M,GAAA;AAAA0M,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}