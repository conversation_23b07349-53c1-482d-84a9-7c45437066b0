{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M22 18a2 2 0 0 1-2 2H3c-1.1 0-1.3-.6-.4-1.3L20.4 4.3c.9-.7 1.6-.4 1.6.7Z\",\n  key: \"183wce\"\n}]];\nconst TriangleRight = createLucideIcon(\"triangle-right\", __iconNode);\nexport { __iconNode, TriangleRight as default };\n//# sourceMappingURL=triangle-right.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}