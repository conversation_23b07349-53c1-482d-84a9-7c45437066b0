{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"line\", {\n  x1: \"22\",\n  x2: \"2\",\n  y1: \"6\",\n  y2: \"6\",\n  key: \"15w7dq\"\n}], [\"line\", {\n  x1: \"22\",\n  x2: \"2\",\n  y1: \"18\",\n  y2: \"18\",\n  key: \"1ip48p\"\n}], [\"line\", {\n  x1: \"6\",\n  x2: \"6\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"a2lnyx\"\n}], [\"line\", {\n  x1: \"18\",\n  x2: \"18\",\n  y1: \"2\",\n  y2: \"22\",\n  key: \"8vb6jd\"\n}]];\nconst Frame = createLucideIcon(\"frame\", __iconNode);\nexport { __iconNode, Frame as default };\n//# sourceMappingURL=frame.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}