{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 4v16\",\n  key: \"1654pz\"\n}], [\"path\", {\n  d: \"M4 7V5a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2\",\n  key: \"e0r10z\"\n}], [\"path\", {\n  d: \"M9 20h6\",\n  key: \"s66wpe\"\n}]];\nconst Type = createLucideIcon(\"type\", __iconNode);\nexport { __iconNode, Type as default };\n//# sourceMappingURL=type.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}