{"ast": null, "code": "/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"m4 4 2.5 2.5\",\n  key: \"uv2vmf\"\n}], [\"path\", {\n  d: \"M13.5 6.5a4.95 4.95 0 0 0-7 7\",\n  key: \"frdkwv\"\n}], [\"path\", {\n  d: \"M15 5 5 15\",\n  key: \"1ag8rq\"\n}], [\"path\", {\n  d: \"M14 17v.01\",\n  key: \"eokfpp\"\n}], [\"path\", {\n  d: \"M10 16v.01\",\n  key: \"14uyyl\"\n}], [\"path\", {\n  d: \"M13 13v.01\",\n  key: \"1v1k97\"\n}], [\"path\", {\n  d: \"M16 10v.01\",\n  key: \"5169yg\"\n}], [\"path\", {\n  d: \"M11 20v.01\",\n  key: \"cj92p8\"\n}], [\"path\", {\n  d: \"M17 14v.01\",\n  key: \"11cswd\"\n}], [\"path\", {\n  d: \"M20 11v.01\",\n  key: \"19e0od\"\n}]];\nconst ShowerHead = createLucideIcon(\"shower-head\", __iconNode);\nexport { __iconNode, ShowerHead as default };\n//# sourceMappingURL=shower-head.js.map", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}